<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>pvc-mes-ear</artifactId>
	<packaging>ear</packaging>
	<name>EAR</name>
	<description>MESwell.ear打包</description>
	
	<parent>
		<groupId>com.glory.mes.pvc</groupId>
		<artifactId>pvc-pom</artifactId>
		<version>8.4-SNAPSHOT</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	
	<dependencies>
		<dependency>
            <groupId>com.glory.framework</groupId>
            <artifactId>activeentitymodel</artifactId>
            <version>${framework.version}</version>
            <classifier>${my.classifier}</classifier>
            <optional>true</optional>
            <type>ejb</type>
        </dependency>
        <dependency>
            <groupId>com.glory.framework</groupId>
            <artifactId>activeentity</artifactId>
            <version>${framework.version}</version>
            <classifier>${my.classifier}</classifier>
            <optional>true</optional>
            <type>ejb</type>
        </dependency>
		<dependency>
			<groupId>com.glory.framework</groupId>
			<artifactId>security</artifactId>
			<version>${framework.version}</version>
            <classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
		</dependency>
        <dependency>
            <groupId>com.glory.framework</groupId>
            <artifactId>securitymodel</artifactId>
            <version>${framework.version}</version>
            <classifier>${my.classifier}</classifier>
            <optional>true</optional>
            <type>ejb</type>
        </dependency>
		<dependency>
			<groupId>com.glory.framework</groupId>
			<artifactId>system</artifactId>
			<version>${framework.version}</version>
            <classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
		</dependency>
		<dependency>
            <groupId>com.glory.framework</groupId>
            <artifactId>tenant</artifactId>
            <version>${framework.version}</version>
            <classifier>${my.classifier}</classifier>
            <optional>true</optional>
            <type>ejb</type>
        </dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>edc</artifactId>
			<version>${mes.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>edcmodel</artifactId>
			<version>${mes.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>mesbase</artifactId>
			<version>${mes.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>mesbasemodel</artifactId>
			<version>${mes.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>mesmsg</artifactId>
			<version>${mes.version}</version>
			<type>ejb</type>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>mesmsgmodel</artifactId>
			<version>${mes.version}</version>
			<type>ejb</type>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>mesmsgsend</artifactId>
			<version>${mes.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>mesmsgservice</artifactId>
			<version>${mes.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>mm</artifactId>
			<version>${mes.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.common</groupId>
					<artifactId>task</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>mmmodel</artifactId>
			<version>${mes.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>ras</artifactId>
			<version>${mes.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.common</groupId>
					<artifactId>task</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>rasmodel</artifactId>
			<version>${mes.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>wip</artifactId>
			<version>${mes.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.common</groupId>
					<artifactId>task</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>wipmodel</artifactId>
			<version>${mes.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
		</dependency>
		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>context</artifactId>
			<version>${context.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentity</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>contextmodel</artifactId>
			<version>${context.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>ecn</artifactId>
			<version>${ecn.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentity</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>ecnmodel</artifactId>
			<version>${ecn.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>excel</artifactId>
			<version>${excel.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentity</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>security</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>securitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>excelmodel</artifactId>
			<version>${excel.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
			</exclusions>			
		</dependency>

		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>datatransfer</artifactId>
			<version>${datatransfer.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentity</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>security</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>securitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>core</artifactId>
				</exclusion>

				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>wipmodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>wip</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>rasmodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>ras</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>mmmodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>mesmsg</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>mesmsgmodel</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		
		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>label</artifactId>
			<version>${label.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>			
			<exclusions>
				<exclusion>
            		<groupId>com.glory.msg</groupId>
					<artifactId>msg</artifactId>
            	</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentity</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>wip</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>wipmodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>mmmodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>mesbase</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>mesmsgmodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>mesmsg</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.common</groupId>
					<artifactId>esb</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.common</groupId>
					<artifactId>context</artifactId>
				</exclusion>
				
				<exclusion>
					<groupId>com.glory.msg</groupId>
					<artifactId>msgmodel</artifactId>
				</exclusion>
            	<exclusion>
					<groupId>com.glory.common</groupId>
					<artifactId>contextmodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>mesbasemodel</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>labelmodel</artifactId>
			<version>${label.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>wipmodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>wip</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>mesbasemodel</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		
		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>state</artifactId>
			<version>${state.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>				
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>security</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>securitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentity</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>	
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentity</artifactId>
				</exclusion>			
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>statemodel</artifactId>
			<version>${state.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentity</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>securitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.glory.msg</groupId>
			<artifactId>msg</artifactId>
			<version>${msg.version}</version>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>securitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentity</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
            <groupId>com.glory.msg</groupId>
            <artifactId>msgmodel</artifactId>
            <version>${msg.version}</version>
            <classifier>${my.classifier}</classifier>
            <optional>true</optional>
            <type>ejb</type>
        </dependency>
		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>print</artifactId>
			<version>${print.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentity</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.mes</groupId>
					<artifactId>wipmodel</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		
		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>printmodel</artifactId>
			<version>${print.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
            <groupId>com.glory.spc</groupId>
            <artifactId>spc</artifactId>
            <version>${spc.version}</version>
            <classifier>${my.classifier}</classifier>
            <optional>true</optional>
            <type>ejb</type>       
        </dependency>
        
        <dependency>
            <groupId>com.glory.spc</groupId>
            <artifactId>spcmodel</artifactId>
            <version>${spc.version}</version>
            <classifier>${my.classifier}</classifier>
            <optional>true</optional>
            <type>ejb</type>
        </dependency>
        
        <dependency>
            <groupId>com.glory.spc</groupId>
            <artifactId>spcmsg</artifactId>
            <version>${spc.version}</version>
            <classifier>${my.classifier}</classifier>
            <optional>true</optional>
            <type>ejb</type>          
        </dependency>

        <dependency>
			<groupId>com.glory.pms</groupId>
			<artifactId>pms</artifactId>
			<version>${pms.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>			
		</dependency>
		
		<dependency>
			<groupId>com.glory.pms</groupId>
			<artifactId>pmsmodel</artifactId>
			<version>${pms.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>		
		</dependency>
		
		<dependency>
			<groupId>com.glory.pms</groupId>
			<artifactId>pmsmsg</artifactId>
			<version>${pms.version}</version>
			<type>ejb</type>		
		</dependency>

		<dependency>
			<groupId>com.glory.pms</groupId>
			<artifactId>parts</artifactId>
			<version>${pms.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>			
		</dependency>
		
		<dependency>
			<groupId>com.glory.pms</groupId>
			<artifactId>repair</artifactId>
			<version>${repair.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>			
		</dependency>

		<dependency>
			<groupId>com.glory.alm</groupId>
			<artifactId>alm</artifactId>
			<version>${alm.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
		</dependency>
		
		<dependency>
			<groupId>com.glory.alm</groupId>
			<artifactId>almmodel</artifactId>
			<version>${alm.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
		</dependency>
		
		<dependency>
			<groupId>com.glory.alm</groupId>
			<artifactId>almmsg</artifactId>
			<version>${alm.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
		</dependency>


 		<dependency>
			<groupId>com.glory.gtm</groupId>
			<artifactId>gtm</artifactId>
			<version>${gtm.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentity</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>security</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>securitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.common</groupId>
					<artifactId>esb</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.msg</groupId>
					<artifactId>msg</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.msg</groupId>
					<artifactId>msgmodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.common</groupId>
					<artifactId>task</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		
 		<dependency>
			<groupId>com.glory.gtm</groupId>
			<artifactId>gtmmodel</artifactId>
			<version>${gtm.version}</version>
			<classifier>${my.classifier}</classifier>
            <optional>true</optional>
			<type>ejb</type>
			<exclusions>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentity</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>security</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>securitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>liquibase</artifactId>
			<version>${mes.version}</version>
			<type>ejb</type>
		</dependency>
		
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>msa</artifactId>
			<version>${msa.version}</version>
			<type>ejb</type>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>msamodel</artifactId>
			<version>${msa.version}</version>
			<type>ejb</type>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>msamsg</artifactId>
			<version>${msa.version}</version>
			<type>ejb</type>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<artifactId>maven-ear-plugin</artifactId>
				<version>2.10.1</version>
				<configuration>
					<outputDirectory>target/dist</outputDirectory>
					<workDirectory>target/dist</workDirectory>
					<defaultLibBundleDir>lib/</defaultLibBundleDir>

					<packagingIncludes>META-INF/**,*.jar</packagingIncludes>
					<earName>MESwell</earName>
					<displayName>meswell</displayName>
					<description>meswell-app</description>
					<modules>
						<ejbModule>
							<groupId>com.glory.framework</groupId>
							<artifactId>activeentity</artifactId>
							<bundleFileName>activeentity.jar</bundleFileName>
							<moduleId>activeentity-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.framework</groupId>
							<artifactId>activeentitymodel</artifactId>
							<bundleFileName>activeentitymodel.jar</bundleFileName>
							<moduleId>activeentity-model</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.framework</groupId>
							<artifactId>security</artifactId>
							<bundleFileName>security.jar</bundleFileName>
							<moduleId>security-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.framework</groupId>
							<artifactId>securitymodel</artifactId>
							<bundleFileName>securitymodel.jar</bundleFileName>
							<moduleId>security-model</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.framework</groupId>
							<artifactId>system</artifactId>
							<bundleFileName>system.jar</bundleFileName>
							<moduleId>system-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.framework</groupId>
							<artifactId>tenant</artifactId>
							<bundleFileName>tenant.jar</bundleFileName>
							<moduleId>tenant-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.common</groupId>
							<artifactId>context</artifactId>
							<bundleFileName>context.jar</bundleFileName>
							<moduleId>context-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.common</groupId>
							<artifactId>contextmodel</artifactId>
							<bundleFileName>contextmodel.jar</bundleFileName>
							<moduleId>context-model</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.common</groupId>
							<artifactId>ecn</artifactId>
							<bundleFileName>ecn.jar</bundleFileName>
							<moduleId>ecn-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.common</groupId>
							<artifactId>ecnmodel</artifactId>
							<bundleFileName>ecnmodel.jar</bundleFileName>
							<moduleId>ecn-model</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.common</groupId>
							<artifactId>excel</artifactId>
							<bundleFileName>excel.jar</bundleFileName>
							<moduleId>excel-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.common</groupId>
							<artifactId>excelmodel</artifactId>
							<bundleFileName>excelmodel.jar</bundleFileName>
							<moduleId>excel-model</moduleId>
						</ejbModule>

						<ejbModule>
							<groupId>com.glory.common</groupId>
							<artifactId>datatransfer</artifactId>
							<bundleFileName>datatransfer.jar</bundleFileName>
							<moduleId>datatransfer-EJB</moduleId>
						</ejbModule>
						
						<ejbModule>
							<groupId>com.glory.common</groupId>
							<artifactId>label</artifactId>
							<bundleFileName>label.jar</bundleFileName>
							<moduleId>label-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.common</groupId>
							<artifactId>labelmodel</artifactId>
							<bundleFileName>labelmodel.jar</bundleFileName>
							<moduleId>label-model</moduleId>
						</ejbModule>
						
						<ejbModule>
							<groupId>com.glory.common</groupId>
							<artifactId>state</artifactId>
							<bundleFileName>state.jar</bundleFileName>
							<moduleId>state-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.common</groupId>
							<artifactId>statemodel</artifactId>
							<bundleFileName>statemodel.jar</bundleFileName>
							<moduleId>state-model</moduleId>
						</ejbModule>
						
						<ejbModule>
							<groupId>com.glory.msg</groupId>
							<artifactId>msg</artifactId>
							<bundleFileName>msg.jar</bundleFileName>
							<moduleId>msg-MSG</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.msg</groupId>
							<artifactId>msgmodel</artifactId>
							<bundleFileName>msgmodel.jar</bundleFileName>
							<moduleId>msg-model</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>edc</artifactId>
							<bundleFileName>edc.jar</bundleFileName>
							<moduleId>edc-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>edcmodel</artifactId>
							<bundleFileName>edcmodel.jar</bundleFileName>
							<moduleId>edc-model</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>mesbase</artifactId>
							<bundleFileName>mesbase.jar</bundleFileName>
							<moduleId>mesbase-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>mesbasemodel</artifactId>
							<bundleFileName>mesbasemodel.jar</bundleFileName>
							<moduleId>mesbase-model</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>mm</artifactId>
							<bundleFileName>mm.jar</bundleFileName>
							<moduleId>mm-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>mmmodel</artifactId>
							<bundleFileName>mmmodel.jar</bundleFileName>
							<moduleId>mm-model</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>ras</artifactId>
							<bundleFileName>ras.jar</bundleFileName>
							<moduleId>ras-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>rasmodel</artifactId>
							<bundleFileName>rasmodel.jar</bundleFileName>
							<moduleId>ras-model</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>wip</artifactId>
							<bundleFileName>wip.jar</bundleFileName>
							<moduleId>wip-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>wipmodel</artifactId>
							<bundleFileName>wipmodel.jar</bundleFileName>
							<moduleId>wip-model</moduleId>
						</ejbModule>
						
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>mesmsg</artifactId>
							<bundleFileName>mesmsg.jar</bundleFileName>
							<moduleId>mes-MSG</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>mesmsgmodel</artifactId>
							<bundleFileName>mesmsgmodel.jar</bundleFileName>
							<moduleId>mesmsg-model</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>mesmsgservice</artifactId>
							<bundleFileName>mesmsgservice.jar</bundleFileName>
							<moduleId>mesmsg-service</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>mesmsgsend</artifactId>
							<bundleFileName>mesmsgsend.jar</bundleFileName>
							<moduleId>mesmsg-send</moduleId>
						</ejbModule>
						
						<ejbModule>
							<groupId>com.glory.common</groupId>
							<artifactId>print</artifactId>
							<bundleFileName>print.jar</bundleFileName>
							<moduleId>print-EJB</moduleId>
						</ejbModule>
						
						<ejbModule>
							<groupId>com.glory.common</groupId>
							<artifactId>printmodel</artifactId>
							<bundleFileName>printmodel.jar</bundleFileName>
							<moduleId>print-model</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.spc</groupId>
							<artifactId>spc</artifactId>
							<bundleFileName>spc.jar</bundleFileName>
							<moduleId>spc-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.spc</groupId>
							<artifactId>spcmodel</artifactId>
							<bundleFileName>spcmodel.jar</bundleFileName>
							<moduleId>spc-model</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.spc</groupId>
							<artifactId>spcmsg</artifactId>
							<bundleFileName>spcmsg.jar</bundleFileName>
							<moduleId>spc-msg</moduleId>
						</ejbModule>


						<ejbModule>
							<groupId>com.glory.alm</groupId>
							<artifactId>alm</artifactId>
							<bundleFileName>alm.jar</bundleFileName>
							<moduleId>alm-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.alm</groupId>
							<artifactId>almmodel</artifactId>
							<bundleFileName>almmodel.jar</bundleFileName>
							<moduleId>alm-model</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.alm</groupId>
							<artifactId>almmsg</artifactId>
							<bundleFileName>almmsg.jar</bundleFileName>
							<moduleId>alm-MSG</moduleId>
						</ejbModule>


						<ejbModule>
							<groupId>com.glory.pms</groupId>
							<artifactId>pms</artifactId>
							<bundleFileName>pms.jar</bundleFileName>
							<moduleId>pms-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.pms</groupId>
							<artifactId>pmsmodel</artifactId>
							<bundleFileName>pmsmodel.jar</bundleFileName>
							<moduleId>pms-model</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.pms</groupId>
							<artifactId>pmsmsg</artifactId>
							<bundleFileName>pmsmsg.jar</bundleFileName>
							<moduleId>pms-MSG</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.pms</groupId>
							<artifactId>parts</artifactId>
							<bundleFileName>parts.jar</bundleFileName>
							<moduleId>parts-EJB</moduleId>
						</ejbModule>
						
						<ejbModule>
							<groupId>com.glory.pms</groupId>
							<artifactId>repair</artifactId>
							<bundleFileName>repair.jar</bundleFileName>
							<moduleId>repair-EJB</moduleId>
						</ejbModule>

 						<ejbModule>
							<groupId>com.glory.gtm</groupId>
							<artifactId>gtm</artifactId>
							<bundleFileName>gtm.jar</bundleFileName>
							<moduleId>gtm-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.gtm</groupId>
							<artifactId>gtmmodel</artifactId>
							<bundleFileName>gtmmodel.jar</bundleFileName>
							<moduleId>gtm-model</moduleId>
						</ejbModule>

						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>liquibase</artifactId>
							<bundleFileName>liquibase.jar</bundleFileName>
							<moduleId>liquibase-EJB</moduleId>
						</ejbModule>
						
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>msa</artifactId>
							<bundleFileName>msa.jar</bundleFileName>
							<moduleId>msa-EJB</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>msamodel</artifactId>
							<bundleFileName>msamodel.jar</bundleFileName>
							<moduleId>msa-model</moduleId>
						</ejbModule>
						<ejbModule>
							<groupId>com.glory.mes</groupId>
							<artifactId>msamsg</artifactId>
							<bundleFileName>msamsg.jar</bundleFileName>
							<moduleId>msa-MSG</moduleId>
						</ejbModule>
					</modules>
				</configuration>
			</plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
		</plugins>
	</build>
</project>