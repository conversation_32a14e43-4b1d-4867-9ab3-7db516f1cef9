<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.glory.mes</groupId>
		<artifactId>mes-pom</artifactId>
		<version>8.4.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>mesbase</artifactId>
	<name>MES Base</name>
	<description>MES基础功能</description>
	<packaging>ejb</packaging>

	<dependencies>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>mesbasemodel</artifactId>
			<version>${mes.version}</version>
		</dependency>
		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>context</artifactId>
			<version>${context.version}</version>
			
			<exclusions>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentity</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>security</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>securitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>contextmodel</artifactId>
			<version>${context.version}</version>
			
			<exclusions>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentity</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>security</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>securitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>excel</artifactId>
			<version>${excel.version}</version>
		</dependency>
	</dependencies>
	
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-ejb-plugin</artifactId>
				<configuration>
					<ejbVersion>3.1</ejbVersion>
					<generateClient>true</generateClient>
					<clientIncludes>
						<clientInclude>com/glory/mes/base/client/**</clientInclude>
						<clientInclude>com/glory/mes/base/model/idgenerator/**</clientInclude>
					</clientIncludes>
                    <classifier>${my.classifier}</classifier>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>