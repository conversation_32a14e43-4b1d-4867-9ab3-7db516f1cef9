package com.glory.mes.base.model.idgenerator;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.Transient;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.exception.MBasExceptionBundle;

/**
 * 生成序列号
 * 从AD_SEQUENCE表中获得下一个序列号
 */
@Entity
@DiscriminatorValue("S")
public class SequenceRuleLine extends GeneratorRuleLine {
	private static final long serialVersionUID = 1L;

	private static final Logger logger = Logger.getLogger(SequenceRuleLine.class);
	
	public static final String SEQUENCETYPE_DIGITS = "1";
	public static final String SEQUENCETYPE_MIX = "2";
	public static final String SEQUENCETYPE_ALPHA = "3";
	public static final String SEQUENCETYPE_SPECIAL1 = "4";

	public static final String BASETYPE_SEQUENCE = "SEQUENCE";
	public static final String BASETYPE_TABLE = "TABLE";
	
	public static final String BASEON_PATTERN = "pattern";
	
	public static final String EXCLUDETYPE_STRING = "All";
	public static final String EXCLUDETYPE_ALPHA = "Include";
	
	//生成策略：F--补缺(Fill)，A--追加(Append)
	public static final String STRATEGY_FILL = "F";
	public static final String STRATEGY_APPEND = "A";

	@Transient
	private List<String> excludeString;
	
	@Transient
	private List<String> excludeAlpha;
	
	/**
	 * 序列号类型
	 * DIGITS: 序列号完全由数字组成(1,2,3,4,...)
	 * MIX   : 序列号由数字和字母混合组成(1,2,3,4,...,A,B,C,D,...)
	 * ALPHA ：序列号完全由字母组成(A,B,C,D,...)
	 * SPECIAL1:个位数是0~9其它位数是0~Z
	 */
	@Column(name="SEQUENCE_TYPE")
	private String sequenceType;
	
	@Column(name="SEQUENCE_DIRECTION")
	private String sequenceDirection;
	
	@Column(name="SIZEE")//SIZE是oracle中的关键字
	private Long sizee;
	
	@Column(name="EXCLUDE")
	private String exclude;
	
	@Column(name="EXCLUDE_TYPE")
	private String excludeType;
	
	@Column(name="MIN")
	private String min;
	
	@Column(name="MAX")
	private String max;
	
	@Column(name="BASE_TYPE")
	private String baseType;
	
	@Column(name="BASE_ON")
	private String baseOn;

	@Column(name="BASE_TABLE")
	private String baseTable;
	
	@Column(name="BASE_COLUMN")
	private String baseColumn;
	
	@Column(name="STRATEGY")
	private String strategy = STRATEGY_APPEND;//生成策略：F--补缺(Fill)，A--追加(Append)
	
	// 进制类型
	@Column(name="FORMAT_CODE")
	private String formatCode;
	
	/**
	 * 后续Sequence规则
	 * 用于有多个Sequence规则的情况
	 * 当nextSequenceRule不为空时需要检查nextSequenceRule的当前值
	 * 如果当前值已经等于最大值,则当前SequenceRule会生成一个新的ID,否则维持原有ID不变
	 * 
	 * 注意目前只支持一级NextSequence及ID生成规则中最多只能包含2个Sequence行
	 */
	@Transient
	private List<GeneratorRuleLine> behindGeneratorRules;
	
	@Transient
	private SequenceRuleLine nextSequenceRule;
		
	public String getId(GeneratorContext context) throws Exception{	
		return getIdBatch(context, 1)[0];
	}
	
	/**
	 * 生成ADSequence的Name即(baseOnString)	
	 * @param context
	 * @return
	 */
	public String getBaseOnString(GeneratorContext context) {
		List<String> useBaseOns = new ArrayList<String>();
		if (baseOn == null || baseOn.trim().length() == 0) {
			//如果未定义baseOn，默认是基于pattern方式
			//pattern方式形成的baseOnString由一下几部分组成
			//generatorType + "," + idPrefix + "_"
			//如: Lot,MBA12K801312._
			useBaseOns.add(BASEON_PATTERN);
		} else {
			String[] baseOns = baseOn.split(";");
			for (String current : baseOns) {
				if (current != null && current.trim().length() > 0) {
					useBaseOns.add(current);
				}
			}
		}
		String baseOnString = "";
		String prefix = null;
		if(context != null && context.getObjectType() != null){
			prefix = context.getObjectType() + ",";
			baseOnString = prefix;
		}
		for (String useBaseOn : useBaseOns) {
			if (BASEON_PATTERN.equals(useBaseOn)) {
				baseOnString = context.getIdPrefix() + "_";
				if (prefix != null){
					baseOnString = prefix + baseOnString;
				}
				break;
			} else {
				/*Object baseOnValue = context.getParameter(useBaseOn);
				String key = "";
				//把baseOn条件对应在对象中的属性值记下
				if (baseOnValue != null){
					if (baseOnValue instanceof String){
						key = (String)baseOnValue;
					}else if (baseOnValue instanceof Long){
						key = String.valueOf(baseOnValue);
					}else if (baseOnValue instanceof BigDecimal){
						key = ((BigDecimal)baseOnValue).toString();
					}else if (baseOnValue instanceof Date){
						SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
						key = sdf.format((Date)baseOnValue);
					}
				}*/
				String key = "";
				List<GeneratorRuleLine> ruleLines = context.getGeneratorRule().getRuleLines();
				Long seqNo = Long.valueOf(useBaseOn);
				for (GeneratorRuleLine ruleLine : ruleLines) {
					if (seqNo - ruleLine.getSeqNo() == 0) {
						try {
							key = ruleLine.getId(context);
						} catch (Exception e) {
							logger.error("getId " + e.getMessage(), e);
							return null;
						}
					}
				}
				baseOnString += key;
			}
		}
		return baseOnString;
	}
	
	public String[] getIdBatch(GeneratorContext context, int idSize) throws Exception{	
		try {
			//首先生成ADSequence的Name即(baseOnString)	
			String baseOnString = getBaseOnString(context);
			
			//获得允许的最大、最小值
			int minValue = Integer.MIN_VALUE;
			if (min != null && min.trim().length() > 0) {
				minValue = translate(min);
			}
			int maxValue = Integer.MAX_VALUE;
			if (max != null && max.trim().length() > 0) {
				maxValue = translate(max);
			}
			
			List<ADURefList> formatCodeList = null;
			if (!StringUtil.isEmpty(formatCode)) {
				formatCodeList = context.getADManager().getADURefList(orgRrn, formatCode);
			}
			
			if (nextSequenceRule != null) {
				Long thisCurrentValueL = context.getMBASManager().getCurrentSequenceValue(context.getSessionContext().getOrgRrn(), objectRrn, baseOnString);
				if (thisCurrentValueL != null) {
					//thisCurrentValueL存的是下一序号，需要减1到上一序号，这里减1就是有需要跳过的号，也没事
					thisCurrentValueL = thisCurrentValueL - 1;
					String thisCurrentValue = translate(thisCurrentValueL.intValue(), formatCodeList);
					if (thisCurrentValue != null) {
						boolean isGenerateNew = isGenerateNewValueByNext(context, baseOnString, thisCurrentValue, formatCodeList);
						if (!isGenerateNew) {
							return new String[] {thisCurrentValue};
						}
					}
				}
			}
			
			List<String> currentSeqList = new ArrayList<String>();
			while (idSize > 0) {
				List<String> thisSeqList = new ArrayList<String>();
				int[] currentInts = getNextSeq(context, idSize, baseOnString, minValue);
				for (int currentInt : currentInts) {
					String currentSeq = getCurrentSeq(currentInt, maxValue, formatCodeList);
					if (currentSeq != null) {
						thisSeqList.add(currentSeq);
					}
				}
				idSize = idSize - thisSeqList.size();
				currentSeqList.addAll(thisSeqList);
			}
			return currentSeqList.toArray(new String[currentSeqList.size()]);
		} catch (Exception e) {
			String errorInfo = e.toString();
			throw MBasExceptionBundle.bundle.IdRuleSequenceRuleLineError(context.getGeneratorRule().getName(), String.valueOf(getSeqNo()), errorInfo);
		}
	}
	
	
	public String getCurrentSeq(int currentInt, int maxValue, List<ADURefList> formatCodeList) {
		//如果比最大值大,则后面的值不再取
		if (currentInt > maxValue) {
			return "_";
		}
		setExclude(exclude);
		String currentSeq = "_";
		if ((excludeString != null && excludeString.size() > 0) || 
				(excludeAlpha != null && excludeAlpha.size() > 0)) {
			currentSeq = translate(currentInt, formatCodeList);
			if (isExclude(currentSeq)) {
				return null;
			}
		} else {
			currentSeq = translate(currentInt, formatCodeList);
		}
		// 没设置最大值时。超过了位数限制抛出异常
		if (currentSeq.length() > sizee.intValue()) {
			throw MBasExceptionBundle.bundle.IdMoreThanSize();
		}
		return currentSeq;
	}
	
	public int[] getNextSeq(GeneratorContext context, int idNum, String baseOnString, int minValue) {
		try {
			MBASManager basManager = context.getMBASManager();
			//根据baseOnString取得所需要的Sequence值
			int[] ids = basManager.getNextSequenceValue(orgRrn, objectRrn, baseOnString, idNum, minValue, context.isNewTrans());
			return ids;
		} catch (Exception e) {
			logger.error("getId " + e.getMessage(), e);
			return null;
		}
	}
	
	/**
	 * 根据下一Sequence信息判断当前Sequence是否要产生一个新的值
	 * 为true时表示要产生,否则不产生
	 * @throws Exception 
	 */
	public boolean isGenerateNewValueByNext(GeneratorContext context, String baseOnString, String thisCurrentValue, List<ADURefList> formatCodeList) throws Exception {
		if (nextSequenceRule != null) {
			MBASManager basManager = context.getMBASManager();
			String nextBaseOnString = "";	
			if (CollectionUtils.isEmpty(behindGeneratorRules)) {
				if (StringUtil.isEmpty(nextSequenceRule.getBaseOn())) {
					if (context.getObjectType() != null) {
						nextBaseOnString = context.getObjectType() + ",";
					}
					nextBaseOnString = nextBaseOnString + context.getIdPrefix() + thisCurrentValue + "_";
				} else {
					nextBaseOnString = baseOnString + thisCurrentValue;	
				}
			} else {
				String behindBaseOnString = "";
				for (GeneratorRuleLine rl : behindGeneratorRules) {
					behindBaseOnString =  behindBaseOnString + rl.getId(context);
				}
				
				if (StringUtil.isEmpty(nextSequenceRule.getBaseOn())) {
					if (context.getObjectType() != null) {
						nextBaseOnString = context.getObjectType() + ",";
					}
					nextBaseOnString = nextBaseOnString + context.getIdPrefix() + thisCurrentValue + behindBaseOnString + "_";
				} else {
					nextBaseOnString = baseOnString + thisCurrentValue + behindBaseOnString;	
				}
			}
			
			Long nextCurrentValueL = basManager.getCurrentSequenceValue(context.getSessionContext().getOrgRrn(), nextSequenceRule.getObjectRrn(), nextBaseOnString);
			if (nextCurrentValueL != null) {		
				if (nextSequenceRule.getMax() != null) {
					int maxValue = nextSequenceRule.translate(nextSequenceRule.getMax() , formatCodeList);
					if (Long.valueOf(maxValue).compareTo(nextCurrentValueL) < 0) {
						//表示下一序号已经大于最大序号
						return true;
					} else {
						return false;
					}
				} else {
					return false;
				}
			}
		}
		return true;
	}
	
	private void parserExcludeString(String exclude) {
		if (exclude != null && exclude.trim().length() > 0) {
			String[] excludes = exclude.split(";");
			excludeString = new ArrayList<String>();
			for (String currentString : excludes ) {
				if (SEQUENCETYPE_DIGITS.equals(sequenceType)) {
					currentString = String.format("%0" + sizee + "d", Integer.parseInt(currentString));
				} else if (SEQUENCETYPE_MIX.equals(sequenceType)) {
					if (currentString.length() < sizee) {
						int i = (int)(sizee - currentString.length());
						while (i > 0) {
							currentString = "0" + currentString;
							i--;
						}
					}
				} else {
					if (currentString.length() < sizee) {
						int i = (int)(sizee - currentString.length());
						while (i > 0) {
							currentString = "A" + currentString;
							i--;
						}
					}
				}
				excludeString.add(currentString);
			}
		}
	}
	
	private void parserExcludeAlpha(String exclude) {
		if (exclude != null && exclude.trim().length() > 0) {
			String[] excludes = exclude.split(";");
			excludeAlpha = new ArrayList<String>();
			for (String currentString : excludes ) {
				excludeAlpha.add(currentString);
			}
		}
	}
	
	public boolean isExclude(String seq) {
		if (EXCLUDETYPE_STRING.equals(excludeType)) {
			if (excludeString != null) {
				if (excludeString.contains(seq)) {
					return true;
				}
			}
		} else {
			if (excludeAlpha != null) {
				for (String exclude : excludeAlpha) {
					if (seq.indexOf(exclude) != -1) {
						return true;
					}
				}
			}
		}
		return false;
	}
	
	/**
	 * 十进制按指定的进制转换成字符串
	 * @param currentSeq
	 * @param formatCodeList
	 * @return
	 */
	public String translate(int currentSeq, List<ADURefList> formatCodeList) {
		String currentString = "";
		if (StringUtil.isEmpty(formatCode)) {
			currentString = translate(currentSeq);
		} else {
			if (formatCodeList != null && formatCodeList.size() > 0) {
				char[] digits = new char[formatCodeList.size()];
				for (int i = 0; i < formatCodeList.size(); i++) {
					digits[i] = formatCodeList.get(i).getText().charAt(0);
				}
				String digit = toCustomNumericString(currentSeq, digits.length, digits);
				currentString = StringUtils.leftPad(digit, sizee.intValue(), "0");
			} else {
				// 如果没找到。默认返回数字
				String format = "%0" + sizee + "d";
				currentString = String.format(format, currentSeq);
			}
		}
		return currentString;
	}
	
	/**
	 * 按指定进制转换成10进制
	 * @param currentSeq
	 * @param formatCodeList
	 * @return
	 */
	public int translate(String currentSeq, List<ADURefList> formatCodeList) {
		int currentString = 0;
		if (StringUtil.isEmpty(formatCode)) {
			currentString = translate(currentSeq);
		} else {
			if (formatCodeList != null && formatCodeList.size() > 0) {
				char [] buf = currentSeq.toCharArray();
				for (int i = 0; i < buf.length; i++) {
					char c = buf[i];
					String decimalStr = formatCodeList.stream()
							.filter(ref -> StringUtils.equals(ref.getText(), String.valueOf(c))).findAny().get().getKey();
					int decimal = Integer.valueOf(decimalStr) * Double.valueOf(Math.pow(formatCodeList.size(), (buf.length - i -1))).intValue();
					currentString += decimal;
				}
			}
		}
		return currentString;
	}
	
	/**
	 * 
	 * @param decimal 十进制数
	 * @param radix 待转换的目的进制位数
	 * @param digits 转换进制的字符集
	 * @return
	 */
	public String toCustomNumericString(int decimal, int radix, char[] digits) {
		long num = 0;
		if (decimal < 0) {
			num = ((long) 2 * 0x7fffffff) + decimal + 2;
		} else {
			num = decimal;
		}
		// 只支持进制数的最大数，超出转换范围则报错，例：4进制的最大十进制数为255，超过255则报错
		int maxBuf = radix + 1;
		char[] buf = new char[maxBuf];
		int charPos = maxBuf;
		while ((num / radix) > 0) {
			buf[--charPos] = digits[(int) (num % radix)];
			num /= radix;
		}
		buf[--charPos] = digits[(int) (num % radix)];
		return new String(buf, charPos, (maxBuf - charPos));
	}
	
	/**
	 * 对数字进行翻译
	 * 将数字转换为对应的字符串
	 */
	public String translate(int currentSeq) {
		String currentString = "";
		if (SEQUENCETYPE_DIGITS.equals(sequenceType)) {
			String format = "%0" + sizee + "d";
			currentString = String.format(format, currentSeq);
		} else if (SEQUENCETYPE_MIX.equals(sequenceType)) {
			while (currentSeq > 0) {
				String letter;
				int mod = currentSeq % 36;
				if (mod > 9) {
					letter = String.valueOf((char)('A' + (mod - 10)));
				} else {
					letter = String.valueOf(mod);
				}
				currentString = letter + currentString;
				currentSeq = currentSeq / 36;
			}
			if (currentString.length() < sizee) {
				int i = (int)(sizee - currentString.length());
				while (i > 0) {
					currentString = "0" + currentString;
					i--;
				}
			}
		} else if (SEQUENCETYPE_SPECIAL1.equals(sequenceType)) {
			int currentSeq2 = currentSeq % 10;
			int currentSeq1 = (currentSeq - currentSeq2) / 10;
			while (currentSeq1 > 0) {
				String letter;
				int mod = currentSeq1 % 36;
				if (mod > 9) {
					letter = String.valueOf((char)('A' + (mod - 10)));
				} else {
					letter = String.valueOf(mod);
				}
				currentString = letter + currentString;
				currentSeq1 = currentSeq1 / 36;
			}
			
			currentString += currentSeq2;
			
			if (currentString.length() < sizee) {
				int i = (int)(sizee - currentString.length());
				while (i > 0) {
					currentString = "0" + currentString;
					i--;
				}
			}
		} else {
			while (currentSeq > 0) {
				int mod = (currentSeq - 1) % 26;
				String letter = String.valueOf((char)('A' + mod));
				currentString = letter + currentString;
				currentSeq = (currentSeq - 1) / 26;
			}
			if (currentString.length() < sizee) {
				int i = (int)(sizee - currentString.length());
				while (i > 0) {
					currentString = "A" + currentString;
					i--;
				}
			}
		}
		return currentString;
	}
	
	/**
	 * 对字符串进行翻译
	 * 将字符串转换为对应的数字
	 */
	public int translate(String currentSeq) {
		int currentInt = 0;
		if (SEQUENCETYPE_DIGITS.equals(sequenceType)) {
			//如果是DIGITS类型,则直接翻译
			currentInt = Integer.parseInt(currentSeq);
		} else if (SEQUENCETYPE_MIX.equals(sequenceType)) {
			//如果是MIX类型
			char[] cs = currentSeq.toCharArray();
			for(int i = 0; i < cs.length; i++) {
				int digits;
				if (Character.isDigit(cs[i])) {
					digits = Integer.parseInt(String.valueOf(cs[i]));
				} else {
					digits = cs[i] - 64 + 9;
				}
				//为36进制(10位数字+26位字母)
				currentInt += digits * Math.pow(36, cs.length - 1 - i);
			}
		} else if (SEQUENCETYPE_SPECIAL1.equals(sequenceType)) {
			String currentSeq1 = currentSeq.substring(0, currentSeq.length() - 1);
			String currentSeq2 = currentSeq.substring(currentSeq.length() - 1);
			
			int currentInt2 = Integer.valueOf(currentSeq2);
			char[] cs = currentSeq1.toCharArray();
			for(int i = 0; i < cs.length; i++) {
				int digits;
				if (Character.isDigit(cs[i])) {
					digits = Integer.parseInt(String.valueOf(cs[i]));
				} else {
					digits = cs[i] - 64 + 9;
				}
				//为36进制(10位数字+26位字母)
				currentInt += digits * Math.pow(36, cs.length - 1 - i);
			}
			
			currentInt = currentInt * 10 + currentInt2;
		} else {
			char[] cs = currentSeq.toCharArray();
			for(int i = 0; i < cs.length; i++) {
				//将字符转为为对应的数字(ASCII中A为65)
				int digits = cs[i] - 64;
				//为26进制(26位字母)
				currentInt += digits * Math.pow(26, cs.length - 1 - i);
			}
		}

		return currentInt;
	}
	
	public String getSequenceType() {
		return sequenceType;
	}

	public void setSequenceType(String sequenceType) {
		this.sequenceType = sequenceType;
	}

	public String getSequenceDirection() {
		return sequenceDirection;
	}

	public void setSequenceDirection(String sequenceDirection) {
		this.sequenceDirection = sequenceDirection;
	}

	public String getExclude() {
		return exclude;
	}

	public void setExclude(String exclude) {
		this.exclude = exclude;
		if(exclude != null){
			if (EXCLUDETYPE_STRING.equals(excludeType)) {
				parserExcludeString(exclude);
			} else {
				parserExcludeAlpha(exclude);
			}
		}
	}

	public String getMin() {
		return min;
	}

	public void setMin(String min) {
		this.min = min;
	}

	public String getMax() {
		return max;
	}

	public void setMax(String max) {
		this.max = max;
	}

	public String getBaseOn() {
		return baseOn;
	}

	public void setBaseOn(String baseOn) {
		this.baseOn = baseOn;
	}

	public Long getSizee() {
		return sizee;
	}

	public void setSizee(Long sizee) {
		this.sizee = sizee;
	}

	public String getBaseType() {
		return baseType;
	}

	public void setBaseType(String baseType) {
		this.baseType = baseType;
	}

	public String getBaseTable() {
		return baseTable;
	}

	public void setBaseTable(String baseTable) {
		this.baseTable = baseTable;
	}

	public String getBaseColumn() {
		return baseColumn;
	}

	public void setBaseColumn(String baseColumn) {
		this.baseColumn = baseColumn;
	}

	public String getStrategy() {
		return strategy;
	}

	public void setStrategy(String strategy) {
		this.strategy = strategy;
	}

	public String getExcludeType() {
		return excludeType;
	}

	public void setExcludeType(String excludeType) {
		this.excludeType = excludeType;
	}

	public String getFormatCode() {
		return formatCode;
	}

	public void setFormatCode(String formatCode) {
		this.formatCode = formatCode;
	}

	public List<GeneratorRuleLine> getBehindGeneratorRules() {
		return behindGeneratorRules;
	}

	public void setBehindGeneratorRules(List<GeneratorRuleLine> behindGeneratorRules) {
		this.behindGeneratorRules = behindGeneratorRules;
	}

	public SequenceRuleLine getNextSequenceRule() {
		return nextSequenceRule;
	}

	public void setNextSequenceRule(SequenceRuleLine nextSequenceRule) {
		this.nextSequenceRule = nextSequenceRule;
	}
	
}
