package com.glory.mes.base.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.ejb.EJB;
import javax.ejb.Stateless;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.service.CoreService;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.base.ejb.MBASManagerLocal;
import com.glory.mes.base.exception.MBasExceptionBundle;
import com.glory.mes.base.model.idgenerator.GeneratorContext;
import com.glory.mes.base.model.idgenerator.GeneratorRule;

@Stateless
@CoreService(serviceGroup = "MESCore")
public class IDGeneratorService {

	@EJB
    private MBASManagerLocal mBasManager;
	
	/**
	 * 使用IDGenerator生成ID号
	 * 
	 * @param object 待生成ID号的对象
	 * @param objectType 对象类型
	 * @param transType 事务类型
	 * @param idParams 初始化参数,可为空
	 * @param isNewTrans 是否为一个新的事务,true时启动一个新的事务,false时于调用事务保持同一事务
	 *                   为true时目的是提高需要大量产生Id时,所带来的性能问题
	 * @param isThrowExecption 当无法生产ID时抛出异常
	 * @param sc
	 */
	public String generateId(ADBase object, String objectType, String transType, 
			Map<String, Object> idParams, boolean isNewTrans, boolean isThrowExecption, SessionContext sc) throws ClientException {
		Map<String, Object> parameterMap = new HashMap<String, Object>();
		if (idParams != null) {
			parameterMap.putAll(idParams);
		}
		parameterMap.put("objectType", objectType);
		parameterMap.put("transaction", transType);
		GeneratorContext context = new GeneratorContext(object, parameterMap, sc, objectType);
		
		String idString;
		if (isNewTrans) {
			idString = mBasManager.generatorId(sc.getOrgRrn(), context);
		} else {
			GeneratorRule rule = mBasManager.getGeneratorRule(sc.getOrgRrn(), context, true);
			if (rule == null) {
				throw MBasExceptionBundle.bundle.IdGenerateRuleNotExist(transType);
			}
			context.setGeneratorRule(rule);
			List<String> ids = mBasManager.generatorId(sc.getOrgRrn(), rule, context);
			idString = ids.get(0);
		}
		if (StringUtil.isEmpty(idString)) {
			throw MBasExceptionBundle.bundle.IdNotGenerate();
		}
		return idString;
	}
	
	/**
	 * 使用IDGenerator批量生成ID号
	 * 
	 * @param object 待生成ID号的对象
	 * @param objectType 对象类型
	 * @param transType 事务类型
	 * @param idParams 初始化参数,可为空
	 * @param isNewTrans 是否为一个新的事务,true时启动一个新的事务,false时于调用事务保持同一事务
	 *                   为true时目的是提高需要大量产生Id时,所带来的性能问题
	 * @param idNum 批量生成ID数量
	 * @param isThrowExecption 当无法生产ID时抛出异常
	 * @param sc
	 */
	public List<String> batchGenerateId(ADBase object, String objectType, String transType, 
			Map<String, Object> idParams, boolean isNewTrans, int idNum, boolean isThrowExecption,SessionContext sc) throws ClientException {
		Map<String, Object> parameterMap = new HashMap<String, Object>();
		if (idParams != null) {
			parameterMap.putAll(idParams);
		}
		parameterMap.put("objectType", objectType);
		parameterMap.put("transaction", transType);
		GeneratorContext context = new GeneratorContext(object, parameterMap, sc, objectType);
		context.setBatch(true);
		context.setIdNum(idNum);
		
		List<String> idStrings;
		if (isNewTrans) {
			idStrings = mBasManager.batchGeneratorId(sc.getOrgRrn(), context, isThrowExecption);
		} else {
			GeneratorRule rule = mBasManager.getGeneratorRule(sc.getOrgRrn(), context, true);
			if (rule == null) {
				throw MBasExceptionBundle.bundle.IdGenerateRuleNotExist(transType);
			}
			context.setGeneratorRule(rule);
			idStrings = mBasManager.generatorId(sc.getOrgRrn(), rule, context);
		}
		return idStrings;
	}
	
}
