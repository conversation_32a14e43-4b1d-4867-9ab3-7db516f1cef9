package com.glory.mes.base.model.idgenerator;

import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.JoinColumn;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.glory.framework.base.model.VersionControl;
import com.google.common.collect.Lists;

@Entity
@Table(name = "BAS_ID_GENERATOR_RULE")
public class GeneratorRule extends VersionControl {
	private static final long serialVersionUID = 1L;

	/**
	 * 用作默认IDGenerate的ObjectRrn
	 * 系统提供默认的ID生成规则{@link generateDefauleRule}
	 * 默认的规则不在数据库种保存,公用DEFAULT_RULE_RRN
	 */
	public static final Long DEFAULT_RULE_RRN = -10001L;
	
	@Column(name = "RULE_TYPE")
	private String ruleType;
	
	@OneToMany(fetch=FetchType.EAGER, cascade=CascadeType.ALL, orphanRemoval = true)
	@JoinColumn(name = "RULE_RRN", referencedColumnName = "OBJECT_RRN", nullable = true)
	@OrderBy(value="seqNo ASC")
	private List<GeneratorRuleLine> ruleLines;
	
	@Transient
	private String generatedString;
	
	public String getRuleType() {
		return ruleType;
	}

	public void setRuleType(String ruleType) {
		this.ruleType = ruleType;
	}
	
	public List<GeneratorRuleLine> getRuleLines() {
		return ruleLines;
	}

	public void setRuleLines(List<GeneratorRuleLine> ruleLines) {
		this.ruleLines = ruleLines;
	}

	public String getGeneratedString() throws Exception {
		try {
			StringBuffer sb = new StringBuffer();
			if (ruleLines == null || ruleLines.size() == 0){
				return "";
			}
			
			for(GeneratorRuleLine line : ruleLines){
				if(line instanceof FixedStringRuleLine){
					sb.append(line.getId(null));
				}else if(line instanceof DateRuleLine){
					sb.append(((DateRuleLine)line).getDateFormat());
				}else{
					sb.append("#");
				}
			}
			return sb.toString();
		} catch (Exception e) {
			throw e;
		}
	}

	public void setGeneratedString(String generatedString) {
		this.generatedString = generatedString;
	}
	
	/**
	 * 生成默认的ID规则,prefix+"YY"+"M"+"DD"+sequence
	 * 
	 * @param prefix ID前缀
	 * @param seqNum 序号位数
	 */
	public static GeneratorRule generateDefaultRule(String prefix, int seqNum) {
		GeneratorRule rule = new GeneratorRule();
		rule.setObjectRrn(DEFAULT_RULE_RRN);
		
		List<GeneratorRuleLine> lines = Lists.newArrayList();
		FixedStringRuleLine line1 = new FixedStringRuleLine();
		line1.setFixedString(prefix);
		line1.setSeqNo(1L);
		lines.add(line1);
		
		DateRuleLine line2 = new DateRuleLine();
		line2.setDateFormat(DateRuleLine.DATEFORMAT_YY);
		line2.setSeqNo(2L);
		lines.add(line2);
		
		DateRuleLine line3 = new DateRuleLine();
		line3.setDateFormat(DateRuleLine.DATEFORMAT_M);
		line3.setSeqNo(3L);
		lines.add(line3);
		
		DateRuleLine line4 = new DateRuleLine();
		line4.setDateFormat(DateRuleLine.DATEFORMAT_DD);
		line4.setSeqNo(4L);
		lines.add(line4);
		
		SequenceRuleLine line5 = new SequenceRuleLine();
		line5.setObjectRrn(-1000101L);
		line5.setSequenceType(SequenceRuleLine.SEQUENCETYPE_DIGITS);
		line5.setSizee((long)seqNum);
		line5.setSeqNo(5L);
		lines.add(line5);
		
		rule.setRuleLines(lines);
		return rule;
		
	}
}
