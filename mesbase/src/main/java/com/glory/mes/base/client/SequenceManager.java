package com.glory.mes.base.client;

import com.glory.framework.activeentity.model.ADSequence;
import com.glory.framework.core.exception.ClientException;

public interface SequenceManager {
			
	public ADSequence createNewSequence(long orgRrn, String name, long generatorRrn, int minValue);
	
	public int[] getNextSequenceValueNewTrans(long orgRrn, long generateRrn, String name, int count, int minValue) throws ClientException;
	
}
