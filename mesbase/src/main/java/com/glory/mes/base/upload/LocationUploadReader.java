package com.glory.mes.base.upload;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.ejb.EJB;
import javax.ejb.Stateless;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;

import com.glory.common.excel.UploadField;
import com.glory.common.excel.upload.DefaultUploadReader;
import com.glory.common.excel.upload.UploadErrorLog;
import com.glory.common.excel.upload.UploadReader;
import com.glory.framework.activeentity.ejb.ADManagerLocal;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.base.model.Location;
import com.google.common.collect.Maps;

@Stateless
public class LocationUploadReader extends DefaultUploadReader implements UploadReader {

	@EJB
	private ADManagerLocal adManager;
	
	Map<String, Location> parentMap = Maps.newHashMap();
	
	@Override
	public void init() {
		parentMap = Maps.newHashMap();
	}
	
	@Override
	public Object read(Object bean, Row row, Map<Integer, UploadField> fieldMap, boolean isMandatory) throws Exception {
		
		if (parentMap.isEmpty()) {
			// 转map方便判断
			List<Location> parenList = adManager.getEntityList(sc.getOrgRrn(), Location.class, Integer.MAX_VALUE, "parentLocationRrn IS NULL ", "");
			parentMap = parenList.stream().collect(Collectors.toMap(Location::getName, obj -> obj));
		}
		Location location = new Location();
		location = (Location) super.read(location, row, fieldMap, isMandatory);
		
		for (Integer cellIndex : fieldMap.keySet()) {
			UploadField field = fieldMap.get(cellIndex);				
			Cell cell = row.getCell(cellIndex);
			if(!Objects.equals("parentLocationRrn", field.getName())) {
				continue;
			}
			if(cell != null) {
				String value = cell.getRichStringCellValue().getString().strip();
				location.setParentLocationName(value);
			}
		}
		
		if(StringUtil.isEmpty(location.getParentLocationName())) {
			if(parentMap.containsKey(location.getName())) {//如果数据库中已存在，违反索引
				UploadErrorLog log = new UploadErrorLog((long)row.getRowNum(), null, "PARENT LOCATION", getMessage("error.object_ishave"));
				errLogs.add(log);
			} else {
				parentMap.put(location.getName(), location);
			}
		}
			
		//循环区分数据进行存储
		if(!StringUtil.isEmpty(location.getParentLocationName())) {
			if(!parentMap.containsKey(location.getParentLocationName())) {
				//判断父类是否存在
				UploadErrorLog log = new UploadErrorLog((long)row.getRowNum() + 1, null, "PARENT LOCATION", getMessage("common.is_not_exist"));
				errLogs.add(log);
			}
		}
		return location;
	}
	
	@Override
	public int getPriority() {
		return 20;
	}

	@Override
	public String getActionName() {
		return "LocationUploadReader";
	}

	@Override
	public String getActionDesc() {
		return "Location excel upload reader";
	}

	@Override
	public String getAuthorityName() {
		return "Ras.Location";
	}
}
