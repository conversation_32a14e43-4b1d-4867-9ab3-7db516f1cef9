package com.glory.mes.base.model.idgenerator;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.glory.framework.core.exception.ClientException;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.exception.MBasExceptionBundle;
import com.google.common.collect.Lists;

/**
 * 循环序号生成方法
 * 在最小和最大间循环,使用其它表判断栏位是否存在,如果存在则寻找下一个
 * 
 * 这里只实现默认的方法,及按照序号依次递增
 * 特殊的实现方法需要实现getNextSeq
 */
@Entity
@DiscriminatorValue("C")
public class SequenceCycleRuleLine extends SequenceRuleLine {
	private static final long serialVersionUID = 1L;
				
	@Column(name="WHERE_CLAUSE")
	private String whereClause;

	@Column(name="VARIABLE_DIRECTION")
	private String variableDirection;
	
	@Column(name="START_POSITION")
	private Long startPosition = 1L;//默认从第一位开始
	
	@Column(name="LENGTH")
	private Long length = 1L;//默认长度取1
	
	public String getWhereClause() {
		return whereClause;
	}

	public void setWhereClause(String whereClause) {
		this.whereClause = whereClause;
	}

	public String getVariableDirection() {
		return variableDirection;
	}

	public void setVariableDirection(String variableDirection) {
		this.variableDirection = variableDirection;
	}

	public Long getStartPosition() {
		return startPosition;
	}

	public void setStartPosition(Long startPosition) {
		this.startPosition = startPosition;
	}

	public Long getLength() {
		return length;
	}

	public void setLength(Long length) {
		this.length = length;
	}

	/**
	 * 获得下一个序号
	 * 
	 * @param context
	 */
	public String getId(GeneratorContext context) throws Exception {
		String id = getIds(context).get(0);
		return id;
	}
	
	/**
	 * 一次获得多个序号
	 * 
	 * @param context
	 */
	public List<String> getIds(GeneratorContext context) throws Exception {
		String baseOnString = getBaseOnString(context);
		
		//循环生成方式, 需要在表中查找已经存在的ID,因此产生并发冲突的可能性大大增加
		//这里getNextSeq只是为了控制数据库锁,避免并发时产生相同的ID
		//因此这里的必须使用旧的事务
		context.setNewTrans(false);
		getNextSeq(context, 1, baseOnString, 1);

		List<String> existSeqStr = getExistSequence(context);
		return getCurrentSeq(existSeqStr, context.getIdNum());
	}
	
	/**
	 * 一次获得多个序号,序号的产生跟AD_SEQUENCE的NextValue无关。
	 * 
	 * @param existSeqStr 已经存在的序号号
	 * @param idNum 需要产生的序号数量
	 */
	public List<String> getCurrentSeq(List<String> existSeqStr, int idNum) throws Exception {
		List<Integer> existSeqInt = null;
		if (!CollectionUtils.isEmpty(existSeqStr)) {
			if (SEQUENCETYPE_DIGITS.equals(this.getSequenceType())) {
				existSeqInt = existSeqStr.stream().map(Integer::parseInt).collect(Collectors.toList());
				Collections.sort(existSeqInt);
			} else {
				existSeqInt = existSeqStr.stream().map(p -> translate(p)).collect(Collectors.toList());
				Collections.sort(existSeqInt);			
			}
		}
		
		List<String> resultSeqs = Lists.newArrayList();

		int minValue = Integer.parseInt(this.getMin());
		int maxValue = Integer.parseInt(this.getMax());
		// STRATEGY_APPEND从已存在最大值后面去增长，STRATEGY_FILL模式下，如果序号有间隔，会补充中间的间隔.
		if (existSeqInt == null || STRATEGY_FILL.equals(this.getStrategy())) {
			//从最小值开始
			for (int i = minValue; i <= maxValue; i++) {
				String curValue = getCurrentSeq(i, maxValue, null);
				if (curValue != null && (existSeqStr == null || !existSeqStr.contains(curValue))) {
					resultSeqs.add(curValue);
					if (resultSeqs.size() >= idNum) {
						break;
					}
				}
			}
		} else {
			//从当前最大值开始
			int existMax = existSeqInt.get(existSeqInt.size() - 1);
			for (int i = existMax + 1; i <= maxValue; i++) {
				String curValue = getCurrentSeq(i, maxValue, null);
				if (curValue != null && !existSeqStr.contains(curValue)) {
					resultSeqs.add(curValue);
					if (resultSeqs.size() >= idNum) {
						break;
					}
				}
			}
			if (resultSeqs.size() < idNum) {
				//继续从头开始
				for (int i = minValue; i < existMax; i++) {
					String curValue = getCurrentSeq(i, maxValue, null);
					if (curValue != null && !existSeqStr.contains(curValue)) {
						resultSeqs.add(curValue);
						if (resultSeqs.size() >= idNum) {
							break;
						}
					}
				}
			}
		}
		
		if (resultSeqs.size() < idNum) {
			//抛出异常
			throw MBasExceptionBundle.bundle.CycleRuleCanNotGenerateId();
		}
		return resultSeqs;
	}
	
	
	public List<String> getExistSequence(GeneratorContext context) throws Exception {	
		// 从DB中获得对那个的数据
		// 采用字符串截取的方法获得已经存在的SEQ
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT DISTINCT ");
		sql.append(this.getBaseColumn());
		sql.append(" FROM ");
		sql.append(this.getBaseTable());
		sql.append(StringUtils.defaultIfEmpty(VariableRuleLine.parseWhereClause(whereClause, context), ""));
		MBASManager basManager = context.getMBASManager();
		List<?> rList = (List<?>) basManager.executeSql(sql.toString());
		
		// ID的最小长度
		long minLenght = (startPosition == null ? 1 : startPosition) + (length == null ? 1 : length) - 1;
		List<String> list = Lists.newArrayList();
		for (Object r : rList) {
			String str = String.valueOf(r);
			if (str.length() >= minLenght) {
				//采用截取方法
				switch (Integer.valueOf(variableDirection)){
					case 1:
						str = str.substring((startPosition.intValue() - 1), (startPosition.intValue() + length.intValue() - 1));
						break;
					case 2:
						str = str.substring(str.length() - (startPosition.intValue() + length.intValue() - 1), str.length() - startPosition.intValue() + 1);
						break;
				}
				list.add(str);
			} else {
				throw MBasExceptionBundle.bundle.CycleRuleIllegal();
			}
		}
		return list;
	}
	
}
