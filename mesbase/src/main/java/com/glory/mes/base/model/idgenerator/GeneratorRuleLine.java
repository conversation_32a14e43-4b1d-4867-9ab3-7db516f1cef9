package com.glory.mes.base.model.idgenerator;

import javax.persistence.Column;
import javax.persistence.DiscriminatorColumn;
import javax.persistence.DiscriminatorType;
import javax.persistence.Entity;
import javax.persistence.Inheritance;
import javax.persistence.InheritanceType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.glory.framework.activeentity.model.ADBaseObjectSeq;


@Entity
@Table(name = "BAS_ID_GENERATOR_RULE_LINE")
@Inheritance(strategy=InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name="DS_TYPE", discriminatorType = DiscriminatorType.STRING, length = 32)
public abstract class GeneratorRuleLine extends ADBaseObjectSeq {

	private static final long serialVersionUID = 1L;
	
	public static final String DSTYPE_FIXEDSTRING = "F-Fixed String";
	public static final String DSTYPE_DATETIME = "D-Date Time Value";
	public static final String DSTYPE_VARIABLE = "V-Variable Value";
	public static final String DSTYPE_SEQUENCE = "S-Sequence Value";
	public static final String DSTYPE_XSEQUENCE = "X-Sequence Value";
	public static final String DSTYPE_RADIX = "R-Radix Value";
	public static final String DSTYPE_SEQUENCECYCLE = "C-Sequence Value";

	@Column(name="RULE_RRN")
	private Long ruleRrn;
	
	@ManyToOne
	@JoinColumn(name="RULE_RRN", referencedColumnName="OBJECT_RRN", insertable = false, updatable = false)
	private GeneratorRule rule;

	@Column(name="RULE_ID")
	private String ruleId;
	
	@Column(name="SEQ_NO")
	private Long seqNo;
	
	@Column(name="DS_TYPE", insertable=false, updatable=false)
	private String dsType;
	
	public abstract String getId(GeneratorContext context) throws Exception;

	public Long getRuleRrn() {
		return ruleRrn;
	}

	public void setRuleRrn(Long ruleRrn) {
		this.ruleRrn = ruleRrn;
	}

	public String getRuleId() {
		return ruleId;
	}

	public void setRuleId(String ruleId) {
		this.ruleId = ruleId;
	}

	public Long getSeqNo() {
		return seqNo;
	}

	public void setSeqNo(Long seqNo) {
		this.seqNo = seqNo;
	}

	public String getDsType() {
		return dsType;
	}

	public void setDsType(String dsType) {
		this.dsType = dsType;
	}

	public GeneratorRule getRule() {
		return rule;
	}

	public void setRule(GeneratorRule rule) {
		this.rule = rule;
	}

}
	
