package com.glory.mes.base.client;

import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADSequence;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.security.model.ADUser;
import com.glory.mes.base.calendar.BusinessCalendar;
import com.glory.mes.base.merge.MergeRule;
import com.glory.mes.base.merge.MergeRuleLine;
import com.glory.mes.base.model.CalendarDay;
import com.glory.mes.base.model.LineUser;
import com.glory.mes.base.model.Location;
import com.glory.mes.base.model.Shift;
import com.glory.mes.base.model.ShiftTime;
import com.glory.mes.base.model.Team;
import com.glory.mes.base.model.TeamUser;
import com.glory.mes.base.model.idgenerator.GeneratorContext;
import com.glory.mes.base.model.idgenerator.GeneratorRule;
import com.glory.mes.base.model.idgenerator.GeneratorRuleLine;

public interface MBASManager {
		
	public EntityManager getEntityManager();
	
	CalendarDay getCalendarDay(long orgRrn, String calendarId, Date date);
	void saveCalendarDay(long orgRrn, List<CalendarDay> calendarDays, SessionContext context)throws ClientException;
	List<CalendarDay> selectCalendarDay(int year, int month,
			String calendarType, String calendarId, SessionContext sc) throws ClientException;
	void generateCalendar(ADBase adBase, SessionContext sc) throws ClientException;
	boolean checkCalendarID(ADBase adBase);
	
	public BusinessCalendar getCalendarByDay(long orgRrn, String calendarId) throws ClientException;
	public BusinessCalendar getCalendarByMinute(long orgRrn, String calendarId) throws ClientException;
	
	public GeneratorRule getGeneratorRule(long orgRrn, GeneratorContext gContext, boolean throwException) throws ClientException;
	GeneratorRule addRuleLine(GeneratorRule rule, GeneratorRuleLine line, SessionContext context) throws ClientException;
	GeneratorRule saveRuleLines(GeneratorRule rule, List<GeneratorRuleLine> lines, SessionContext context) throws ClientException;
	void deleteRuleLine(GeneratorRuleLine line, SessionContext context) throws ClientException;
	GeneratorRule updateRuleLine(GeneratorRuleLine oldLine, GeneratorRuleLine newLine, SessionContext context) throws ClientException;
	GeneratorRule moveRuleLine(GeneratorRuleLine line, int offSet, SessionContext context) throws ClientException;
	GeneratorRule inActiveGeneratorRule(GeneratorRule generatorRule, SessionContext sc) throws ClientException;
	
	public GeneratorRule getGeneratorRuleByName(long orgRrn, String name, boolean throwException) throws ClientException;
	String generatorId(long orgRrn, GeneratorContext context) throws ClientException;
	String generatorId(long orgRrn, GeneratorContext context, boolean throwException) throws ClientException;
	List<String> batchGeneratorId(long orgRrn, GeneratorContext context, boolean throwException) throws ClientException;
	List<String> generatorId(long orgRrn, GeneratorRule rule, GeneratorContext context) throws ClientException;
	List<String> generatorId(long orgRrn, GeneratorRule rule, boolean isParameterList, GeneratorContext context) throws ClientException;
	
	Location getLocationById(long orgRrn, String locationId) throws ClientException;
	Location getLocationByLineId(long orgRrn, long locationRrn, String lineId) throws ClientException;
	
	int[] getNextSequenceValue(long orgRrn, long generateRrn, String name, int count) throws ClientException;;
	int[] getNextSequenceValue(long orgRrn, long generateRrn, String name, int count, int minValue) throws ClientException;
	int[] getNextSequenceValue(long orgRrn, long generateRrn, String name, int count, int minValue, boolean newTrans) throws ClientException;
	
	List<?> executeSql(String sql) throws ClientException;
	List<ADUser> getLineUsers(long lineRrn) throws ClientException;
	public int executeUpdateSql(String sql) throws ClientException ;
	
	public MergeRule saveMergeRule(MergeRule mergerRule, SessionContext sc) throws ClientException;
	public MergeRule inActiveMergeRule(MergeRule mergerRule, SessionContext sc) throws ClientException;
	public MergeRule getMergeRule(String name, String category, boolean isVariable, SessionContext sc) throws ClientException;
	public List<MergeRuleLine> getMergeRuleLineVariable(long mergeRuleRrn) throws ClientException;
	
	public void saveLineUsers(long lineRrn, List<LineUser> lineUsers, SessionContext sc) throws ClientException;
	
	public Team getTeamByName(long orgRrn, String teamName, boolean exceptionFlag) throws ClientException;
	
	public void saveTeamUsers(Team team, List<TeamUser> teamUsers, SessionContext sc) throws ClientException;
	
	public void deleteTeamUsers(Team team, SessionContext sc) throws ClientException;
	
	public ShiftTime getShiftTime(long orgRrn, Date currentDate) throws ClientException;
	
	public Long getCurrentSequenceValue(long orgRrn, long generateRrn, String name) throws ClientException;
	
	public Shift saveShiftTime(Shift shift, List<ShiftTime> shiftTimeList, SessionContext sc) throws ClientException;
	
	public void deleteShift(Shift shift, SessionContext sc) throws ClientException;
	
	public ADSequence createNewSequence(long orgRrn, String name, long generatorRrn, int minValue);
	
	public void saveLocationChildAndParen(List<Location> locationList, SessionContext sc) throws ClientException;
}
