package com.glory.mes.base.model.idgenerator;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.apache.commons.beanutils.PropertyUtils;
import org.apache.log4j.Logger;

import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.exception.MBasExceptionBundle;

@Entity
@DiscriminatorValue("X")
public class XSequenceRuleLine extends GeneratorRuleLine {
	private static final Logger logger = Logger.getLogger(XSequenceRuleLine.class);
	
	private static final long serialVersionUID = 1L;

	/**
	 * 对应的变量,在variableType为"PARAMETER"时使用
	 * 如果Context的parameterMap有此变量,则使用此值
	 * 否则从Context的Object上取值取得对应的值
	 */
	@Column(name="PARAMETER")
	private String parameter;
	
	@Column(name="START_POSITION")
	private Long startPosition = 1L;//默认从第一位开始
	
	@Column(name="LENGTH")
	private Long length = 1L;//默认长度取1

	@Column(name="FIXED_STRING")
	private String fixedString = "0";
	
	public String getId(GeneratorContext context) throws ClientException {
		try {
			String variable = "";
			if (context.getParameterMap().containsKey(parameter)) {
				//如果Context的parameterMap有此变量,则使用此值
				variable = String.valueOf(context.getParameter(parameter));
			} else if (context.getObject() != null){
				//如果没有在Context的parameterMap中定义,则从Object中取值
				Object value;
				try {
					value = PropertyUtils.getProperty(context.getObject(), parameter);
					if (value != null) {
						variable = String.valueOf(value);
					}
				} catch (Exception e) {
				}
			}
			if (variable == null || variable.trim().length() == 0) {
				//如果不能获得对应的值,则用"_"代替
				variable = "_";
			}
			
			if (!"_".equals(variable)){
				String baseOn = parseBaseOn(variable);
				if(context != null && context.getObjectType() != null){
					String prefix = context.getObjectType() + ",";
					baseOn = prefix + baseOn;
				}
				int [] seqs = getNextSeq(context, 1, baseOn, 0);
				
				variable = xParse(variable, seqs[0]);
			}
			return variable;
		} catch (Exception e) {
			String errorInfo = e.toString();
			throw MBasExceptionBundle.bundle.IdRuleXSequenceRuleLineError(context.getGeneratorRule().getName(), String.valueOf(getSeqNo()), errorInfo);
		}
	}
	
	private String parseBaseOn(String variable) {
		String baseHead = variable.substring(0, (startPosition.intValue() - 1));
		for (int i = 0; i < length; i++) {
			baseHead = baseHead + fixedString;
		}
		return baseHead;
	}

	private String xParse(String variable, int seq) throws ClientException {
		try {
			String numStr = variable.substring(0, (startPosition.intValue() - 1));
			String result = String.valueOf(seq);
			
			if (length.compareTo(Long.valueOf(result.length())) > 0) {
				for (int i = 0; i < (length - result.length()); i++) {
					result = fixedString + result;
				}
			}
			return numStr + result;
		} catch (NumberFormatException e) {
			throw MBasExceptionBundle.bundle.GeneratorXseqXparse();
		}
		
	}
	
	private int[] getNextSeq(GeneratorContext context, int idNum, String baseOnString, int minValue) {
		try {
			MBASManager basManager = context.getMBASManager();
			//根据baseOnString取得所需要的Sequence值
			int[] ids = basManager.getNextSequenceValue(orgRrn, getRule().getObjectRrn(), baseOnString, idNum, minValue, context.isNewTrans());
			return ids;
		} catch (Exception e) {
			logger.error("getId " + e.getMessage(), e);
			return null;
		}
	}

	public String getParameter() {
		return parameter;
	}

	public void setParameter(String parameter) {
		this.parameter = parameter;
	}


	public Long getStartPosition() {
		return startPosition;
	}

	public void setStartPosition(Long startPosition) {
		this.startPosition = startPosition;
	}

	public Long getLength() {
		return length;
	}

	public void setLength(Long length) {
		this.length = length;
	}

}
