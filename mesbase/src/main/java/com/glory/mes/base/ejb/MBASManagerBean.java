package com.glory.mes.base.ejb;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Vector;
import java.util.stream.Collectors;

import javax.ejb.EJB;
import javax.ejb.Local;
import javax.ejb.Remote;
import javax.ejb.Stateless;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.enterprise.inject.Any;
import javax.enterprise.inject.Instance;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.LockModeType;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import com.glory.common.context.ejb.ContextManagerLocal;
import com.glory.common.context.model.ContextValue;
import com.glory.framework.activeentity.ejb.ADManagerLocal;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADRefList;
import com.glory.framework.activeentity.model.ADSequence;
import com.glory.framework.base.model.VersionControl;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ExceptionManager;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.security.model.ADUser;
import com.glory.framework.variable.ejb.VariableManagerLocal;
import com.glory.framework.variable.model.ADVariable;
import com.glory.mes.base.calendar.BusinessCalendar;
import com.glory.mes.base.calendar.Duration;
import com.glory.mes.base.cdi.IIdGeneratorCdiAction;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.exception.MBasExceptionBundle;
import com.glory.mes.base.merge.MergeRule;
import com.glory.mes.base.merge.MergeRuleContext;
import com.glory.mes.base.merge.MergeRuleLine;
import com.glory.mes.base.model.CalendarDay;
import com.glory.mes.base.model.CalendarHour;
import com.glory.mes.base.model.CalendarSetup;
import com.glory.mes.base.model.LineUser;
import com.glory.mes.base.model.Location;
import com.glory.mes.base.model.LocationLine;
import com.glory.mes.base.model.Shift;
import com.glory.mes.base.model.ShiftTime;
import com.glory.mes.base.model.Team;
import com.glory.mes.base.model.TeamUser;
import com.glory.mes.base.model.idgenerator.GeneratorContext;
import com.glory.mes.base.model.idgenerator.GeneratorRule;
import com.glory.mes.base.model.idgenerator.GeneratorRuleLine;
import com.glory.mes.base.model.idgenerator.SequenceRuleLine;
import com.google.common.collect.Lists;

@Stateless
@Remote(MBASManager.class)
@Local(MBASManagerLocal.class)
public class MBASManagerBean implements MBASManager, MBASManagerLocal {
	private static final Logger logger = Logger.getLogger(MBASManagerBean.class);

	@PersistenceContext
	private EntityManager em;

	@EJB
	private ADManagerLocal adManager;
	
	@EJB
	private ContextManagerLocal ctxManager;
	
	@EJB
	private SequenceManagerLocal sequenceManager;
	
	@EJB
	private VariableManagerLocal variableManager;
	
	@Inject
	@Any
	private Instance<IIdGeneratorCdiAction> idGeneratorActions;

	public EntityManager getEntityManager() {
		return em;
	}
	
	/**
     * 获取工作日历信息
     * @param orgRrn 区域号
     * @param calendarId 工作日历ID
     * @param date 日期
     * @throws ClientException
     */
	public CalendarDay getCalendarDay(long orgRrn, String calendarId, Date date) {
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT CalendarDay FROM CalendarDay CalendarDay ");
		sql.append(" WHERE ");
		sql.append(ADBase.BASE_CONDITION_N);
		sql.append(" AND calendarId = :calendarId ");
		sql.append(" AND sysDate = trunc(:sysdate) "); 
		try {
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("calendarId", calendarId);
			query.setParameter("sysdate", date);
			List<CalendarDay> days = (List<CalendarDay>)query.getResultList();
			if (days != null && days.size() > 0){
				return days.get(0);
			}

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return null;
	}
	
	/**
     * 检查日历ID
     * @param adBase
     * @throws ClientException
     */
	public boolean checkCalendarID(ADBase adBase) {
		CalendarSetup calendarSetup = (CalendarSetup) adBase;
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT CalendarDay.calendarId FROM CalendarDay as CalendarDay ");
		sql.append("WHERE CalendarDay.calendarType = :calendarType ");
		sql.append("AND CalendarDay.calendarId = :calendarId ");
		sql.append("AND CalendarDay.year = :year ");
		Query query = em.createQuery(sql.toString());
		query.setParameter("calendarType", calendarSetup.getCalendarType());
		query.setParameter("calendarId", calendarSetup.getCalendarId());
		if (calendarSetup.getYear() == null
				|| "".equals(calendarSetup.getYear())) { //因为Year不是在页面文本框中不一定要填的,默认为本年
			Date d = new Date();
			query.setParameter("year", ((Integer) (d.getYear() + 1900)).toString());
		} else {
			query.setParameter("year", calendarSetup.getYear());
		}
		List ob = query.getResultList();
		if (ob.size() != 0) {
			return true;
		}
		return false;
	}

	/**
	 * 根据年份、日历类型和日历编号生成工作日历。
	 * 
	 * @param adBase 工作日历
	 * @param  sc 上下文
	 * @throws ClientException
	 */
	public void generateCalendar(ADBase adBase, SessionContext sc) throws ClientException {
		CalendarSetup calendarSetup = (CalendarSetup) adBase;
		int dayCount = 365;
		int year = 0;
		// 删除存在的
		StringBuffer sql = new StringBuffer();
		sql.append("DELETE FROM CalendarDay CalendarDay");
		sql.append(" WHERE ");
		sql.append(ADBase.BASE_CONDITION_N);
		sql.append(" AND CalendarDay.calendarType = :calendarType ");
		sql.append("AND CalendarDay.calendarId = :calendarId ");
		sql.append("AND CalendarDay.year = :year ");
		logger.debug(sql);
		try {
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", sc.getOrgRrn());
			query.setParameter("calendarType", calendarSetup.getCalendarType());
			query.setParameter("calendarId", calendarSetup.getCalendarId());
			if (calendarSetup.getYear() == null || "".equals(calendarSetup.getYear())) { // 因为Year不是在页面文本框中不一定要填的,默认为本年
				Date d = new Date();
				query.setParameter("year", ((Integer) (d.getYear() + 1900)).toString());
				year = d.getYear() + 1900;
			} else {
				query.setParameter("year", calendarSetup.getYear());
				year = Integer.parseInt(calendarSetup.getYear());
			}
			int result = query.executeUpdate();

			// 生成日历
			if (isLeapYear(year)) {
				dayCount = 366;
			}
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			/* 根据年份和天数获得该天的日期 */
			Calendar calendar = Calendar.getInstance();
			calendar.setFirstDayOfWeek((int) calendarSetup.getFirstDayOfWeek());
			for (int i = 1; i <= dayCount; i++) {
				calendar.set(Calendar.YEAR, year);
				calendar.set(Calendar.DAY_OF_YEAR, i);

				CalendarDay calendarDay = new CalendarDay();
				calendarDay.setIsActive(true);
				calendarDay.setCreated(new Date());
				calendarDay.setCreatedBy(sc.getUserName());
				calendarDay.setOrgRrn(sc.getOrgRrn());
				calendarDay.setUpdated(new Date());
				calendarDay.setUpdatedBy(sc.getUserName());

				calendarDay.setCalendarId(calendarSetup.getCalendarId());
				calendarDay.setCalendarType(calendarSetup.getCalendarType());
				calendarDay.setSysDate(dateFormat.parse(dateFormat
						.format(calendar.getTime())));
				calendarDay.setQuarter(getCurrentQuarter(calendar));
				calendarDay.setYear(((Integer) year).toString());
				calendarDay.setMonth(String.valueOf(calendar.get(Calendar.MONTH) + 1));
				calendarDay.setWeek(String.valueOf(calendar.get(Calendar.WEEK_OF_YEAR)));
				calendarDay.setWeekDay(String.valueOf(calendar.get(Calendar.DAY_OF_WEEK)));
				if (calendar.get(Calendar.DAY_OF_WEEK) == 1) {
					calendarDay.setIsHoliday(calendarSetup.getSunday());
				} else if (calendar.get(Calendar.DAY_OF_WEEK) == 2) {
					calendarDay.setIsHoliday(calendarSetup.getMonday());
				} else if (calendar.get(Calendar.DAY_OF_WEEK) == 3) {
					calendarDay.setIsHoliday(calendarSetup.getTuesday());
				} else if (calendar.get(Calendar.DAY_OF_WEEK) == 4) {
					calendarDay.setIsHoliday(calendarSetup.getWednesday());
				} else if (calendar.get(Calendar.DAY_OF_WEEK) == 5) {
					calendarDay.setIsHoliday(calendarSetup.getThursday());
				} else if (calendar.get(Calendar.DAY_OF_WEEK) == 6) {
					calendarDay.setIsHoliday(calendarSetup.getFriday());
				} else if (calendar.get(Calendar.DAY_OF_WEEK) == 7) {
					calendarDay.setIsHoliday(calendarSetup.getSaturday());
				}
				em.persist(calendarDay);
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
     * 查询工作日历
     * @param year 年份
     * @param month 月份
     * @param calendarType 工作日历类型
     * @param calendarId 工作日历id
     * @throws ClientException
     */
	public List<CalendarDay> selectCalendarDay(int year, int month,
			String calendarType, String calendarId, SessionContext sc)
			throws ClientException {
		List<CalendarDay> calendarDays = new ArrayList<CalendarDay>();
		try {
			if (year != 0 && month != 0) {
				SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
				/* 获得某月的第一天 */
				Calendar calendar = Calendar.getInstance();
				calendar.set(Calendar.YEAR, year);
				calendar.set(Calendar.MONTH, month - 1);
				calendar.set(Calendar.DATE, 1);
				Date first = calendar.getTime();
				Date firstDate = dateFormat.parse(dateFormat.format(first));
				/* 获得下月的第一天 */
				calendar.set(Calendar.YEAR, year);
				calendar.set(Calendar.MONTH, month);
				calendar.set(Calendar.DATE, 1);
				Date last = calendar.getTime();
				Date lastDate = dateFormat.parse(dateFormat.format(last));

				StringBuffer sql1 = new StringBuffer();
				sql1.append("SELECT CalendarDay FROM CalendarDay as CalendarDay ");

				if (calendarId == null) { // 单击进入工作日历详细信息页面时，显示本月为日历类型为工厂类型的记录。因为工厂类型只会有一个日历编号。
					sql1.append(" WHERE (sysDate < :lastDate AND sysDate >= :firstDate) AND calendarType = :calendarType AND ");
					sql1.append(ADBase.BASE_CONDITION_N);
					Query query = em.createQuery(sql1.toString());
					query.setParameter("lastDate", lastDate);
					query.setParameter("firstDate", firstDate);
					query.setParameter("calendarType", calendarType);
					query.setParameter("orgRrn", sc.getOrgRrn());
					calendarDays = query.getResultList();
				} else {
					sql1.append(" WHERE (sysDate < :lastDate AND sysDate >= :firstDate) AND calendarType = :calendarType AND calendarId = :calendarId AND ");
					sql1.append(ADBase.BASE_CONDITION_N);
					Query query = em.createQuery(sql1.toString());
					query.setParameter("lastDate", lastDate);
					query.setParameter("firstDate", firstDate);
					query.setParameter("calendarType", calendarType);
					query.setParameter("calendarId", calendarId);
					query.setParameter("orgRrn", sc.getOrgRrn());
					calendarDays = query.getResultList();
				}
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
		return calendarDays;
	}
	
	/**
     * 批量保存或更改工作日历
     * @param orgRrn 区域号
     * @param calendarDays 工作日历列表
     * @throws ClientException
     */
	public void saveCalendarDay(long orgRrn, List<CalendarDay> calendarDays, SessionContext sc)throws ClientException {
		try {
			for (int i = 0; i < calendarDays.size(); i++) {
				CalendarDay calendarDay = calendarDays.get(i);
				if (calendarDay != null && calendarDay.getObjectRrn() != null) {
					calendarDay.setUpdated(new Date());
					calendarDay.setUpdatedBy(sc.getUserName());
					em.merge(calendarDay);					
				} else {
					calendarDay.setOrgRrn(orgRrn);
					calendarDay.setIsActive(true);
					calendarDay.setCreatedBy(sc.getUserName());
					calendarDay.setCreated(new Date());
					calendarDay.setUpdated(new Date());
					calendarDay.setUpdatedBy(sc.getUserName());
				//	calendarDay.setCalendarType(calendarType);
				//	calendarDay.setCalendarId(calendarId);
					em.persist(calendarDay);
				}
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * 获得以天为时间为单位的日历
	 * @param orgRrn 区域号
     * @param calendarId 工作日id
     * @throws ClientException
	 */
	public BusinessCalendar getCalendarByDay(long orgRrn, String calendarId) throws ClientException {
		try {
			String whereClause = " isHoliday = 'Y' AND calendarId = '" + calendarId + "'";
			List<CalendarDay> holidays = adManager.getEntityList(orgRrn, CalendarDay.class, Integer.MAX_VALUE, whereClause, "");
			
			List<CalendarHour> hours = CalendarHour.getDefaultHours();
			
			BusinessCalendar businessCalendar = new BusinessCalendar(holidays, hours, Duration.BUSINESS_DAYS);
			
			return businessCalendar;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 获得以分钟为时间为单位的日历
	 * @param orgRrn 区域号
     * @param calendarId 工作日id
     * @throws ClientException
	 */
	public BusinessCalendar getCalendarByMinute(long orgRrn, String calendarId) throws ClientException {
		try {
			String whereClause = " isHoliday = 'Y' AND calendarId = '" + calendarId + "'";
			List<CalendarDay> holidays = adManager.getEntityList(orgRrn, CalendarDay.class, Integer.MAX_VALUE, whereClause, "");
			
			whereClause = " calendarId = '" + calendarId + "'";
			List<CalendarHour> hours = adManager.getEntityList(orgRrn, CalendarHour.class, Integer.MAX_VALUE, whereClause, "");
			if (hours == null || hours.size() == 0) {
				hours = CalendarHour.getDefaultHours();
			}
			BusinessCalendar businessCalendar = new BusinessCalendar(holidays, hours, Duration.BUSINESS_MINUTES);
			return businessCalendar;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	

	
	/**
	 * 输入年份，判断该年是平年还是闰年。
	 * 
	 * @param year 年份
	 */
	public boolean isLeapYear(int year) {
		return (year % 4 == 0) && ((year % 100 != 0) || (year % 400 == 0));
	}

	/**
	 * 获得指定日期是一年中的哪个季度
	 * 
	 * @param calendar
	 * @return
	 */
	public String getCurrentQuarter(Calendar calendar) {
		if (calendar.get(Calendar.MONTH) + 1 <= 3) {
			return "1";
		} else if (calendar.get(Calendar.MONTH) + 1 <= 6) {
			return "2";
		} else if (calendar.get(Calendar.MONTH) + 1 <= 9) {
			return "3";
		} else {
			return "4";
		}
	}
	
	/**
	* 获得在参考表中的字符定义
	* @param orgRrn
	* @param formatCodeRefName 对应的参考表名称
	*             参考表格式如： key     text
	*                            A        0
	*                            B        1
	*                            C        2  
	*                            D        3
	* @return 如[A][B][C][D]...
	*/ 
	private char[] getDigits(long orgRrn, String formatCodeRefName) throws ClientException {
		try {
			List<ADRefList> formatCodes = adManager.getADRefList(orgRrn, formatCodeRefName);
			if (formatCodes != null && formatCodes.size() > 0) {
				int max = 0;
				int[] codes = new int[formatCodes.size()];
				for (int i = 0; i < formatCodes.size(); i++) {
					int v = Integer.valueOf(formatCodes.get(i).getText()).intValue();
					if (v > max) {
						max = v;
					}
					codes[i] = v;
				}

				//初始化digits [0][1][2]...
				char[] digits = new char[max + 1];
				for (int i = 0; i <= max; i++) {
					digits[i] = String.valueOf(i).charAt(0);
				}

				for (int i = 0; i < digits.length; i++) {
					for (int j = 0; j < codes.length; j++) {
						if (i == codes[j]) {
							//使用RefList中定义的码取代默认值
							digits[i] = formatCodes.get(j).getKey().charAt(0);
							break;
						}
					}
				}
				return digits;
			}
			return new char[] {};
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 将数字转化为对应的字符串
	* @param orgRrn
	* @param n 待转换的数字
	* @param formatCodeRefName 对应的参考表名称
	* @return
	*/ 
	public char[] transformDigit(long orgRrn, int n, String formatCodeRefName) throws ClientException{
		try {
			//目标进制 从0开始按顺序替换，数组中的第一个字符代表十进制中的0，第二个字符代表十进制中的1，依次类推
			char[] digits = getDigits(orgRrn, formatCodeRefName);
			
			//获得最大进制
			int max = digits.length - 1;
			
			//对数值进行进制转换,如max=16,则将数值按16进制进行转换
			int n1 = n;
			List<Integer> remainderList = new Vector<Integer>();
			do {
				remainderList.add(n1 % max);
				n1 = n1 / max;
			} while (n1 / max > max);
			remainderList.add(n1);
			int[] remainder = new int[remainderList.size()];
			for (int i = 0; i < remainder.length; i++) {
				remainder[i] = remainderList.get(i);
			}
			
			//转换成对应的字符输入
			char[] output = new char[remainder.length];
			for (int i = remainder.length - 1; i >= 0; i--) {
				//倒序输出
				output[remainder.length - 1 - i] = digits[remainder[i]];
			}
			return output;
		} catch (ClientException e) {
			throw e;
		}
	}
	
	/**
	 * 新增或更新ID生成规则明细
	 * @param rule id生成规则
     * @param line 规则明细
     * @throws ClientException
	 */
	public GeneratorRule addRuleLine(GeneratorRule rule, GeneratorRuleLine line, SessionContext context) throws ClientException {
		try{
			if(rule.getObjectRrn() == null){
				rule = (GeneratorRule) adManager.saveEntity(rule, context);
			}
			line.setRuleRrn(rule.getObjectRrn());
			List<GeneratorRuleLine> lines = rule.getRuleLines();
			if (line.getObjectRrn() == null){
				line.setSeqNo((long)(lines.size()+1));
			}
			line = (GeneratorRuleLine) adManager.saveEntity(line, context);
			return em.find(rule.getClass(), rule.getObjectRrn());
		} catch(Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * 删除ID生成规则明细
	 * @param line id生成规则
     * @param context 会话上下文
     * @throws ClientException
	 */
	public void deleteRuleLine(GeneratorRuleLine line, SessionContext context) throws ClientException {
		try{
			GeneratorRule rule = line.getRule();
			
			List<GeneratorRuleLine> lines = rule.getRuleLines();
			int lineIndex = lines.indexOf(line);
			List<GeneratorRuleLine> belowLines = lines.subList(lineIndex + 1, lines.size());
			for (GeneratorRuleLine rl : belowLines){
				rl.setSeqNo(rl.getSeqNo()-1);
				em.merge(rl);
			}
			rule = em.find(GeneratorRule.class, rule.getObjectRrn());
			
			rule.getRuleLines().remove(line);
			
			em.merge(rule);
		} catch(Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	public GeneratorRule saveRuleLines(GeneratorRule rule, List<GeneratorRuleLine> lines, SessionContext context)
			throws ClientException {
		try{
			//TODO 此方法留空
			return null;
		} catch(Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * 修改ID生成规则明细
	 * @param oldLine id生成规则
     * @param newLine 规则明细
     * @throws ClientException
	 */
	public GeneratorRule updateRuleLine(GeneratorRuleLine oldLine, GeneratorRuleLine newLine, SessionContext context)
			throws ClientException {
		try {
			GeneratorRule rule = oldLine.getRule();
			newLine.setObjectRrn(oldLine.getObjectRrn());
			newLine.setSeqNo(oldLine.getSeqNo());
			newLine = (GeneratorRuleLine) adManager.saveEntity(newLine, context);
			return em.find(rule.getClass(), rule.getObjectRrn());
		} catch(Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * id生成规则明细项位置变更
	 * @param line 规则明细项
     * @param offSet 变更位置
     * @throws ClientException
	 */
	public GeneratorRule moveRuleLine(GeneratorRuleLine line, int offSet, SessionContext context) throws ClientException {
		try{
			GeneratorRule rule = line.getRule();
			List<GeneratorRuleLine> lines = rule.getRuleLines();
			int index = lines.indexOf(line);
			int nearlyIndex = index + offSet;
			if (nearlyIndex >= 0 && nearlyIndex < lines.size()){
				GeneratorRuleLine nearlyLine = lines.get(nearlyIndex);
				long tempSeqNo = nearlyLine.getSeqNo();
				nearlyLine.setSeqNo(line.getSeqNo());
				line.setSeqNo(tempSeqNo);
				em.merge(line);
				em.merge(nearlyLine);
			}
			rule = em.find(rule.getClass(), rule.getObjectRrn());
			rule.getRuleLines().size();
			return rule;
		} catch(Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	* InActive GeneratorRule
	* <p><br>
	* @param generatorRule 要InActive的GeneratorRule
	* @param sc 会话上下文
	* @return
	*/ 
	public GeneratorRule inActiveGeneratorRule(GeneratorRule generatorRule, SessionContext sc) throws ClientException {
		try {
			//先检查默认规则
			Map<String, String> resultMap = new HashMap<String, String>();
			resultMap.put(GeneratorContext.CONTEXT_RESULT_FIELD_RULE_NAME, generatorRule.getName());
			
			List<ContextValue> contextValues = ctxManager.getContextValuesByResult(sc.getOrgRrn(), GeneratorContext.CONTEXT_NAME_IDGENERATOR, resultMap, false, false);
			if (contextValues != null && contextValues.size() > 0) {
				throw MBasExceptionBundle.bundle.GeneratorRuleIsUsed();
			}
			//再检查其它特殊规则
			List<ADRefList> refList = adManager.getADRefList(sc.getOrgRrn(), GeneratorContext.SYSREF_IDGENERATOR_CONTEXT);
			for (ADRefList ref : refList) {
				resultMap.put(GeneratorContext.CONTEXT_RESULT_FIELD_RULE_NAME, ref.getText());
				
				contextValues = ctxManager.getContextValuesByResult(sc.getOrgRrn(), GeneratorContext.CONTEXT_NAME_IDGENERATOR, resultMap, false, false);
				if (contextValues != null && contextValues.size() > 0) {
					throw MBasExceptionBundle.bundle.GeneratorRuleIsUsed();
				}
			}
			
			generatorRule.setUpdatedBy(sc.getUserName());
			generatorRule.setStatus(VersionControl.STATUS_INACTIVE);
			generatorRule = em.merge(generatorRule);
			return generatorRule;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	public List<?> executeSql(String sql) throws ClientException {
		try{
			Query query = em.createNativeQuery(sql);
			return query.getResultList();
		} catch(Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	public int executeUpdateSql(String sql) throws ClientException {
		try{
			Query query = em.createNativeQuery(sql);
			return query.executeUpdate();
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 按名字获取GeneratorRule
	 * @param orgRrn 区域
	 * @param name 名称
	 * @param throwException 是否抛出异常
	 * @return
	 * @throws ClientException
	 */
	public GeneratorRule getGeneratorRuleByName(long orgRrn, String name, boolean throwException) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("SELECT GeneratorRule FROM GeneratorRule GeneratorRule ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND name = :name ");
			sql.append(" AND status = 'Active' ");			
					
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("name", name);
			
			List<GeneratorRule> generatorRules = query.getResultList();
			if (CollectionUtils.isNotEmpty(generatorRules)) {
				GeneratorRule generatorRule = generatorRules.get(0);
				if (generatorRule.getRuleLines() != null) {
					generatorRule.getRuleLines().size();
				}
				return generatorRule;
			} else {
				if (throwException) {
					throw MBasExceptionBundle.bundle.IdGeneratorRuleNotFound();
				} else {
					return null;
				}
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}	
	}
	
	/**
	 * 根据Context获取ID生成规则单个
	 * @param orgRrn 区域号
     * @param context 序号生成上下文
	 */
	@TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
	public String generatorId(long orgRrn, GeneratorContext context) throws ClientException {
		return generatorId(orgRrn, context, true);
	}
	
	/**
	 * 根据Context获取ID生成规则单个
	 * @param orgRrn 区域号
     * @param context 序号生成上下文
     * @param throwException 是否抛出异常
     * @throws ClientException
	 */
	@TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
	public String generatorId(long orgRrn, GeneratorContext context, boolean throwException) throws ClientException {
		try {			
			GeneratorRule rule = getGeneratorRule(orgRrn, context, throwException);
			if (rule == null) {
				if (throwException) {
					Map<String, Object> parameterMap = context.getParameterMap();
					throw MBasExceptionBundle.bundle.IdGenerateRuleNotFound(String.valueOf(parameterMap.get(GeneratorContext.CONTEXT_FIELD_TRANSACTION)));
				} else {
					return "";
				}
			}
			context.setGeneratorRule(rule);
			List<String> ids = generatorId(orgRrn, rule, context);
			return ids.get(0);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据Context批量获取ID生成规则
	 * @param orgRrn 区域号
     * @param context 序号生成上下文
     * @param throwException 是否抛出异常
     * @throws ClientException
	 */
	@TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
	public List<String> batchGeneratorId(long orgRrn, GeneratorContext context, boolean throwException) throws ClientException {
		try {			
			GeneratorRule rule = getGeneratorRule(orgRrn, context, throwException);
			if (rule == null) {
				if (throwException) {
					throw MBasExceptionBundle.bundle.IdGeneratorRuleNotFound();
				} else {
					return null;
				}
			}
			context.setGeneratorRule(rule);
			List<String> ids = generatorId(orgRrn, rule, context);
			return ids;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 批量，根据Context获取ID生成规则
	 * @param orgRrn 区域号
	 * @param rule 序号生成规则
     * @param context 序号生成上下文
	 */
	public List<String> generatorId(long orgRrn, GeneratorRule rule, GeneratorContext context) throws ClientException {
		return generatorId(orgRrn, rule, false, context);
	}
	
	/**
	 * 批量生成序号
	 * @param orgRrn 区域号
	 * @param rule 序号生成规则
	 * @param isParameterList 是否使用参数List处理批量生成序号
	 * @param context 序号生成上下文
	 * 
	 * @return 生成的序号
	 */
	public List<String> generatorId(long orgRrn, GeneratorRule rule, boolean isParameterList, GeneratorContext context) throws ClientException {
		try {			
			if (rule == null) {
				throw MBasExceptionBundle.bundle.IdGeneratorRuleNotFound();
			}
			context.setADManager(adManager);
			context.setMBASManager(this);
			
			for (IIdGeneratorCdiAction action : idGeneratorActions) {
				if (action.isSupport(rule, context)) {
					context = action.buildGeneratorContext(rule, context);
					if (context.getGeneratorRule() != null) {
						// 支持替换rule
						rule = context.getGeneratorRule();
					}
					
					if (action.isSupportGeneratorId(rule, context)) {
						//只应该有一个规则生效
						return action.generatorId(rule, isParameterList, context);
					}
				}
			}
			
			List<String> ids = new LinkedList<String>();
			List<GeneratorRuleLine> ruleLines = rule.getRuleLines();
			List<GeneratorRuleLine> sequenceRuleLines = ruleLines.stream().filter(p -> p instanceof SequenceRuleLine).collect(Collectors.toList());
			
			if (context.isBatch()) {
				if (isParameterList) {
					//不支持同时使用
					throw MBasExceptionBundle.bundle.IdGeneratorUnSupportParamListInBatch();
				}
				if (sequenceRuleLines.size() > 1) {
					//不支持同时使用
					throw MBasExceptionBundle.bundle.IdGeneratorUnSupportMultiseqInBatch();
				}
				String[] batchIds = null;
				for (GeneratorRuleLine rl : ruleLines){
					if (rl instanceof SequenceRuleLine) {
						if (batchIds != null) {
							throw MBasExceptionBundle.bundle.IdGeneratorUnSupportMultiseqInBatch();
						}
						batchIds = ((SequenceRuleLine)rl).getIdBatch(context, context.getIdNum());
						context.addIdSegments(GeneratorContext.BATCH_SEQ_HOLDER);
					} else {
						context.addIdSegments(rl.getId(context));
					}
				}
				if (batchIds != null) {
					for (String batchId : batchIds){
						String idPrefix = "";
						for (String idSegment : context.getIdSegments()) {
							if (GeneratorContext.BATCH_SEQ_HOLDER.equals(idSegment)) {
								idPrefix += batchId;
							} else {
								idPrefix += idSegment;
							}
						}
						ids.add(idPrefix);
					}
				}
				context.getIdSegments().clear();
			} else {			
				for (int i = 0; i < context.getIdNum(); i++){
					if (isParameterList) {
						context.setCurrentIdNum(i);
					}
					for (GeneratorRuleLine rl : ruleLines){
						if (rl instanceof SequenceRuleLine) {
							Optional<GeneratorRuleLine> optGeneratorRuleLine = ruleLines.stream().filter(
									p -> p.getSeqNo() > rl.getSeqNo() && p instanceof SequenceRuleLine).findFirst();
							if (optGeneratorRuleLine.isPresent()) {
								((SequenceRuleLine)rl).setNextSequenceRule((SequenceRuleLine)optGeneratorRuleLine.get());
								//获取两个SequenceRuleLine中间的RuleLine
								List<GeneratorRuleLine> behindGeneratorRules =  ruleLines.stream().filter(
										p -> p.getSeqNo() > rl.getSeqNo() && p.getSeqNo() < optGeneratorRuleLine.get().getSeqNo()).collect(Collectors.toList());
								((SequenceRuleLine)rl).setBehindGeneratorRules(behindGeneratorRules);
							}						
						}
						
						context.addIdSegments(rl.getId(context));
					}
					String idPrefix = context.getIdPrefix();
					context.getIdSegments().clear();
					ids.add(idPrefix);
				}
			}
			
			for (IIdGeneratorCdiAction action : idGeneratorActions) {
				if (action.isSupport(rule, context)) {
					//只应该有一个规则生效
					ids = action.postGenerateId(ids, rule, isParameterList, context);
				}
			}
			return ids;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	
	
	/**
	 * 根据GeneratorContext获得对应Generator
	 * 1,根据系统栏位参考表IDGeneratorContext,找到此ObjectType对应的Context(如果没有则使用默认的Context(IDGENERATOR))
	 * 2,根据Context找到对应的ContextValue,从而找到对应的
	 */
	public GeneratorRule getGeneratorRule(long orgRrn, GeneratorContext gContext, boolean throwException) throws ClientException{
		try {
			//检查有没有对应的Context
			if (gContext.getGeneratorRule() != null) {
				return gContext.getGeneratorRule();
			}
			List<ADRefList> refList = adManager.getADRefList(orgRrn, GeneratorContext.SYSREF_IDGENERATOR_CONTEXT);
			String contextName = null;
			for (ADRefList ref : refList) {
				if (gContext.getObjectType().equals(ref.getKey())) {
					contextName = ref.getText();
				}
			}
			if (StringUtil.isEmpty(contextName)) {
				contextName = GeneratorContext.CONTEXT_NAME_IDGENERATOR;
			}
			Map<String, String> contextMap = new HashMap<String, String>();
			contextMap.put(GeneratorContext.CONTEXT_FIELD_OBJECT_TYPE, gContext.getObjectType());
			for (String key : gContext.getParameterMap().keySet()) {
				contextMap.put(key, DBUtil.toString(gContext.getParameterMap().get(key)));
			}
		
			List<Map<String, String>> contextValuesMap = ctxManager.getContextValuesMap(orgRrn, contextName, contextMap);
			if (contextValuesMap.size() == 0 && throwException) {
				throw MBasExceptionBundle.bundle.IdGeneratorContextNotFound();
			}
			for (Map<String, String> contextValueMap : contextValuesMap) {
				String idGeneratorRuleName = contextValueMap.get(GeneratorContext.CONTEXT_RESULT_FIELD_RULE_NAME);
				List<GeneratorRule> rules = adManager.getEntityList(orgRrn, GeneratorRule.class, 1, " name = '" + idGeneratorRuleName + "'", "");
				if (rules.size() > 0) {
					GeneratorRule rule = rules.get(0);
					gContext.setGeneratorRule(rule);
					return rule;
				} else if (throwException) {
					throw MBasExceptionBundle.bundle.IdGenerateRuleNotFound(idGeneratorRuleName);
				}
			}
			
			return null;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据对应的Generator及ADSequence的名字
	 * 获得所对应的下一个Sequence值
	 * 如果没有则创建一个新的记录
	 * (采用new Transaction方式)
	 * 
	 * @param orgRrn 所对应的区域
	 * @param generateRrn 所对应的Generator的ObjectRrn
	 * @param name ADSequence的名字
	 * @param count 所需要获得Sequence数量
	 */
	public int[] getNextSequenceValue(long orgRrn, long generateRrn, String name, int count) throws ClientException {
		return getNextSequenceValue(orgRrn, generateRrn, name, count, 0, true);
	}
	
	/**
	 * 根据对应的Generator及ADSequence的名字
	 * 获得所对应的下一个Sequence值
	 * 如果没有则创建一个新的记录
	 * (采用new Transaction方式)
	 * 
	 * @param orgRrn 所对应的区域
	 * @param generateRrn 所对应的Generator的ObjectRrn
	 * @param name ADSequence的名字
	 * @param count 所需要获得Sequence数量
	 * @param minValue 最小Seq值,所返回的Seq必须大于等于minValue
	 */
	public int[] getNextSequenceValue(long orgRrn, long generateRrn, String name, int count, int minValue) throws ClientException {
		return getNextSequenceValue(orgRrn, generateRrn, name, count, minValue, true);
	}
	
	/**
	 * 根据对应的Generator及ADSequence的名字
	 * 获得所对应的下一个Sequence值
	 * 如果没有则创建一个新的记录
	 * 
	 * @param orgRrn 所对应的区域
	 * @param generateRrn 所对应的Generator的ObjectRrn
	 * @param name ADSequence的名字
	 * @param count 所需要获得Sequence数量
	 * @param minValue 最小Seq值,所返回的Seq必须大于等于minValue
	 * @param newTrans 是否在新的事务中创建Sequence(默认为ture),防止并发锁
	 */
	public int[] getNextSequenceValue(long orgRrn, long generateRrn, String name, int count, int minValue, boolean newTrans) throws ClientException{
		if (newTrans) {
			return sequenceManager.getNextSequenceValueNewTrans(orgRrn, generateRrn, name, count, minValue);
		} else {
			return getNextSequenceValueOldTrans(orgRrn, generateRrn, name, count, minValue);
		}
	}

	private int[] getNextSequenceValueOldTrans(long orgRrn, long generateRrn, String name, int count, int minValue) throws ClientException{
		StringBuffer sql = new StringBuffer(" SELECT ADSequence FROM ADSequence ADSequence ");
		sql.append(" WHERE ");
		sql.append(ADBase.BASE_CONDITION_N);
		sql.append(" AND name = :name ");
		sql.append(" AND generatorRrn = :generatorRrn ");

		try {
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("name", name);
			query.setParameter("generatorRrn", generateRrn);

			ADSequence seqence;
			List<ADSequence> seqences = (List<ADSequence>)query.getResultList();
			if (seqences.size() == 0) {
				seqence = sequenceManager.createNewSequence(orgRrn, name, generateRrn, minValue);
				if (seqence == null) {
					seqences = (List<ADSequence>)query.getResultList();
					if (seqences.size() == 0) {
						throw MBasExceptionBundle.bundle.GetAdSequenceError();
					} else {
						seqence = seqences.get(0);
					}
				}
			} else {
				seqence = seqences.get(0);
			}
			if (!em.contains(seqence)) {
				seqence = em.find(ADSequence.class, seqence.getObjectRrn(), LockModeType.PESSIMISTIC_WRITE);
			} else {
				em.refresh(seqence, LockModeType.PESSIMISTIC_WRITE);
			}
			
			int seqValue = seqence.getNextSeq().intValue();
			if (seqValue < minValue) {
				seqValue = minValue;
			}
			seqence.setNextSeq((long)(seqValue + count));
			int[] vals = new int[count];
			for (int i = 0; i < count; i++){
				vals[i] = seqValue + i;
			}
			em.merge(seqence);
			return vals;
		} catch(Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 获得Sequence的当前值
	 * @param orgRrn 区域号
	 * @param generateRrn 规则主键
	 * @param name 名称
	 */
	public Long getCurrentSequenceValue(long orgRrn, long generateRrn, String name) throws ClientException{
		StringBuffer sql = new StringBuffer(" SELECT ADSequence FROM ADSequence ADSequence ");
		sql.append(" WHERE ");
		sql.append(ADBase.BASE_CONDITION_N);
		sql.append(" AND name = :name ");
		sql.append(" AND generatorRrn = :generatorRrn ");

		try {
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("name", name);
			query.setParameter("generatorRrn", generateRrn);

			ADSequence seqence;
			List<ADSequence> seqences = (List<ADSequence>)query.getResultList();
			if (seqences.size() == 0) {
				return null;
			} else {
				seqence = seqences.get(0);
				return seqence.getNextSeq();
			}
		} catch(Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	} 
	
	/**
	* 根据ID获得对应的Location
	* @param orgRrn 区域号
	* @param locationId
	*/ 
	public Location getLocationById(long orgRrn, String locationId) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" SELECT Location FROM Location Location ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND name = :name ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("name", locationId);
			List<Location> locations = query.getResultList();
			if (locations == null || locations.size() == 0) {
				return null;
			}
			return locations.get(0);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 根据Location组,获得线别对应的具体Location
	* @param orgRrn 区域号
	* @param locationRrn location主键
	* @param lineId 线别
	*/ 
	public Location getLocationByLineId(long orgRrn, long locationRrn, String lineId) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" SELECT LocationLine FROM LocationLine LocationLine ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND groupLocatoinRrn = :groupLocatoinRrn ");
			sql.append(" AND lineId = :lineId ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("groupLocatoinRrn", locationRrn);
			query.setParameter("lineId", lineId);
			List<LocationLine> locationLines = query.getResultList();
			if (locationLines == null || locationLines.size() == 0) {
				return null;
			}
			LocationLine locationLine = locationLines.get(0);
			return em.find(Location.class, locationLine.getLocationRrn());
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 根据线别主键,获得线别对应用户
	* @param lineRrn 线别主键
	*/ 
	public List<ADUser> getLineUsers(long lineRrn) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("SELECT ADUser FROM ADUser ADUser, ");
			sql.append(" LineUser LineUser ");
			sql.append(" WHERE LineUser.userRrn = ADUser.objectRrn ");
			sql.append(" AND LineUser.lineRrn = :lineRrn ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("lineRrn", lineRrn);
			return (List<ADUser>)query.getResultList();
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 保存合批规则
	 * @param mergerRule 合批规则主键
	 * @param sc 上下文
	 */
	public MergeRule saveMergeRule(MergeRule mergerRule, SessionContext sc) throws ClientException {
		try {
			 //如果存在这个合批规则就先删除，然后再重新插入
			if (mergerRule.getObjectRrn() != null) {
				List<MergeRuleLine> mergeRuleLines = adManager.getEntityList(sc.getOrgRrn(), MergeRuleLine.class, 10, 
						" mergeRuleRrn = " + mergerRule.getObjectRrn(), "");
				for (MergeRuleLine mergeRuleLine : mergeRuleLines) {
					mergeRuleLine = em.find(MergeRuleLine.class, mergeRuleLine.getObjectRrn());
					em.remove(mergeRuleLine);
				}
				mergerRule.setUpdatedBy(sc.getUserName());
				mergerRule = em.merge(mergerRule);
			} else {
				if (StringUtil.isEmpty(mergerRule.getStatus())) {
					mergerRule.setStatus(VersionControl.STATUS_UNFROZNE);
				}		
				mergerRule.setCreatedBy(sc.getUserName());
				mergerRule.setUpdatedBy(sc.getUserName());
				em.persist(mergerRule);
			}					
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
		return mergerRule;
	}
	
	/**
	* InActive MergeRule
	* <p><br>
	* @param mergerRule 要InActive的MergeRule
	* @param sc 会话上下文
	* @return
	*/ 
	public MergeRule inActiveMergeRule(MergeRule mergerRule, SessionContext sc) throws ClientException {
		try {
			Map<String, String> resultMap = new HashMap<String, String>();
			resultMap.put(MergeRuleContext.CONTEXT_RESULT_MERGERULE_NAME, mergerRule.getName());
			resultMap.put(MergeRuleContext.CONTEXT_RESULT_BATCHRULE_NAME, mergerRule.getName());
			resultMap.put(MergeRuleContext.CONTEXT_RESULT_MLOT_MERGERULE_NAME, mergerRule.getName());
			
			List<ContextValue> contextValues = ctxManager.getContextValuesByResult(sc.getOrgRrn(), mergerRule.getCategory(), resultMap, false, false);
			if (contextValues != null && contextValues.size() > 0) {
				throw MBasExceptionBundle.bundle.MergeRuleIsUsed();
			}
			mergerRule.setUpdatedBy(sc.getUserName());
			mergerRule.setStatus(VersionControl.STATUS_INACTIVE);
			mergerRule = em.merge(mergerRule);
			return mergerRule;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * 根据名称和类型获取合批规则，同时获取MergeRuleLine与变量实体
	 * 这里采用左边接是因为MergeRuleLine.variableName可能为空
	 * @param name 合批规则名称
	 * @param category		合批规则类型
	 * @param isVariable 	是否同时获取变量
	 * @return
	 * @throws ClientException
	 */	
	public MergeRule getMergeRule(String name, String category, boolean isVariable, SessionContext sc) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("SELECT MergeRule FROM MergeRule MergeRule ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND name = :name ");
			sql.append(" AND status = 'Active' ");
			if (!StringUtil.isEmpty(category)) {
				sql.append(" AND category = :category ");
			}
					
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", sc.getOrgRrn());
			query.setParameter("name", name);
			if (!StringUtil.isEmpty(category)) {
				query.setParameter("category", category);
			}
			List<MergeRule> mergeRules = query.getResultList();
			if (CollectionUtils.isNotEmpty(mergeRules)) {
				MergeRule mergeRule = mergeRules.get(0);
				mergeRule.getMergeRuleLines().size();
				
				if (isVariable) {
					List<MergeRuleLine> mergeRuleLineVariables = getMergeRuleLineVariable(mergeRule.getObjectRrn());
					
					for (MergeRuleLine line : mergeRule.getMergeRuleLines()) {
						if (!StringUtil.isEmpty(line.getVariableName())) {
							List<MergeRuleLine> filterLineVariables = mergeRuleLineVariables.stream().filter(
									p -> p.getObjectRrn().equals(line.getObjectRrn())).collect(Collectors.toList());
							if (CollectionUtils.isNotEmpty(filterLineVariables)) {
								line.setVariable(filterLineVariables.get(0).getVariable());
							}							
						}
					}
				}
				
				return mergeRule;
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
		return null;
	}
	
	
	/**
	 * 同时获取MergeRuleLine与变量实体，这里采用左边接是因为MergeRuleLine.variableName可能为空
	 * @param mergeRuleRrn
	 * @return
	 * @throws ClientException
	 */
	public List<MergeRuleLine> getMergeRuleLineVariable(long mergeRuleRrn) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("SELECT MergeRuleLine, ADVariable FROM MergeRuleLine MergeRuleLine ");
			sql.append(" LEFT JOIN ");
			sql.append(" ADVariable ADVariable ");
			sql.append(" ON MergeRuleLine.orgRrn = ADVariable.orgRrn ");
			sql.append(" AND MergeRuleLine.variableName = ADVariable.name ");
			sql.append(" WHERE MergeRuleLine.mergeRuleRrn = :mergeRuleRrn ");
					
			Query query = em.createQuery(sql.toString());
			query.setParameter("mergeRuleRrn", mergeRuleRrn);
			List<Object[]> objectsList = query.getResultList();

			List<MergeRuleLine> lines = Lists.newArrayList();
			if (objectsList != null && objectsList.size() > 0) {
				for (Object[] objects : objectsList) {
					MergeRuleLine line = (MergeRuleLine)objects[0];
					ADVariable variable = (ADVariable)objects[1];
					line.setVariable(variable);
					lines.add(line);
				}
			}
			return lines;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	@Override
	/**
	 * 保存line相关的User信息
	 * @param lineRrn 线别主键
	 * @param lineUsers 线别用户列表
	 * @param sc 上下文
	 */
	public void saveLineUsers(long lineRrn, List<LineUser> lineUsers,
			SessionContext sc) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" DELETE FROM LineUser LineUser ");
			sql.append(" WHERE ");
			sql.append(" lineRrn = :lineRrn ");
			Query query= em.createQuery(sql.toString());
			query.setParameter("lineRrn", lineRrn);
			query.executeUpdate();
			
			for (LineUser lineUser : lineUsers) {
				lineUser.setOrgRrn(sc.getOrgRrn());
				lineUser.setUpdatedBy(sc.getUserName());
				em.persist(lineUser);
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
		
	}
	
	/**
	 * 通过名称获取班组
	 * @param orgRrn 区域号
	 * @param teamName 班组名称
	 * @param exceptionFlag是否抛出异常
	 */
	@Override
	public Team getTeamByName(long orgRrn, String teamName, boolean exceptionFlag) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" SELECT Team FROM Team Team ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND name = :name ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("name", teamName);
			List<Team> teamList = query.getResultList();
			if (teamList == null || teamList.size() == 0) {
				if (!exceptionFlag) {
					return null;
				}
				throw MBasExceptionBundle.bundle.TeamNotFound();
			}
			
			//懒加载
			if(CollectionUtils.isNotEmpty(teamList.get(0).getTeamUser())) {
				teamList.get(0).getTeamUser().size();
			}
			
			Team Team = teamList.get(0);
			return Team;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 保存Team相关的User信息
	 * @param team team
	 * @param teamUsers Team相关的User列表
	 */
	@Override
	public void saveTeamUsers(Team team, List<TeamUser> teamUsers, SessionContext sc) throws ClientException {
		try {
			if(team.getObjectRrn() == null) {
				team.setCreated(new Date());
				team.setCreatedBy(sc.getUserName());
				team.setOrgRrn(sc.getOrgRrn());
			}else {
				team.setUpdated(new Date());
				team.setUpdatedBy(sc.getUserName());
			}
			
			StringBuffer sql = new StringBuffer(" DELETE FROM TeamUser TeamUser ");
			sql.append(" WHERE ");
			sql.append(" teamRrn = :teamRrn ");
			Query query= em.createQuery(sql.toString());
			query.setParameter("teamRrn", team.getObjectRrn());
			query.executeUpdate();
			
			for (TeamUser teamUser : teamUsers) {
				teamUser.setOrgRrn(sc.getOrgRrn());
				teamUser.setCreated(new Date());
				teamUser.setCreatedBy(sc.getUserName());
				teamUser.setUpdated(new Date());
				teamUser.setUpdatedBy(sc.getUserName());
				teamUser.setTeamId(team.getName());
				teamUser.setTeamRrn(team.getObjectRrn());
			}
			
			team.setTeamUser(teamUsers);
			team = em.merge(team);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	
	/**
	 * 
	 * 删除Team相关的User信息，包括本身
	 * @param team team
	 */
	@Override
	public void deleteTeamUsers(Team team, SessionContext sc) throws ClientException {
		try {
			//删除
			StringBuffer sql = new StringBuffer(" DELETE FROM TeamUser TeamUser ");
			sql.append(" WHERE ");
			sql.append(" teamRrn = :teamRrn ");
			Query query= em.createQuery(sql.toString());
			query.setParameter("teamRrn", team.getObjectRrn());
			query.executeUpdate();
			
			team = em.find(Team.class, team.getObjectRrn());
			em.remove(team);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 获得时间所对应的班次
	 * @param orgRrn
	 * @param currentDate 时间
	 */
	public ShiftTime getShiftTime(long orgRrn, Date currentDate) throws ClientException {
		try {
			int weekDay = CalendarDay.getWeekDay(currentDate);
			
			StringBuffer sql = new StringBuffer("SELECT ShiftTime FROM ShiftTime ShiftTime WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND weekDay = :weekDay");
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("weekDay", String.valueOf(weekDay));
			List<ShiftTime> shifts = query.getResultList();
			
			Instant instant = currentDate.toInstant();
			LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
			LocalTime localTime = localDateTime.toLocalTime();
								
			for (ShiftTime shiftTime : shifts) {
				if (shiftTime.isPartTime(localTime)) {
					return shiftTime;
				}
			}
			return null;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 保存对应班次班时信息
	 * @param shift 班次
	 * @param shiftTimeList 班时
	 * @param sc
	 */
	@Override
	public Shift saveShiftTime(Shift shift, List<ShiftTime> shiftTimeList, SessionContext sc) throws ClientException {
		try {
			if(shift.getObjectRrn() == null) {
				shift.setCreated(new Date());
				shift.setCreatedBy(sc.getUserName());
			}else {
				shift.setUpdated(new Date());
				shift.setUpdatedBy(sc.getUserName());
			}
			
			if(CollectionUtils.isNotEmpty(shiftTimeList)) {
				for(ShiftTime shiftTime : shiftTimeList) {
					em.merge(shiftTime);
				}
			}
			
			shift = em.merge(shift);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
		return shift;
	}
	
	/**
	 * 保存对应班次班时信息
	 * @param shift 班次
	 * @param sc 会话上下文
	 */
	@Override
	public void deleteShift(Shift shift, SessionContext sc) throws ClientException {
		try {
			if(shift != null && shift.getObjectRrn() != null) {
				//先删除之下的班时信息
				StringBuffer sql = new StringBuffer("DELETE FROM ShiftTime ShiftTime ");
				sql.append(" WHERE ");
				sql.append(ADBase.BASE_CONDITION_N);
				sql.append(" AND shiftId = :shiftId");
				Query query = em.createQuery(sql.toString());
				query.setParameter("orgRrn", sc.getOrgRrn());
				query.setParameter("shiftId", shift.getShiftId());
				query.executeUpdate();
				
				shift = em.find(Shift.class, shift.getObjectRrn());
				em.remove(shift);
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	public ADSequence createNewSequence(long orgRrn, String name, long generatorRrn, int minValue) {
		return sequenceManager.createNewSequence(orgRrn, name, generatorRrn, minValue);
	}
	
	/**
	* 保存Location父级与子级
	* @param locationList 区域集合
	* @param sc
	*/ 
	public void saveLocationChildAndParen(List<Location> locationList, SessionContext sc) throws ClientException {
		try {
			if(CollectionUtils.isNotEmpty(locationList)) {
				List<Location> parenList = new ArrayList<Location>();
				List<Location> childList = new ArrayList<Location>();
				//区分父子级
				for (Location location : locationList) {
					if(!StringUtil.isEmpty(location.getParentLocationName())) {
						childList.add(location);
					}else {
						parenList.add(location);
					}
				}
				Map<String, Location> parentMap = new HashMap<String, Location>();
				List<Location> parens = adManager.getEntityList(sc.getOrgRrn(), Location.class, Integer.MAX_VALUE, "parentLocationRrn IS NULL", "");
				if(CollectionUtils.isNotEmpty(parens)) {
					// 转map方便判断
					parentMap = parens.stream().collect(Collectors.toMap(Location::getName, obj -> obj));
				}
				//保存父级后将关联主键写入子级
				if(CollectionUtils.isNotEmpty(parenList)) {
					for(Location location : parenList) {
						location.setOrgRrn(sc.getOrgRrn());
						location.setCreatedBy(sc.getUserName());
						location = em.merge(location);
						parentMap.put(location.getName(), location);
					}
				}
				if(CollectionUtils.isNotEmpty(childList)) {
					for(Location location : childList) {
						location.setOrgRrn(sc.getOrgRrn());
						location.setCreatedBy(sc.getUserName());
						//写入父级主键
						location.setParentLocationRrn(parentMap.get(location.getParentLocationName()).getObjectRrn());
						em.merge(location);
					}
				}
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
}
