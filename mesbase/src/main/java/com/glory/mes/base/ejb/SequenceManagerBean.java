package com.glory.mes.base.ejb;

import java.util.List;

import javax.ejb.EJB;
import javax.ejb.Local;
import javax.ejb.Remote;
import javax.ejb.Stateless;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.persistence.EntityManager;
import javax.persistence.LockModeType;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.apache.log4j.Logger;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADSequence;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ExceptionManager;
import com.glory.mes.base.client.SequenceManager;
import com.glory.mes.base.exception.MBasExceptionBundle;

@Stateless
@Remote(SequenceManager.class)
@Local(SequenceManagerLocal.class)
public class SequenceManagerBean implements SequenceManager, SequenceManagerLocal {

	private static final Logger logger = Logger.getLogger(SequenceManagerBean.class);

	@PersistenceContext
	private EntityManager em;
	
	@EJB
	private MBASManagerLocal mBasManager;
	
	/**
	 * 创建Sequence
	 * @param orgRrn 所对应的区域
	 * @param name Sequence的名字
	 * @param generatorRrn Generator主键
	 * @param minValue 最小Seq值,所返回的Seq必须大于等于minValue
	 */
	@TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
	public ADSequence createNewSequence(long orgRrn, String name, long generatorRrn, int minValue) {
		try {
			ADSequence seqence;
			seqence = new ADSequence();
			seqence.setOrgRrn(orgRrn);
			seqence.setIsActive(true);
			seqence.setName(name);
			seqence.setGeneratorRrn(generatorRrn);
			if (minValue > 0) {
				seqence.setNextSeq((long)minValue);
			} else {
				seqence.setNextSeq(1L);
			}
			em.persist(seqence);
			em.flush();
			
			return seqence;
		} catch(Exception e) {
			logger.error(e.getMessage(), e);
		}
		return null;
	}
	
	
	/**
	 * 根据对应的Generator及ADSequence的名字
	 * 获得所对应的下一个Sequence值
	 * 如果没有则创建一个新的记录
	 * 
	 * @param orgRrn 所对应的区域
	 * @param generateRrn 所对应的Generator的ObjectRrn
	 * @param name ADSequence的名字
	 * @param count 所需要获得Sequence数量
	 * @param minValue 最小Seq值,所返回的Seq必须大于等于minValue
	 */
	@TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
	public int[] getNextSequenceValueNewTrans(long orgRrn, long generateRrn, String name, int count, int minValue) throws ClientException{
		StringBuffer sql = new StringBuffer(" SELECT ADSequence FROM ADSequence ADSequence ");
		sql.append(" WHERE ");
		sql.append(ADBase.BASE_CONDITION_N);
		sql.append(" AND name = :name ");
		sql.append(" AND generatorRrn = :generatorRrn ");

		try {
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("name", name);
			query.setParameter("generatorRrn", generateRrn);

			ADSequence seqence;
			List<ADSequence> seqences = (List<ADSequence>)query.getResultList();
			if (seqences.size() == 0) {
				seqence = mBasManager.createNewSequence(orgRrn, name, generateRrn, minValue);
				if (seqence == null) {
					seqences = (List<ADSequence>)query.getResultList();
					if (seqences.size() == 0) {
						throw MBasExceptionBundle.bundle.GetAdSequenceError();
					} else {
						seqence = seqences.get(0);
					}
				}
			} else {
				seqence = seqences.get(0);
			}
			if (!em.contains(seqence)) {
				seqence = em.find(ADSequence.class, seqence.getObjectRrn(), LockModeType.PESSIMISTIC_WRITE);
			} else {
				em.refresh(seqence, LockModeType.PESSIMISTIC_WRITE);
			}
			
			int seqValue = seqence.getNextSeq().intValue();
			if (seqValue < minValue) {
				seqValue = minValue;
			}
			seqence.setNextSeq((long)(seqValue + count));
			int[] vals = new int[count];
			for (int i = 0; i < count; i++){
				vals[i] = seqValue + i;
			}
			em.merge(seqence);
			return vals;
		}  catch(Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
}
