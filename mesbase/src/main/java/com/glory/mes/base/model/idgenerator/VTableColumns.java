package com.glory.mes.base.model.idgenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.glory.framework.activeentity.model.ADBaseObjectSeq;

@Entity
@Table(name="V_TABLE_COLUMNS")
public class VTableColumns extends ADBaseObjectSeq {
	private static final long serialVersionUID = 1L;

	@Column(name="TABLE_NAME")
	private String tableName;
	
	@Column(name="COLUMN_NAME")
	private String columnName;

	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public String getColumnName() {
		return columnName;
	}

	public void setColumnName(String columnName) {
		this.columnName = columnName;
	}
	
}
