package com.glory.mes.base.model.idgenerator;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.core.util.SessionContext;
import com.glory.mes.base.client.MBASManager;

/**
 * IDGenerator上下文
 */
public class GeneratorContext implements Serializable {
	private static final long serialVersionUID = 1L;
	
	public static final String BATCH_SEQ_HOLDER = "BATCH_SEQ_HOLDER";

	/**
	 * IDGenerator所对应的Context类型
	 */
	public static final String SYSREF_IDGENERATOR_CONTEXT = "IDGeneratorContext";
	
	/**
	 * IDGenerator默认的Context
	 */
	public static final String CONTEXT_NAME_IDGENERATOR = "IDGENERATOR";
	
	public static final String CONTEXT_FIELD_ORG = "orgRrn";
	public static final String CONTEXT_FIELD_OBJECT_TYPE = "objectType";
	public static final String CONTEXT_FIELD_TRANSACTION = "transaction";
	public static final String CONTEXT_FIELD_SEPCNAME = "specName";
	public static final String CONTEXT_FIELD_PLANSTARTDATE = "planStartDate";
	public static final String CONTEXT_FIELD_PART_NAME = "partName";
	public static final String CONTEXT_FIELD_PROCESS_NAME = "processName";
	public static final String CONTEXT_FIELD_PROCEDURE_NAME = "procedureName";
	public static final String CONTEXT_FIELD_STEP_NAME = "stepName";
	public static final String CONTEXT_FIELD_EQUIPMENT = "equipment";
	public static final String CONTEXT_FIELD_LOT_TYPE = "lotType";
	public static final String CONTEXT_FIELD_CUSTOMER = "customer";
	public static final String CONTEXT_FIELD_WO_ID = "woId";
	public static final String CONTEXT_FIELD_LOT_ID = "lotId";
	public static final String CONTEXT_FIELD_LOT_NAME = "lotName";
	public static final String CONTEXT_FIELD_COMPONENT_NAME = "componentName";
	public static final String CONTEXT_FIELD_BATCH_ID = "batchId";
	public static final String CONTEXT_FIELD_CELL_ID = "cellId";
	public static final String CONTEXT_FIELD_PARENT_ID = "parentId";

	public static final String CONTEXT_RESULT_FIELD_RULE_NAME = "idGeneratorRuleName";
	
	public static final String TRANS_TYPE_LOT_CREATE = "CreateLot";
	public static final String TRANS_TYPE_LOT_SPLIT = "SplitOutLot";
	public static final String TRANS_TYPE_PILOT_SPLIT = "Pilot";
	public static final String TRANS_TYPE_REWORK_SPLIT = "Rework";

	public static final String TRANS_TYPE_BATCH_CREATE = "CreateBatch";
	public static final String TRANS_TYPE_BATCH_SPLIT = "SplitBatch";
	
	public static final String PARAM_SPLIT_TYPE = "SplitType";

	private ADManager adManager;
	private MBASManager mbasManager;
	
	private String objectType;
	private ADBase object;
	private Date ruleDate;
	private SessionContext sessionContext;
	
	private GeneratorRule generatorRule;
	
	private Map<String, Object> parameterMap = new HashMap<String, Object>();
	
	private int idNum = 1;

	private Integer currentIdNum = null;

	private List<String> idSegments = new LinkedList<String>();
	
	/**
	 * 是否在一个新的事务中创建序号(默认为true)
	 * 优点:避免大量创建同一个Sequence时,发生并发锁,而导致系统死锁
	 * 缺点:在事务失败时,不能恢复到以前的序列号
	 */
	private boolean isNewTrans = true;
	
	/**
	 * 以Batch方式生成Sequence,只针对Sequence有效
	 * 要求Sequence其他部分第一次生成后就保存不变
	 */
	private boolean isBatch = false;

	public GeneratorContext(ADBase object, Map<String, Object> parameterMap, 
			SessionContext sc, String objectType, int idNum) {
		this.setObject(object);
		this.parameterMap = parameterMap;
		this.sessionContext = sc;
		this.objectType = objectType;
		this.idNum = idNum;
	}
	
	public GeneratorContext(ADBase object, Map<String, Object> parameterMap, 
			SessionContext sc, String objectType) {
		this(object, parameterMap, sc, objectType, 1);
	}

	public String getObjectType() {
		return objectType;
	}

	public void setObjectType(String objectType) {
		this.objectType = objectType;
	}
	
	public void addParameter(String name, Object value) {
		parameterMap.put(name, value);
	}
	
	public Object getParameter(String name) {
		return parameterMap.get(name);
	}
	
	public void setParameterMap(Map<String, Object> parameterMap) {
		this.parameterMap = parameterMap;
	}

	public Map<String, Object> getParameterMap() {
		return parameterMap;
	}
	
	public String getIdPrefix() {
		String idPrefix = "";
		for (String idSegment : idSegments) {
			idPrefix += idSegment;
		}
		return idPrefix;
	}

	public void setObject(ADBase object) {
		this.object = object;
	}

	public ADBase getObject() {
		return object;
	}
	
	public void addIdSegments(String segment){
		idSegments.add(segment);
	}

	public int getIdNum() {
		return idNum;
	}

	public void setIdNum(int idNum) {
		this.idNum = idNum;
	}

	public Integer getCurrentIdNum() {
		return currentIdNum;
	}

	public void setCurrentIdNum(Integer currentIdNum) {
		this.currentIdNum = currentIdNum;
	}
	
	public void setRuleDate(Date ruleDate) {
		this.ruleDate = ruleDate;
	}

	public Date getRuleDate() {
		return ruleDate;
	}

	public List<String> getIdSegments() {
		return idSegments;
	}

	public void setSessionContext(SessionContext sessionContext) {
		this.sessionContext = sessionContext;
	}

	public SessionContext getSessionContext() {
		return sessionContext;
	}

	public GeneratorRule getGeneratorRule() {
		return generatorRule;
	}

	public void setGeneratorRule(GeneratorRule generatorRule) {
		this.generatorRule = generatorRule;
	}

	public ADManager getADManager() {
		return adManager;
	}

	public void setADManager(ADManager adManager) {
		this.adManager = adManager;
	}

	public MBASManager getMBASManager() {
		return mbasManager;
	}

	public void setMBASManager(MBASManager mbasManager) {
		this.mbasManager = mbasManager;
	}
	
	public boolean isNewTrans() {
		return isNewTrans;
	}

	public void setNewTrans(boolean isNewTrans) {
		this.isNewTrans = isNewTrans;
	}
	
	public boolean isBatch() {
		return isBatch;
	}

	public void setBatch(boolean isBatch) {
		this.isBatch = isBatch;
	}
}
