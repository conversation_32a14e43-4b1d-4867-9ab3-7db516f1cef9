package com.glory.mes.base.upload;

import java.util.List;
import java.util.Map;

import javax.ejb.EJB;
import javax.ejb.Stateless;

import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Row;

import com.glory.common.excel.UploadField;
import com.glory.common.excel.upload.DefaultUploadReader;
import com.glory.common.excel.upload.UploadErrorLog;
import com.glory.common.excel.upload.UploadReader;
import com.glory.framework.security.ejb.SecurityManagerLocal;
import com.glory.framework.security.model.ADUser;
import com.glory.mes.base.model.Team;
import com.glory.mes.base.model.TeamUser;
import com.google.common.base.Objects;

@Stateless
public class TeamUserUploadReader extends DefaultUploadReader implements UploadReader {

	@EJB
	private SecurityManagerLocal securityManager;
	
	private Team currentTeam;
	private int seqNo = 0;
	
	@Override
	public void init() {
		currentTeam = new Team();
		seqNo = 0;
	}
	
	@Override
	public Object read(Object bean, Row row, Map<Integer, UploadField> fieldMap, boolean isMandatory) throws Exception {
		Team team;
		if(bean == null) {
			team = new Team();
		}else {
			team = (Team)bean;
		}
		team = (Team) super.read(team, row, fieldMap, isMandatory);
		
		if (!Objects.equal(team.getName(), currentTeam.getName())) {
			currentTeam = team;
			seqNo = 0;
		}
		
		List<TeamUser> teamUsers = team.getTeamUser();
		if (CollectionUtils.isNotEmpty(teamUsers)) {
			TeamUser teamUser = teamUsers.get(seqNo);
			ADUser user = securityManager.getUserByUserName(teamUser.getUserName());
			if (user != null) {
				teamUser.setOrgRrn(sc.getOrgRrn());
				teamUser.setTeamId(team.getName());
				teamUser.setUserRrn(user.getObjectRrn());
				teamUser.setUserName(user.getUserName());
			} else {
				UploadErrorLog log = new UploadErrorLog((long)row.getRowNum() + 1, null, "USERNAME", getMessage("bas.line_lineuser_not_exsit"));
				errLogs.add(log);
			}
			seqNo++;
		}
		return team;
	}
	
	@Override
	public int getPriority() {
		return 30;
	}

	@Override
	public String getActionName() {
		return "TeamUserUploadReader";
	}

	@Override
	public String getActionDesc() {
		return "Team excel upload reader";
	}

	@Override
	public String getAuthorityName() {
		return "Bas.TeamUser";
	}
}
