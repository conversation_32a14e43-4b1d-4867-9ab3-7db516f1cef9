package com.glory.mes.base.model.idgenerator;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADRefList;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.exception.MBasExceptionBundle;

/**
 * 生成变量
 * 根据参数值截取所需要的字符
 */
@Entity
@DiscriminatorValue("V")
public class VariableRuleLine extends GeneratorRuleLine {
	private static final long serialVersionUID = 1L;

	public static final String VARIABLETYPE_PARAMETER = "PARAMETER";
	public static final String VARIABLETYPE_PARAMETER_LIST = "PARAMETERLIST";
	public static final String VARIABLETYPE_DBVALUE = "DBVALUE";
	
	/**
	 * 变量类型
	 * PARAMETER: 变量从Context的parameterMap中取值
	 *            如果没有对应的Parameter,也可以从Context的Object上取值
	 * PARAMETERLIST: 变量从Context的parameterMap中取值,用于需返回多个ID时,与currentIdNum结合使用
	 *            使用parameter + currentIdNum作为key从parameterMap中取值
	 * DBVALUE  : 从DB栏位上取值
	 */
	@Column(name="VARIABLE_TYPE")
	private String variableType = "PARAMETER";
	
	/**
	 * 对应的变量,在variableType为"PARAMETER"时使用
	 * 如果Context的parameterMap有此变量,则使用此值
	 * 否则从Context的Object上取值取得对应的值
	 */
	@Column(name="PARAMETER")
	private String parameter;
	
	@Column(name="TABLEE")//TABLE是oracle的关键字
	private String tablee;
	
	@Column(name="COLUMNN")//COLUMN是oracle的关键字
	private String columnn;
	
	@Column(name="WHERE_CLAUSE")
	private String whereClause;
	
	/**
	 * 编码转换
	 */
	@Column(name = "FORMAT_CODE")
	private String formatCode;
	
	@Column(name="VARIABLE_DIRECTION")
	private String variableDirection;
	
	@Column(name="START_POSITION")
	private Long startPosition = 1L;//默认从第一位开始
	
	@Column(name="LENGTH")
	private Long length = 1L;//默认长度取1

	@Column(name="FIXED_STRING")
	private String fixedString = "_";
	
	@Column(name="STRATEGY")
	private String strategy;//指定分割字符
	
	public String getId(GeneratorContext context) throws Exception {
		try {
			String variable = "";
			if (VARIABLETYPE_PARAMETER.equals(variableType)) {
				if (context.getParameterMap().containsKey(parameter)) {
					//如果Context的parameterMap有此变量,则使用此值
					variable = String.valueOf(context.getParameter(parameter));
				} else if (context.getObject() != null){
					//如果没有在Context的parameterMap中定义,则从Object中取值
					Object value;
					try {
						value = PropertyUtils.getProperty(context.getObject(), parameter);
						if (value != null) {
							variable = String.valueOf(value);
						}
					} catch (Exception e) {
					}
				}
				if (variable == null || variable.trim().length() == 0) {
					//如果不能获得对应的值,则用"_"代替
					variable = "_";
				}
			} else if (VARIABLETYPE_PARAMETER_LIST.equals(variableType)) {
				if (context.getCurrentIdNum() == null) {
					throw new ClientException("Current id number is null.");
				}
				String key = parameter + context.getCurrentIdNum();
				if (context.getParameterMap().containsKey(key)) {
					//如果Context的parameterMap有此变量,则使用此值
					variable = String.valueOf(context.getParameter(key));
				} 
				if (variable == null || variable.trim().length() == 0) {
					//如果不能获得对应的值,则用"_"代替
					variable = "_";
				}
			} else if (VARIABLETYPE_DBVALUE.equals(variableType)) {
				StringBuffer sql = new StringBuffer();
				sql.append("SELECT ");
				sql.append(columnn);
				sql.append(" FROM ");
				sql.append(tablee);
				sql.append(parseWhereClause(whereClause, context));
				MBASManager basManager = context.getMBASManager();
				List<?> r = (List<?>) basManager.executeSql(sql.toString());
				if(r == null || r.size() == 0){
					variable = "_";
				}
				variable = (String) r.get(0);
			}
			if (!"_".equals(variable)){
				if (!StringUtil.isEmpty(strategy)) {
					//按指定字符进行截取
					switch (Integer.valueOf(variableDirection)){
						case 1:
							variable = StringUtils.split(variable,strategy)[0];
							break;
						case 2:
							variable = StringUtils.split(variable,strategy)[1];
							break;
					}
				} else {
					//截取对应的字符串（按长度进行截取）
					if(length != null && length > 0){
						long minLenght = (startPosition == null ? 1 : startPosition) + 
								(length == null ? 1 : length) - 1;
						if (variable.length() >= minLenght) {
							//采用截取方法
							switch (Integer.valueOf(variableDirection)){
								case 1:
									variable = variable.substring((startPosition.intValue() - 1), (startPosition.intValue() + length.intValue() - 1));
									break;
								case 2:
									variable = variable.substring(variable.length() - (startPosition.intValue() + length.intValue() - 1), variable.length() - startPosition.intValue() + 1);
									break;
							}
						} else if (variable.length() < minLenght) {
							//采用补数方式（fixedString只支持一位长度）
							variable = "";
							if (fixedString != null & fixedString.length() > 0) {
								fixedString = fixedString.substring(0, 1);
								switch (Integer.valueOf(variableDirection)){
									case 1:
										while (variable.length() < length) {  
											variable = fixedString + variable;
										}
										break;
									case 2:
										while (variable.length() < length) {  
											variable = variable + fixedString;
										}
										break;
								}
							}
						} 
					}
					
					if (formatCode != null && formatCode.trim().length() > 0) {
						//对变量进行转换
						ADManager adManager = context.getADManager();
						List<ADRefList> formatCodes = adManager.getADRefList(orgRrn, formatCode);
						for (ADRefList ref : formatCodes) {
							if (ref.getKey().equals(variable)) {
								variable = ref.getText();
							}
						}
					}
				}
			}
			return variable;
		} catch (Exception e) {
			String errorInfo = e.toString();
			throw MBasExceptionBundle.bundle.IdRuleVariableRuleLineError(context.getGeneratorRule().getName(), String.valueOf(getSeqNo()), errorInfo);
		}
	}
	
	public static String parseWhereClause(String whereClause, GeneratorContext context) throws ClientException {
		try {
			if (whereClause == null || whereClause.indexOf("=:") < 0) {
				return whereClause;
			} else {
				List<String> paramList = StringUtil.parseClauseParam(whereClause);
				Map<String, String> paraMap = new LinkedHashMap<String, String>();
				
				if (CollectionUtils.isNotEmpty(paramList)) {
					for (String param : paramList) {
						boolean isMatched = false;
						if (context.getObject() != null) {
							Object value = PropertyUtils.getProperty(context.getObject(), param);
							//如果发value为空的话,就跳过该条件
							if(value != null){
								String clauseStr =  DBUtil.toString(value);
								paraMap.put(param, clauseStr);
								isMatched = true;
							}
						}
						
						if (!isMatched) {
							// 从参数Map中匹配
							if (MapUtils.isNotEmpty(context.getParameterMap())) {
								Object value = context.getParameterMap().get(param);
								//如果发value为空的话,就跳过该条件
								if(value != null){
									String clauseStr =  DBUtil.toString(value);
									paraMap.put(param, clauseStr);
								}
							}
						}
					}
				}
				
				StringBuffer buffer = new StringBuffer(" WHERE ");
				buffer.append(StringUtil.parseClause(whereClause, paraMap));
				return buffer.toString();
			}
		} catch (Exception e) {
			throw MBasExceptionBundle.bundle.IdgeneratorDBValueError();
		}
	}

	public String getVariableType() {
		return variableType;
	}

	public void setVariableType(String variableType) {
		this.variableType = variableType;
	}

	public String getParameter() {
		return parameter;
	}

	public void setParameter(String parameter) {
		this.parameter = parameter;
	}

	public String getWhereClause() {
		return whereClause;
	}

	public void setWhereClause(String whereClause) {
		this.whereClause = whereClause;
	}

	public String getTablee() {
		return tablee;
	}

	public void setTablee(String tablee) {
		this.tablee = tablee;
	}

	public String getColumnn() {
		return columnn;
	}

	public void setColumnn(String columnn) {
		this.columnn = columnn;
	}

	public String getVariableDirection() {
		return variableDirection;
	}

	public void setVariableDirection(String variableDirection) {
		this.variableDirection = variableDirection;
	}

	public Long getStartPosition() {
		return startPosition;
	}

	public void setStartPosition(Long startPosition) {
		this.startPosition = startPosition;
	}

	public Long getLength() {
		return length;
	}

	public void setLength(Long length) {
		this.length = length;
	}

	public void setFormatCode(String formatCode) {
		this.formatCode = formatCode;
	}

	public String getFormatCode() {
		return formatCode;
	}

	public String getFixedString() {
		return fixedString;
	}

	public void setFixedString(String fixedString) {
		this.fixedString = fixedString;
	}

	public String getStrategy() {
		return strategy;
	}

	public void setStrategy(String strategy) {
		this.strategy = strategy;
	}

}
