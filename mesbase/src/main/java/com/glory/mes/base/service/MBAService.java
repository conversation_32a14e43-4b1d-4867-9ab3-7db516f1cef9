package com.glory.mes.base.service;

import java.util.List;

import javax.ejb.EJB;
import javax.ejb.Stateless;

import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.service.CoreService;
import com.glory.framework.core.util.SessionContext;
import com.glory.mes.base.ejb.MBASManagerLocal;
import com.glory.mes.base.model.Location;

@Stateless
@CoreService(serviceGroup = "MESCore")
public class MBAService {

	@EJB
    private MBASManagerLocal mBasManager;
	
	/**
	* 保存Location父级与子级
	* @param locationList 区域集合
	* @param sc
	*/ 
	public void saveLocationChildAndParen(List<Location> locationList, SessionContext sc) throws ClientException {
		mBasManager.saveLocationChildAndParen(locationList, sc);
	}
}
