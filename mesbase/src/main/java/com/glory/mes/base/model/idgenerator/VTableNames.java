package com.glory.mes.base.model.idgenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.glory.framework.activeentity.model.ADBaseObjectSeq;

@Entity
@Table(name="V_TABLE_NAMES")
public class VTableNames extends ADBaseObjectSeq {
	private static final long serialVersionUID = 1L;
	
	@Column(name="TABLE_NAME")
	private String tableName;

	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

}
