package com.glory.mes.base.cdi;

import java.util.List;

import com.glory.framework.core.cdi.ICdiAction;
import com.glory.mes.base.model.idgenerator.GeneratorContext;
import com.glory.mes.base.model.idgenerator.GeneratorRule;

public interface IIdGeneratorCdiAction extends ICdiAction {

	/**
	 * 用来判断注入方法是否对当前规则有效
	 * 一般可通过规则名称或对象类型,事务类型(保存在context的parameterMap参数中)等
	 */
	public boolean isSupport(GeneratorRule rule, GeneratorContext context);

	/**
	 * 变更Context内容
	 */
	public GeneratorContext buildGeneratorContext(GeneratorRule rule, GeneratorContext context);
	
	/**
	 * 用来判断是否完全使用只定义Id生成规则, 只有isSupport为true时,才会有效
	 */
	public boolean isSupportGeneratorId(GeneratorRule rule, GeneratorContext context);

	/**
	 * 完全自定义ID生成方式,isSupportGeneratorId方法配合使用
	 */
	public List<String> generatorId(GeneratorRule rule, boolean isParameterList, GeneratorContext context);
	
	/**
	 * 在按照ID生成规则生成ID后,再对ID进行处理后返回
	 */
	public List<String> postGenerateId(List<String> ids, GeneratorRule rule, boolean isParameterList, GeneratorContext context);
	
}
