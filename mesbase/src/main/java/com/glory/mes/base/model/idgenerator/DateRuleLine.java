package com.glory.mes.base.model.idgenerator;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.apache.commons.beanutils.PropertyUtils;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADRefList;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.exception.MBasExceptionBundle;
import com.glory.mes.base.model.CalendarDay;

/**
 * 根据日期生成所需ID
 */
@Entity
@DiscriminatorValue("D")
public class DateRuleLine extends GeneratorRuleLine {
	private static final long serialVersionUID = 1L;

	public static final String DATETYPE_SYSTEM = "SYSTEM";
	public static final String DATETYPE_SPECIFIC = "SPECIFIC";
	public static final String DATETYPE_VARIABLE = "VARIABLE";

	public static final String DATEFORMAT_YYYY = "yyyy";
	public static final String DATEFORMAT_YY = "yy";
	public static final String DATEFORMAT_Y = "y";
	public static final String DATEFORMAT_Y1 = "y1"; // 年第1位，例如2021:2
	public static final String DATEFORMAT_Y2 = "y2"; // 年第2位，例如2021:0
	public static final String DATEFORMAT_Y3 = "y3"; // 年第3位，例如2021:2
	public static final String DATEFORMAT_Y4 = "y4"; // 年第4位，例如2021:1
	public static final String DATEFORMAT_MM = "MM";
	public static final String DATEFORMAT_M = "M";
	public static final String DATEFORMAT_DD = "dd";
	public static final String DATEFORMAT_WW = "ww";
	public static final String DATEFORMAT_hh = "hh";
	public static final String DATEFORMAT_HH = "HH";
	public static final String DATEFORMAT_mm = "mm";
	public static final String DATEFORMAT_ss = "ss";
	public static final String DATEFORMAT_REF_YEAR = "YEAR_CODE";
	public static final String DATEFORMAT_REF_MONTH = "MONTH_CODE";
	public static final String DATEFORMAT_REF_DAY = "DAY_CODE";
	public static final String DATEFORMAT_REF_WEEK = "WEEK_CODE";

	/**
	 * 日期类型
	 * SYSTEM   : 默认类型,根据Context中日期来生成
	 * SPECIFIC : 指定类型,根据Rule中固定的日期来生成,用的极少
	 * VARIABLE : 变量类型,通过对象传值
	 */
	@Column(name = "DATE_TYPE")
	private String dateType = "SYSTEM"; // SYSTEM or SPECIFIC

	@Column(name = "SPECIFIC_DATE")
	private Date specificDate;

	/**
	 * 对应的日历,如果未指定则使用标准日历
	 */
	@Column(name = "CALENDAR")
	private String calendar;

	@Column(name = "DATE_FORMAT")
	private String dateFormat;
	
	@Column(name = "PARAMETER")
	private String parameter;

	@Column(name = "FORMAT_CODE")
	private String formatCode;

	@Column(name = "CODE_LEN")
	private int codeLen = 1;   //编码的长度,默认是1位

	/**
	 * 生成日期型ID
	 */
	public String getId(GeneratorContext context) throws Exception {
		try {
			Date useDate = new Date();
			if (DATETYPE_SPECIFIC.equals(dateType)) {
				//如果类型为指定型,则使用指定的日期
				if (specificDate != null) {
					useDate = specificDate;
				} else {
					useDate = context.getRuleDate();
				}
			}
			
			if (DATETYPE_VARIABLE.equals(dateType)) {
				/*//如果类型为指定型,则使用指定的日期
			if (specificDate != null) {
				
			} else {
				useDate = context.getRuleDate();
			}*/
				
				if (context.getParameterMap().containsKey(parameter)) {
					//如果Context的parameterMap有此变量,则使用此值
					useDate = (Date) context.getParameter(parameter);
				} else if (context.getObject() != null){
					//如果没有在Context的parameterMap中定义,则从Object中取值
					Object value;
					try {
						value = PropertyUtils.getProperty(context.getObject(), parameter);
						if (value != null) {
							useDate = (Date) value;
						}
					} catch (Exception e) {
					}
				}
				if (useDate == null) {
					useDate = context.getRuleDate();
				}
			}
			ADManager adManager = context.getADManager();
			MBASManager basManager = context.getMBASManager();
			if (calendar == null || calendar.trim().length() == 0) {
				//如果未指定日历,则使用标准日历
				Calendar calendar = new GregorianCalendar();
				calendar.setTime(useDate);
				String returnString = "_";
				if (DATEFORMAT_YYYY.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
					returnString = String.valueOf(sdf.format(calendar.getTime()));
				} else if (DATEFORMAT_YY.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
					returnString = String.valueOf(sdf.format(calendar.getTime()));
				} else if (DATEFORMAT_Y.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
					String year = sdf.format(calendar.getTime());
					returnString = String.valueOf(year.charAt(year.length()-1));
				} else if (DATEFORMAT_Y1.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat(DATEFORMAT_Y);
					String year = sdf.format(calendar.getTime());
					returnString = String.valueOf(year.charAt(0));
				} else if (DATEFORMAT_Y2.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat(DATEFORMAT_Y);
					String year = sdf.format(calendar.getTime());
					returnString = String.valueOf(year.charAt(1));
				} else if (DATEFORMAT_Y3.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat(DATEFORMAT_Y);
					String year = sdf.format(calendar.getTime());
					returnString = String.valueOf(year.charAt(2));
				} else if (DATEFORMAT_Y4.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat(DATEFORMAT_Y);
					String year = sdf.format(calendar.getTime());
					returnString = String.valueOf(year.charAt(3));
				} else if (DATEFORMAT_REF_YEAR.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
					String year = String.valueOf(sdf.format(calendar.getTime()));
					List<ADRefList> formatCodes = adManager.getADRefList(orgRrn, formatCode);
					for (ADRefList ref : formatCodes) {
						if (ref.getKey().equals(year)) {
							returnString = ref.getText();
							break;
						}
					}
				} else if (DATEFORMAT_MM.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
					returnString = String.valueOf(sdf.format(calendar.getTime()));
				} else if (DATEFORMAT_M.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
					returnString = String.valueOf(sdf.format(calendar.getTime()));
					if ("10".equals(returnString)) {
						returnString = "A";
					} else if ("11".equals(returnString)) {
						returnString = "B";
					} else if ("12".equals(returnString)) {
						returnString = "C";
					}
				} else if (DATEFORMAT_REF_MONTH.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat("MM");
					String month = String.valueOf(sdf.format(calendar.getTime()));
					List<ADRefList> formatCodes = adManager.getADRefList(orgRrn, formatCode);
					for (ADRefList ref : formatCodes) {
						if (ref.getKey().equals(month)) {
							returnString = ref.getText();
							break;
						}
					}
				} else if (DATEFORMAT_DD.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
					returnString = String.valueOf(sdf.format(calendar.getTime()));
				} else if (DATEFORMAT_REF_DAY.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat("dd");
					String day = sdf.format(calendar.getTime());
					List<ADRefList> formatCodes = adManager.getADRefList(orgRrn, formatCode);
					for (ADRefList ref : formatCodes) {
						if (ref.getKey().equals(day)) {
							returnString = ref.getText();
							break;
						}
					}
				} else if (DATEFORMAT_WW.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
					int weekNum = calendar.get(Calendar.WEEK_OF_YEAR);
					int year = calendar.get(Calendar.YEAR);
					int month = calendar.get(Calendar.MONTH);
					//month=11是12月，一年的最后一周，年周号是上周年周号加1
					//不做特殊处理，最后一周会变成2101,2201这样
					if(month == 11 && weekNum ==1) 
					{
						Calendar calPreWeek =(Calendar) calendar.clone();
						calPreWeek.add(Calendar.DAY_OF_YEAR, -7);
						int num2 = calPreWeek.get(Calendar.WEEK_OF_YEAR);
						returnString = String.format("%02d",  num2+1);
					}
					else{
						returnString = sdf.format(calendar.getTime());
					}
				} else if (DATEFORMAT_REF_WEEK.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat("ww");
					String week = sdf.format(calendar.getTime());
					List<ADRefList> formatCodes = adManager.getADRefList(orgRrn, formatCode);
					for (ADRefList ref : formatCodes) {
						if (ref.getKey().equals(week)) {
							returnString = ref.getText();
							break;
						}
					}
				} else if (DATEFORMAT_hh.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
					returnString = sdf.format(calendar.getTime());
				} else if (DATEFORMAT_HH.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
					returnString = sdf.format(calendar.getTime());
				} else if (DATEFORMAT_mm.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
					returnString = String.valueOf(sdf.format(calendar.getTime()));
				} else if (DATEFORMAT_ss.equals(dateFormat)) {
					SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
					returnString = sdf.format(calendar.getTime());
				}
				return returnString;
			} else {
				CalendarDay calendarDay = basManager.getCalendarDay(orgRrn, calendar, useDate);
				String returnString = null;
				if (calendarDay != null) {
					if (DATEFORMAT_YYYY.equals(dateFormat)) {
						String year = calendarDay.getYear();
						returnString = formatString(year, 4, '0');
					} else if (DATEFORMAT_YY.equals(dateFormat)) {
						String year = calendarDay.getYear();
						returnString = formatString(year, 2, '0');
					} else if (DATEFORMAT_Y.equals(dateFormat)) {
						String year = calendarDay.getYear();
						returnString = "0" + year;
						returnString = returnString.substring(returnString.length() - 1);
					} else if (DATEFORMAT_REF_YEAR.equals(dateFormat)) {
						String year = calendarDay.getYear();
						List<ADRefList> formatCodes = adManager.getADRefList(orgRrn, formatCode);
						for (ADRefList ref : formatCodes) {
							if (ref.getKey().equals(year)) {
								returnString = ref.getText();
								break;
							}
						}
					} else if (DATEFORMAT_MM.equals(dateFormat)) {
						String month = calendarDay.getMonth();
						returnString = formatString(month, 2, '0');
					} else if (DATEFORMAT_M.equals(dateFormat)) {
						//1-9,A,B,C
						String month = calendarDay.getMonth();
						if("10".equals(month)){
							month = "A";
						}else if("11".equals(month)){
							month = "B";
						}if("12".equals(month)){
							month = "C";
						}
						returnString = month;
					} else if (DATEFORMAT_REF_MONTH.equals(dateFormat)) {
						String month = calendarDay.getMonth();
						List<ADRefList> formatCodes = adManager.getADRefList(orgRrn, formatCode);
						for (ADRefList ref : formatCodes) {
							if (ref.getKey().equals(month)) {
								returnString = ref.getText();
								break;
							}
						}
					} else if (DATEFORMAT_DD.equals(dateFormat)) {
						SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
						returnString = String.valueOf(sdf.format(calendarDay.getSysDate()));
					} else if (DATEFORMAT_REF_DAY.equals(dateFormat)) {
						SimpleDateFormat sdf = new SimpleDateFormat("dd");
						String day = String.valueOf(sdf.format(calendarDay.getSysDate()));
						List<ADRefList> formatCodes = adManager.getADRefList(orgRrn, formatCode);
						for (ADRefList ref : formatCodes) {
							if (ref.getKey().equals(day)) {
								returnString = ref.getText();
								break;
							}
						}
					} else if (DATEFORMAT_WW.equals(dateFormat)) {
						String week = calendarDay.getWeek();
						returnString = formatString(week, 2, '0');;
					} else if (DATEFORMAT_REF_WEEK.equals(dateFormat)) {
						String week = calendarDay.getWeek();
						List<ADRefList> formatCodes = adManager.getADRefList(orgRrn, formatCode);
						for (ADRefList ref : formatCodes) {
							if (ref.getKey().equals(week)) {
								returnString = ref.getText();
								break;
							}
						}
					} else if (DATEFORMAT_hh.equals(dateFormat)) {
						returnString = "_";
					} else if (DATEFORMAT_HH.equals(dateFormat)) {
						returnString = "_";
					} else if (DATEFORMAT_mm.equals(dateFormat)) {
						returnString = "_";
					} else if (DATEFORMAT_ss.equals(dateFormat)) {
						returnString = "_";
					}
					return returnString;
				}
			}
			return "_";
		} catch (Exception e) {
			String errorInfo = e.toString();
			throw MBasExceptionBundle.bundle.IdRuleDateRuleLineError(context.getGeneratorRule().getName(), String.valueOf(getSeqNo()), errorInfo);
		}
	}

	public String getDateType() {
		return dateType;
	}

	public void setDateType(String dateType) {
		this.dateType = dateType;
	}

	public Date getSpecificDate() {
		return specificDate;
	}

	public void setSpecificDate(Date specificDate) {
		this.specificDate = specificDate;
	}

	public String getCalendar() {
		return calendar;
	}

	public void setCalendar(String calendar) {
		this.calendar = calendar;
	}

	public String getDateFormat() {
		return dateFormat;
	}

	public void setDateFormat(String dateFormat) {
		this.dateFormat = dateFormat;
	}

	public int getCodeLen() {
		return codeLen;
	}

	public void setCodeLen(int codeLen) {
		this.codeLen = codeLen;
	}
	
	private String formatString(String original, int length, char prefixChar){
		for (int i = 0; i < length; i++){
			original = prefixChar + original;
		}
		return original.substring(original.length() - length);
	}

	public String getParameter() {
		return parameter;
	}

	public void setParameter(String parameter) {
		this.parameter = parameter;
	}
	
	public String getFormatCode() {
		return formatCode;
	}

	public void setFormatCode(String formatCode) {
		this.formatCode = formatCode;
	}
	
}
