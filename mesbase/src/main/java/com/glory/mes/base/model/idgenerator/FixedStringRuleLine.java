package com.glory.mes.base.model.idgenerator;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

@Entity
@DiscriminatorValue("F")
public class FixedStringRuleLine extends GeneratorRuleLine {
	private static final long serialVersionUID = 1L;

	@Column(name="FIXED_STRING")
	private String fixedString;
	
	public String getId(GeneratorContext context) {
		return fixedString;
	}

	public String getFixedString() {
		return fixedString;
	}

	public void setFixedString(String fixedString) {
		this.fixedString = fixedString;
	}
	
}
