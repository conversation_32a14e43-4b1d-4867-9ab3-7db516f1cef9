/*
 * package com.glory.mes.base.model.idgenerator;
 * 
 * import static org.junit.Assert.*;
 * 
 * import java.util.List;
 * 
 * import org.junit.Test;
 * 
 * import com.google.common.collect.Lists;
 * 
 * import junit.framework.Assert;
 * 
 * public class SequenceCycleRuleLineTest {
 * 
 * @Test public void testGetCurrentSeq() throws Exception {
 * SequenceCycleRuleLine rule = new SequenceCycleRuleLine(); rule.setMin("1");
 * rule.setMax("99");
 * rule.setSequenceType(SequenceCycleRuleLine.SEQUENCETYPE_DIGITS);
 * rule.setSizee(2L);
 * 
 * List<String> nextSeqs = rule.getCurrentSeq(null, 1);
 * Assert.assertEquals("01", nextSeqs.get(0));
 * 
 * nextSeqs = rule.getCurrentSeq(null, 3); Assert.assertEquals("01",
 * nextSeqs.get(0)); Assert.assertEquals("02", nextSeqs.get(1));
 * Assert.assertEquals("03", nextSeqs.get(2));
 * 
 * 
 * nextSeqs = rule.getCurrentSeq(Lists.newArrayList("19", "09", "31", "01"), 3);
 * Assert.assertEquals("32", nextSeqs.get(0)); Assert.assertEquals("33",
 * nextSeqs.get(1)); Assert.assertEquals("34", nextSeqs.get(2));
 * 
 * nextSeqs = rule.getCurrentSeq(Lists.newArrayList("19", "98", "31", "01"), 3);
 * Assert.assertEquals("99", nextSeqs.get(0)); Assert.assertEquals("02",
 * nextSeqs.get(1)); Assert.assertEquals("03", nextSeqs.get(2));
 * 
 * rule.setStrategy(SequenceCycleRuleLine.STRATEGY_FILL); nextSeqs =
 * rule.getCurrentSeq(null, 3); Assert.assertEquals("01", nextSeqs.get(0));
 * Assert.assertEquals("02", nextSeqs.get(1)); Assert.assertEquals("03",
 * nextSeqs.get(2));
 * 
 * nextSeqs = rule.getCurrentSeq(Lists.newArrayList("01", "02", "31", "04",
 * "09"), 3); Assert.assertEquals("03", nextSeqs.get(0));
 * Assert.assertEquals("05", nextSeqs.get(1)); Assert.assertEquals("06",
 * nextSeqs.get(2)); }
 * 
 * @Test public void testGetCurrentSeq1() throws Exception {
 * SequenceCycleRuleLine rule = new SequenceCycleRuleLine(); rule.setMin("1");
 * rule.setMax("99");
 * rule.setSequenceType(SequenceCycleRuleLine.SEQUENCETYPE_ALPHA);
 * rule.setSizee(2L);
 * 
 * List<String> nextSeqs = rule.getCurrentSeq(null, 1);
 * Assert.assertEquals("AA", nextSeqs.get(0));
 * 
 * nextSeqs = rule.getCurrentSeq(null, 3); Assert.assertEquals("AA",
 * nextSeqs.get(0)); Assert.assertEquals("AB", nextSeqs.get(1));
 * Assert.assertEquals("AC", nextSeqs.get(2));
 * 
 * 
 * nextSeqs = rule.getCurrentSeq(Lists.newArrayList("AS", "AI", "BE", "AA"), 3);
 * Assert.assertEquals("BF", nextSeqs.get(0)); Assert.assertEquals("BG",
 * nextSeqs.get(1)); Assert.assertEquals("BH", nextSeqs.get(2));
 * 
 * nextSeqs = rule.getCurrentSeq(Lists.newArrayList("AS", "CT", "BE", "AA"), 3);
 * Assert.assertEquals("CU", nextSeqs.get(0)); Assert.assertEquals("AB",
 * nextSeqs.get(1)); Assert.assertEquals("AC", nextSeqs.get(2));
 * 
 * rule.setStrategy(SequenceCycleRuleLine.STRATEGY_FILL); nextSeqs =
 * rule.getCurrentSeq(null, 3); Assert.assertEquals("AA", nextSeqs.get(0));
 * Assert.assertEquals("AB", nextSeqs.get(1)); Assert.assertEquals("AC",
 * nextSeqs.get(2));
 * 
 * nextSeqs = rule.getCurrentSeq(Lists.newArrayList("AA", "AB", "BE", "AD",
 * "AI"), 3); Assert.assertEquals("AC", nextSeqs.get(0));
 * Assert.assertEquals("AE", nextSeqs.get(1)); Assert.assertEquals("AF",
 * nextSeqs.get(2));
 * 
 * 
 * }
 * 
 * }
 */