package com.glory.mes.ras.rti;

import com.glory.common.rti.msg.alarm.AlarmRTI;
import com.glory.common.rti.msg.alarm.AlarmRTIBody;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.UUIDUtil;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.msg.RequestHeader;

public class EquipmentRTI {

	public static final String TRANSACTION_TYPE_LONGSTATE = "LongEqpState";

	
	public static AlarmRTI getEquipmentRTI(String transactionType, Equipment equipment, SessionContext sc) {
		AlarmRTI rti = new AlarmRTI(transactionType);
		
		RequestHeader header = rti.getHeader();
		header.setOrgName(sc.getOrgName());
		header.setOrgRrn(sc.getOrgRrn());
		header.setUserName(sc.getUserName());
		header.setTransactionId(UUIDUtil.base58Uuid());
		AlarmRTIBody body = rti.getBody();
		body.setCategory(AlarmRTIBody.CATEGORY_MES);
		body.setAlarmType(transactionType);
		body.setObjectType(AlarmRTIBody.OBJECT_TYPE_EQP);
		body.setObjectId(equipment.getEquipmentId());
		body.setEquipmentId(equipment.getEquipmentId());
		body.setEquipmentType(equipment.getEqpType());
		body.setObjectOwner(equipment.getDepartment());
		return rti;
	}
	
}
