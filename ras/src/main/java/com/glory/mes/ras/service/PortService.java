package com.glory.mes.ras.service;

import java.util.ArrayList;
import java.util.List;

import javax.ejb.EJB;
import javax.ejb.Stateless;

import org.apache.commons.collections.CollectionUtils;

import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.service.CoreService;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.ras.ejb.RASManagerLocal;
import com.glory.mes.ras.exception.RasExceptionBundle;
import com.glory.mes.ras.port.Port;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

@Stateless
@CoreService(serviceGroup = "MESCore")
public class PortService {
	
	@EJB
	public RASManagerLocal rasManager;
	
	/**
	 * 根据equipmentId,portId 获取Port
	 * @param orgRrn  厂区号
	 * @param equipmentId 设备Id
	 * @param portId PortId
	 * @param  exceptionFlag 没有查询到设备数据或者Port数据时是否抛出异常(否则返回null)
	 * 
	 * @return Port
	 * 
	 */
	public Port getPortById(long orgRrn, String equipmentId, String portId, boolean exceptionFlag){
		return rasManager.getPortById(orgRrn, equipmentId, portId, exceptionFlag);
	}
	
	/**
	 * 根据equipmentId,portNum 获取Port
	 * 
	 * @param orgRrn  厂区号
	 * @param equipmentId 设备Id
	 * @param portNum portNum
	 * @param  exceptionFlag 没有查询到设备数据或者Port数据时是否抛出异常(否则返回null)
	 * @return Port
	 * 
	 */
	public Port getPortByPortNum(long orgRrn, String equipmentId, String portNum, boolean exceptionFlag){
		return rasManager.getPortByPortNum(orgRrn, equipmentId, portNum, exceptionFlag);
	}
	/**
	 * 根据equipmentId,portId,portNum, 获取Port
	 * @param orgRrn 厂区号
	 * @param equipmentId 设备Id
	 * @param portId
	 * @param portNum
	 * @param  exceptionFlag 没有查询到设备数据或者Port数据时是否抛出异常(否则返回null)
	 * @return
	 * @throws ClientException
	 */
	public Port getPortById(long orgRrn, String equipmentId, String portId,String portNum,boolean exceptionFlag) throws ClientException {
		
		   return rasManager.getPortById(orgRrn, equipmentId, portId, portNum,exceptionFlag);
	}
	
	/**
	 * 根据设备Id，获取设备所有的Port
	 * @param orgRrn  厂区号
	 * @param equipmentId 设备Id
	 * @param exceptionFlag 没有查询到设备数据或者Port数据时是否抛出异常(否则返回空List)
	 * @return List<Port>
	 * 
	 */
	public List<Port> getPortsByEquipment(long orgRrn, String equipmentId, boolean exceptionFlag) {
		return rasManager.getPortsByEquipment(orgRrn, equipmentId, exceptionFlag);
	}
	
	/**
	 * 获取Port
	 * @param orgRrn           厂区ID
	 * @param equipmentId   设备ID
	 * @param port              待查询Port
	 * @param exceptionFlag 是否抛出异常
	 * @return
	 * @throws Exception
	 */
	public Port getPort(long orgRrn, String equipmentId, Port port, boolean exceptionFlag) throws Exception {
		List<Port> ports = Lists.newArrayList(port);
		List<Port> portList = getPorts(orgRrn, equipmentId, ports, exceptionFlag);
		if (CollectionUtils.isEmpty(portList)) {
			return null;
		}
		return portList.get(0);
	}
	
	/**
	 * 获取PortList
	 * @param orgRrn           厂区ID
	 * @param equipmentId  设备ID
	 * @param portList          待查询PortList
	 * @param exceptionFlag 是否抛出异常
	 * @return
	 * @throws Exception
	 */
	public List<Port> getPorts(long orgRrn, String equipmentId, List<Port> portList, boolean exceptionFlag)
			throws Exception {
		List<Port> ports = Lists.newArrayList();
		for (Port port : portList) {
			if (!StringUtil.isEmpty(port.getPortId())) {
				Port newPort = getPortById(orgRrn, equipmentId, port.getPortId(), exceptionFlag);
				ports.add(newPort);
			} else if (!StringUtil.isEmpty(port.getPortNum())) {
				Port newPort = getPortByPortNum(orgRrn, equipmentId, port.getPortNum(), exceptionFlag);
				ports.add(newPort);
			} else {
				throw RasExceptionBundle.bundle.EqpPortNotFound();
			}
		}
		return ports;
	}
	
	/**
	 * 新增/修改 Port
	 * @param port 需要保存的Port
	 * @param sc 会话上下文
	 * @return Port
	 * @throws ClientException
	 */
	public Port savePort(Port port, SessionContext sc) throws ClientException{
		return rasManager.savePort(port, sc);
	}
	
	/**
	 * 删除 Port
	 * @param port 需要删除的Port
	 * @param sc 会话上下文
	 * @throws ClientException
	 */
	public void deletePort(Port port, SessionContext sc) throws ClientException{
		rasManager.deletePort(port, sc);
	}
	
	
	/**
	 * 根据equipmentId,portId 更新PortAccessMode(PortAccessMode 未发生变化时不修改，也不记录历史)
	 * @param equipmentId 设备Id
	 * @param portId PortId
	 * @param portNum portNum
	 * @param targetAccessMode 目标Port作业模式（AGV:Auto,MGV:Manual）
	 * @param sc 会话上下文
	 * @throws ClientException
	 */
	public void changePortAccessModeByPortId(String equipmentId, String portId, String portNum, String targetAccessMode, SessionContext sc){
		Port port = rasManager.getPortById(sc.getOrgRrn(), equipmentId, portId, portNum);
		if(!StringUtils.equals(port.getAccessState(),targetAccessMode)){
			rasManager.changePortAccessMode(port, targetAccessMode, sc);
		}
	}

	/**
	 * 更新PortAccessMode(Port Type 没有发生变化时不会触发修改也不会记录历史)
	 * @param portList 修改的Port
	 * @param sc 会话上下文
	 *
	 * @throws ClientException
	 */
	public void changePortAccessMode(List<Port> portList, SessionContext sc) throws ClientException{
		if(CollectionUtils.isNotEmpty(portList)){
			for(Port tempPort :portList){
				Port port = rasManager.getPortById(sc.getOrgRrn(), tempPort.getParentEqpId(), tempPort.getPortId(), tempPort.getPortNum());
				changePortAccessModeByPortId(tempPort.getParentEqpId(),tempPort.getPortId(),tempPort.getPortNum(), tempPort.getAccessState(), sc);
			}
		}
	}
	
	/**
	 * 修改Port状态并记录Port历史 
	 * @param equipmentId 设备Id
	 * @param portId PortId
	 * @param portNum portNum
	 * @param targetComClass 目标状态大类
	 * @param targetState 目标状态
	 * @param targetSubState 目标状态子类
	 * @param comment 修改备注
	 * @param sc 会话上下文
	 * @return Port
	 * @throws ClientException
	 */
	public void changePortState(String equipmentId, String portId, String portNum, String targetComClass, String targetState, String targetSubState, String comment, SessionContext sc) throws ClientException{
		Port port = rasManager.getPortById(sc.getOrgRrn(), equipmentId, portId, portNum);
		rasManager.changePortState(port, targetComClass, targetState, targetSubState, comment, sc);
	}

	/**
	 * 修改Port列表状态并记录Port历史
	 * @param portList Port 列表（Port 状态都保存在Port中）
	 * @param sc 会话上下文
	 * @throws ClientException
	 */
	public void changePortState(List<Port> portList, SessionContext sc) throws ClientException{
		if(CollectionUtils.isNotEmpty(portList)){
			for(Port tempPort :portList){
				Port port = rasManager.getPortById(sc.getOrgRrn(), tempPort.getParentEqpId(), tempPort.getPortId(), tempPort.getPortNum());
				rasManager.changePortState(port, tempPort.getComClass(), tempPort.getState(), tempPort.getSubState(), null, sc);
			}
		}
	}

	/**
	 * 通过事件修改port状态并记录历史
	 * @param equipmentId 设备Id
	 * @param portId PortId
	 * @param portNum portNum
	 * @param eventId 事件ID
	 * @param comment 备注
	 * @param checkAuthorityFlag 是否检查设备权限
	 * @param sc 会话上下文
	 *
	 * @throws ClientException
	 * 
	 */
	public void changePortStateByEvent(String equipmentId, String portId, String portNum, String eventId, String comment, boolean checkAuthorityFlag, SessionContext sc) throws ClientException{
		Port port = rasManager.getPortById(sc.getOrgRrn(), equipmentId, portId, portNum);
		rasManager.changePortStateByEvent(port.getObjectRrn(), eventId, comment, checkAuthorityFlag, sc);
	}
	
	
	/**
	 * 修改设备端口的搬送状态
	 * @param port  Port对象
	 * @param transportState 搬送端口状态
	 * @param comment 备注
	 * @param sc 会话上下文
	 * @throws ClientException
	 */
	public void changePortTransferState(Port port, String transportState, String comment, SessionContext sc) throws ClientException{
		rasManager.changePortTransferState(port, transportState, comment, sc);
	}

	/**
	 * 修改设备端口的搬送状态
	 * @param port  Port对象
	 * @param transportState 搬送端口状态
	 * @param portUseType 设备使用类型
	 * @param comment 备注
	 * @param sc 会话上下文
	 * @throws ClientException
	 */
	public void changePortTransferState(Port port, String transportState, String portUseType, String comment, SessionContext sc) throws ClientException{
		rasManager.changePortTransferState(port, transportState, portUseType, comment, sc);
	}
	
	
	/**
	 * 通过事件修改设备端口的搬送状态
	 * @param equipmentId 设备Id
	 * @param portId PortId
	 * @param portNum portNum
	 * @param eventId 事件ID
	 * @param comment 备注
	 *
	 * @throws ClientException
	 */
	public void changePortTransferStateByEvent(String equipmentId, String portId, String portNum, String eventId, String comment, SessionContext sc) throws ClientException{
		Port port = rasManager.getPortById(sc.getOrgRrn(), equipmentId, portId, portNum);
		rasManager.changePortTransferStateByEvent(port, eventId, comment, sc);
	}

	/**
	 * 修改设备端口的搬送状态，指定可以切换的原搬送状态
	 * @param port 端口
	 * @param transportState 搬送端口状态
	 * @param portUseType 端口正在使用状态
	 * @param originTransferStates 原搬送端口状态，多个则以","分隔
	 * @param comment 备注
	 * @param sc 会话上下文
	 * @throws ClientException
	 */
	public void changePortTransferState(Port port, String transportState, String portUseType, String originTransferStates, String comment, SessionContext sc)
			throws ClientException {
		List<String> stateList = new ArrayList<>();
		if (!StringUtil.isEmpty(originTransferStates)) {
			stateList = List.of(originTransferStates.split(","));
		}
		rasManager.changePortTransferState(port, transportState, portUseType, stateList, comment, sc);
	}
	
	/**
	 * 修改PortType(Port Type 没有发生变化时不会触发修改也不会记录历史)
	 * @param equipmentId 设备Id
	 * @param portId PortId
	 * @param portNum portNum
	 * @param targetType 目标type
	 * @param sc 会话上下文
	 *
	 * @throws ClientException
	 */
	public void changePortType(String equipmentId, String portId, String portNum, String targetType, SessionContext sc) throws ClientException{
		Port port = rasManager.getPortById(sc.getOrgRrn(), equipmentId, portId, portNum);
		if(!StringUtils.equals(port.getPortType(),targetType)){
			rasManager.changePortType(port, targetType, sc);
		}
	}

	/**
	 * 修改PortType(Port Type 没有发生变化时不会触发修改也不会记录历史)
	 * @param portList 修改的Port
	 * @param sc 会话上下文
	 *
	 * @throws ClientException
	 */
	public void changePortType(List<Port> portList, SessionContext sc) throws ClientException{
		if(CollectionUtils.isNotEmpty(portList)){
			for(Port tempPort :portList){
				Port port = rasManager.getPortById(sc.getOrgRrn(), tempPort.getParentEqpId(), tempPort.getPortId(), tempPort.getPortNum());
				changePortType(tempPort.getParentEqpId(),tempPort.getPortId(),tempPort.getPortNum(), tempPort.getPortType(), sc);
			}
		}
	}
	
	/**
	 * 修改Port UseType(Port UseType 没有发生变化时不会触发修改也不会记录历史)
	 * @param equipmentId 设备Id
	 * @param portId PortId
	 * @param portNum portNum
	 * @param targetUseType 端口正在使用状态
	 * @param sc 会话上下文
	 *
	 * @throws ClientException
	 *
	 */
	public void changePortUseType(String equipmentId, String portId, String portNum, String targetUseType, SessionContext sc) throws ClientException{
		Port port = rasManager.getPortById(sc.getOrgRrn(), equipmentId, portId, portNum);
		if(!StringUtils.equals(port.getPortUseType(),targetUseType)){
			rasManager.changePortUseType(port, targetUseType, sc);
		}
	}

	/**
	 * 修改PortType(Port UseType 没有发生变化时不会触发修改也不会记录历史)
	 * @param portList 修改的Port
	 * @param sc 会话上下文
	 *
	 * @throws ClientException
	 */
	public void changePortUseType(List<Port> portList, SessionContext sc) throws ClientException{
		if(CollectionUtils.isNotEmpty(portList)){
			for(Port tempPort :portList){
				Port port = rasManager.getPortById(sc.getOrgRrn(), tempPort.getParentEqpId(), tempPort.getPortId(), tempPort.getPortNum());
				changePortUseType(tempPort.getParentEqpId(),tempPort.getPortId(),tempPort.getPortNum(), tempPort.getPortUseType(), sc);
			}
		}
	}
	
	/**
	 * Hold设备端口
	 * @param equipmentId 设备Id
	 * @param portId PortId
	 * @param portNum portNum
	 * @param actionCode Hold码
	 * @param actionReason Hold原因
	 * @param actionComment Hold备注
	 * @param sc 会话上下文
	 *
	 *@throws ClientException
	 */
	public void holdPort(String equipmentId, String portId, String portNum, String actionCode, String actionReason, String actionComment, SessionContext sc) throws ClientException{
		Port port = rasManager.getPortById(sc.getOrgRrn(), equipmentId, portId, portNum);
		rasManager.holdPort(port.getObjectRrn(), actionCode, actionReason, actionComment, sc);
	}
	
	/**
	 * Release设备端口
	 * @param equipmentId 设备Id
	 * @param portId PortId
	 * @param portNum portNum
	 * @param actionCode Release码
	 * @param actionReason Release原因
	 * @param actionComment Release备注
	 * @param sc 会话上下文
	 *
	 * @throws ClientException
	 * 
	 */
	public void releasePort(String equipmentId, String portId, String portNum, String actionCode, String actionReason, String actionComment, SessionContext sc) throws  ClientException{
		Port port = rasManager.getPortById(sc.getOrgRrn(), equipmentId, portId, portNum);
		rasManager.releasePort(port.getObjectRrn(), actionCode, actionReason, actionComment, sc);
	}

}
