package com.glory.mes.ras.rti;

import java.util.Map;

import com.glory.common.rti.msg.alarm.AlarmRTI;
import com.glory.common.rti.msg.alarm.AlarmRTIBody;
import com.glory.mes.ras.port.Port;
import com.glory.msg.RequestHeader;

public class PortAlarmRTI {

	public static final String TRANSACTION_TYPE_LONGSTATE = "LongPortState";
	public static final String TRANSACTION_TYPE_CHANGESTATE = "ChangePortState";
	public static final String TRANSACTION_TYPE_EQPALARM = "PortAlarm";
	
	public static final String ATTRIBUTE_EVENT = "Event";
	public static final String ATTRIBUTE_COMCLASS = "ComClass";
	public static final String ATTRIBUTE_STATE = "State";
	public static final String ATTRIBUTE_SUBSTATE = "SubState";
	public static final String ATTRIBUTE_BEFORE_COMCLASS = "BeforeComClass";
	public static final String ATTRIBUTE_BEFORE_STATE = "BeforeState";
	public static final String ATTRIBUTE_BEFORE_SUBSTATE = "BeforeSubState";

	public static final String ATTRIBUTE_LONG_STATE_INTERVAL = "LongStateInterval";
	
	public static final String OBJECT_TYPE_PORT = "PORT";

	public static AlarmRTI getPortAlarmRTI(String transactionType, Port port, Map<String, String> attributes) {
		AlarmRTI rti = new AlarmRTI(transactionType);
		
		RequestHeader header = rti.getHeader();
		header.setOrgName(port.getOrgName());
		header.setOrgRrn(port.getOrgRrn());		
		AlarmRTIBody body = rti.getBody();
		body.setCategory(AlarmRTIBody.CATEGORY_MES);
		body.setAlarmType(transactionType);
		body.setObjectType(OBJECT_TYPE_PORT);
		body.setObjectId(port.getPortId());
		body.setPortId(port.getPortId());
		//body.setPortType(port.getPortType());
		body.setEquipmentId(port.getParentEqpId());
		body.setObjectOwner(port.getDepartment());
		
		if (attributes != null) {
			if (attributes.containsKey(ATTRIBUTE_EVENT)) {
				body.setAttribute1(attributes.get(ATTRIBUTE_EVENT));
			}
			if (attributes.containsKey(ATTRIBUTE_COMCLASS)) {
				body.setAttribute2(attributes.get(ATTRIBUTE_COMCLASS));
			}
			if (attributes.containsKey(ATTRIBUTE_STATE)) {
				body.setAttribute3(attributes.get(ATTRIBUTE_STATE));
			}
			if (attributes.containsKey(ATTRIBUTE_SUBSTATE)) {
				body.setAttribute4(attributes.get(ATTRIBUTE_SUBSTATE));
			}
			if (attributes.containsKey(ATTRIBUTE_BEFORE_COMCLASS)) {
				body.setAttribute5(attributes.get(ATTRIBUTE_BEFORE_COMCLASS));
			}
			if (attributes.containsKey(ATTRIBUTE_BEFORE_STATE)) {
				body.setAttribute6(attributes.get(ATTRIBUTE_BEFORE_STATE));
			}
			if (attributes.containsKey(ATTRIBUTE_BEFORE_SUBSTATE)) {
				body.setAttribute7(attributes.get(ATTRIBUTE_BEFORE_SUBSTATE));
			}
			if (attributes.containsKey(ATTRIBUTE_LONG_STATE_INTERVAL)) {
				body.setAttribute8(attributes.get(ATTRIBUTE_LONG_STATE_INTERVAL));
			}
		}
		return rti;
	}
}
