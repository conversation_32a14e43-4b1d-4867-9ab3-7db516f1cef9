package com.glory.mes.ras.service;

import java.util.ArrayList;
import java.util.List;

import javax.ejb.EJB;
import javax.ejb.Stateless;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.glory.framework.activeentity.ejb.ADManagerLocal;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ExceptionManager;
import com.glory.framework.core.service.CoreService;
import com.glory.framework.core.util.SessionContext;
import com.glory.mes.base.model.WorkStation;
import com.glory.mes.ras.ejb.RASManagerLocal;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.eqp.EquipmentAction;
import com.glory.mes.ras.eqp.EquipmentCurrent;
import com.glory.mes.ras.eqp.EquipmentHold;
import com.glory.mes.ras.eqp.PositionSetLine;
import com.glory.mes.ras.exception.RasExceptionBundle;

@Stateless
@CoreService(serviceGroup = "MESCore")
public class EquipmentService {
    private static final Logger logger = Logger.getLogger(EquipmentService.class);

    @EJB
    private RASManagerLocal rasManager;

    @EJB
    private ADManagerLocal adManager;

    /**
     * 根据设备号获得设备<br>
     *
     * @param orgRrn        区域号
     * @param equipmentId   设备号
     * @param exceptionFlag 是否抛出异常
     * @return Equipment
     */
    public Equipment getEquipmentByEquipmentId(long orgRrn, String equipmentId, boolean exceptionFlag) throws ClientException {
        return rasManager.getEquipmentByEquipmentId(orgRrn, equipmentId, exceptionFlag);
    }

    /**
     * 按设备组获取设备<br>
     *
     * @param orgRrn        区域号
     * @param eqpGroup      设备组
     * @param exceptionFlag 是否抛出异常
     * @return List<Equipment>
     */
    public List<Equipment> getEquipmentByEqpGroup(long orgRrn, String eqpGroup, boolean exceptionFlag) {
        return rasManager.getEquipmentByEqpGroup(orgRrn, eqpGroup, exceptionFlag);
    }

    /**
     * 获得批次Hold列表<br>
     *
     * @param orgRrn        区域号
     * @param equipmentId   设备号
     * @param exceptionFlag 是否抛出异常
     * @return List<EquipmentHold>
     */
    public List<EquipmentHold> getEquipmentHolds(long orgRrn, String equipmentId, boolean exceptionFlag) throws ClientException {
        Equipment equipment = getEquipmentByEquipmentId(orgRrn, equipmentId, exceptionFlag);
        if (equipment != null) {
            return rasManager.getEquipmentHolds(equipment.getObjectRrn(), exceptionFlag);
        }
        return null;
    }

    /**
     * 获取设备物料位置<br>
     *
     * @param orgRrn        区域号
     * @param equipmentId   设备号
     * @param positionName  位置名称
     * @param exceptionFlag 是否抛出异常
     * @return PositionSetLine
     */
    public PositionSetLine getEquipmentPositionLine(Long orgRrn, String equipmentId, String positionName, boolean exceptionFlag) {
        return rasManager.getEquipmentPositonLine(orgRrn, equipmentId, positionName, exceptionFlag);
    }

    /**
     * 查询设备所有的PositionLine <br>
     *
     * @param orgRrn        区域号
     * @param equipmentId   设备号
     * @param exceptionFlag 是否抛出异常
     * @return List<PositionSetLine>
     */
    public List<PositionSetLine> getEquipmentPositionLines(Long orgRrn, String equipmentId, boolean exceptionFlag) throws ClientException {
        return rasManager.getEquipmentPositionLines(orgRrn, equipmentId, exceptionFlag);
    }

    /**
     * 根据区域ID获取设备信息 <br>
     *
     * @param orgRrn        区域号
     * @param locationId    设备区域ID
     * @param exceptionFlag 是否抛出异常
     * @return List<Equipment>
     */
    public List<Equipment> getEquipmentsByLocationId(long orgRrn, String locationId, boolean exceptionFlag) throws ClientException {
        return rasManager.getEquipmentsByLocationId(orgRrn, locationId, exceptionFlag);
    }

    /**
     * 根据BAY ID获取设备信息 <br>
     *
     * @param orgRrn        区域号
     * @param subLocationId BAY ID
     * @param exceptionFlag 是否抛出异常
     * @return List<Equipment>
     */
    public List<Equipment> getEquipmentsBySubLocationId(long orgRrn, String subLocationId, boolean exceptionFlag) throws ClientException {
        return rasManager.getEquipmentsBySubLocationId(orgRrn, subLocationId, exceptionFlag);
    }

    /**
     * 查找设备的下级子设备 <br>
     *
     * @param orgRrn      区域号
     * @param equipmentId 需查询的设备号
     * @param isCascase 是否级联查询
     * @param exceptionFlag 是否抛出异常
     * @return List<Equipment>
     */
    public List<Equipment> getSubEquipments(long orgRrn, String equipmentId, boolean isCascase, boolean exceptionFlag) throws ClientException {
        return rasManager.getSubEquipments(orgRrn, equipmentId, isCascase, exceptionFlag);
    }
    
    /**
     * 根据设备查询设备当前信息
     * @param equipment	待查询设备
     * @param sc				sessionContext
     * @return
     * @throws ClientException
     */
    public EquipmentCurrent getEquipmentCurrent(Equipment equipment, SessionContext sc) throws ClientException {
    	return rasManager.getEquipmentCurrent(equipment.getObjectRrn(), sc);
    }

    /**
     * 修改设备状态 <br>
     *
     * @param equipmentId    待修改状态的设备号
     * @param targetComClass 目标大类
     * @param targetState    目标状态
     * @param comment        备注
     * @param sc             sessionContext
     */
    public void changeEquipmentState(String equipmentId, String targetComClass, String targetState, String comment, SessionContext sc) throws ClientException {
        Equipment equipment = getEquipmentByEquipmentId(sc.getOrgRrn(), equipmentId, true);
        if (equipment != null) {
            rasManager.changeEquipmentState(equipment, targetComClass, targetState, comment, sc);
        }
    }
    
    /**
     * 修改设备状态 <br>
     *
     * @param equipmentId    待修改状态的设备号
     * @param targetComClass 目标大类
     * @param targetState    目标状态
     * @param targetSubState 目标子状态
     * @param comment        备注
     * @param sc             sessionContext
     */
    public void changeEquipmentState(String equipmentId, String targetComClass, String targetState,String targetSubState, String comment, SessionContext sc) throws ClientException {
        Equipment equipment = getEquipmentByEquipmentId(sc.getOrgRrn(), equipmentId, true);
        if (equipment != null) {
        	targetComClass = StringUtils.isNotBlank(targetComClass) ? targetComClass: targetState;
            rasManager.changeEquipmentState(equipment, targetComClass, targetState,targetSubState,comment, sc);
        }
    }

    /**
     * 修改设备通讯状态,并记录历史 <br>
     *
     * @param equipmentId        待修改通讯状态的设备号
     * @param communicationState 通讯状态
     * @param comment            备注
     * @param sc                 sessionContext
     */
    public void changeEquipmentCommunicationState(String equipmentId, String communicationState, String comment, SessionContext sc) {
        Equipment equipment = getEquipmentByEquipmentId(sc.getOrgRrn(), equipmentId, true);
        if (equipment != null) {
            rasManager.changeEquipmentCommunicationState(equipment, communicationState, comment, sc);
        }
    }

    /**
     * 修改对应设备当前产品 <br>
     * @param equipmentId 设备号
     * @param partName    产品名称
     * @param sc          sessionContext
     */
    public void changeEquipmentPart(String equipmentId, String partName, SessionContext sc) {
        Equipment equipment = getEquipmentByEquipmentId(sc.getOrgRrn(), equipmentId, true);
        if (equipment != null) {
            changeEquipmentPart(equipment, partName, sc);
        }
    }

    /**
     * 修改对应设备当前产品 <br>
     * @param equipment 设备信息
     * @param partName    产品名称
     * @param sc          sessionContext
     */
    public void changeEquipmentPart(Equipment equipment, String partName, SessionContext sc) {
        rasManager.changeEquipmentPart(equipment, partName, sc);
    }

    /**
     * 修改设备作业模式,并记录历史 <br>
     *
     * @param equipmentId 设备号
     * @param processMode 作业模式
     * @param comment     备注
     * @param sc          sessionContext
     */
    public void changeEquipmentProcessMode(String equipmentId, String processMode, String comment, SessionContext sc) {
        Equipment equipment = getEquipmentByEquipmentId(sc.getOrgRrn(), equipmentId, true);
        if (equipment != null) {
            rasManager.changeEquipmentProcessMode(equipment, processMode, comment, sc);
        }
    }

    /**
     * 修改对应设备当前Recipe <br>
     *
     * @param equipmentId     设备号
     * @param equipmentRecipe 设备recipe
     * @param sc              sessionContext
     */
    public void changeEquipmentRecipe(String equipmentId, String equipmentRecipe, SessionContext sc) {
        Equipment equipment = getEquipmentByEquipmentId(sc.getOrgRrn(), equipmentId, true);
        if (equipment != null) {
            rasManager.changeEquipmentRecipe(equipment, equipmentRecipe, sc);
        }
    }

    /**
     * 触发设备事件 <br>
     *
     * @param equipmentId 设备号
     * @param eventId     设备事件号
     * @param comment     备注
     * @param sc          sessionContext
     */
    public void logEvent(String equipmentId, String eventId, String comment, SessionContext sc) throws ClientException {
        Equipment equipment = getEquipmentByEquipmentId(sc.getOrgRrn(), equipmentId, true);
        if (equipment != null) {
            rasManager.logEvent(equipment, eventId, comment, sc);
        }
    }

    /**
     * Hold设备 <br>
     *
     * @param equipmentId   设备号
     * @param actionCode    动作码
     * @param actionReason  动作原因
     * @param actionComment 备注
     * @param sc            sessionContext
     */
    public void holdEquipment(String equipmentId, String actionCode, String actionReason, String actionComment, SessionContext sc) throws ClientException {
        Equipment equipment = getEquipmentByEquipmentId(sc.getOrgRrn(), equipmentId, true);
        if (equipment != null) {
            EquipmentHold equipmentHold = new EquipmentHold();
            equipmentHold.setHoldCode(actionCode);
            equipmentHold.setHoldReason(actionReason);
            equipmentHold.setHoldComment(actionComment);
            rasManager.holdEquipment(equipment, equipmentHold, false, sc);
        }
    }

    /**
     * 放行设备 <br>
     *
     * @param equipmentId   设备号
     * @param holdRrn       暂停Rrn
     * @param actionCode    动作码
     * @param actionReason  动作原因
     * @param actionComment 备注
     * @param sc            sessionContext
     */
    public void releaseEquipment(String equipmentId, long holdRrn, String actionCode, String actionReason, String actionComment, SessionContext sc) throws ClientException {
        Equipment equipment = getEquipmentByEquipmentId(sc.getOrgRrn(), equipmentId, true);        
        if (equipment != null) {
            EquipmentAction equipmentAction = new EquipmentAction();
            equipmentAction.setActionCode(actionCode);
            equipmentAction.setActionComment(actionReason);
            equipmentAction.setActionReason(actionComment);
            List<EquipmentHold> equipmentHolds = rasManager.getEquipmentHolds(equipment.getObjectRrn());
            EquipmentHold eqpHold = equipmentHolds.stream()
                    .filter(u -> u.getObjectRrn().longValue() == holdRrn)
                    .findAny().orElse(null);
            if (eqpHold == null) {
            	throw RasExceptionBundle.bundle.EqpHoldNotFound();
            }            
            List<EquipmentHold> eqpHolds = new ArrayList<EquipmentHold>();
            eqpHolds.add(eqpHold);            		
            rasManager.releaseEquipment(equipment, eqpHolds, equipmentAction, sc);
        }
    }

    /**
     * 删除设备 <br>
     *
     * @param equipmentId 设备号
     * @param sc          sessionContext
     */
    public void deleteEquipment(String equipmentId, SessionContext sc) throws ClientException {
        Equipment equipment = getEquipmentByEquipmentId(sc.getOrgRrn(), equipmentId, true);
        if (equipment != null) {
            rasManager.deleteEquipment(equipment, sc);
        }
    }

    /**
     * 删除多个设备 <br>
     *
     * @param equipmentIds 设备号
     * @param sc           sessionContext
     */
    public void deleteEquipments(List<String> equipmentIds, SessionContext sc) throws ClientException {
        try {
            if (!equipmentIds.isEmpty()) {
                for (String eqpId : equipmentIds) {
                    Equipment equipment = getEquipmentByEquipmentId(sc.getOrgRrn(), eqpId, true);
                    if (equipment != null) {
                        rasManager.deleteEquipment(equipment, sc);
                    }
                }
            }
        } catch (Exception e) {
            throw ExceptionManager.handleException(logger, e);
        }

    }

    /**
     * 保存设备 <br>
     *
     * @param equipment Equipment
     * @param sc        sessionContext
     * @return Equipment
     */
    public Equipment saveEquipment(Equipment equipment, SessionContext sc) throws ClientException {
        Equipment eqp = new Equipment();

        if (equipment.getObjectRrn() != null) {
            eqp.setObjectRrn(equipment.getObjectRrn());
            eqp = (Equipment) adManager.getEntity(eqp);
        }
        eqp = getEquipment(eqp, equipment);
        return rasManager.saveEquipment(501L, eqp, sc);
    }

    /**
     * 保存多个设备 <br>
     *
     * @param equipments List<Equipment>
     * @param sc         sessionContext
     * @return List<Equipment>
     */
    public List<Equipment> saveEquipments(List<Equipment> equipments, SessionContext sc) throws ClientException {
        List<Equipment> saveEquipments = new ArrayList<>();
        for (Equipment e : equipments) {
            Equipment equipment = saveEquipment(e, sc);
            saveEquipments.add(equipment);
        }
        return saveEquipments;
    }

    private Equipment getEquipment(Equipment equipment, Equipment oldEquipment) {
        equipment.setEquipmentId(oldEquipment.getEquipmentId());
        equipment.setDescription(oldEquipment.getDescription());
        equipment.setLocation(oldEquipment.getLocation());
        equipment.setEqpType(oldEquipment.getEqpType());
        equipment.setPositionSetRrn(oldEquipment.getPositionSetRrn());
        equipment.setStatusModelRrn(oldEquipment.getStatusModelRrn());
        equipment.setParentEqpRrn(oldEquipment.getParentEqpRrn());
        equipment.setComClass(oldEquipment.getComClass());
        equipment.setState(oldEquipment.getState());
        equipment.setSubState(oldEquipment.getSubState());
        equipment.setIsMultiPart(oldEquipment.getIsMultiPart());
        equipment.setIsBatch(oldEquipment.getIsBatch());
        equipment.setControlQtyType(oldEquipment.getControlQtyType());
        equipment.setBatchSizeMax(oldEquipment.getBatchSizeMax());
        equipment.setBatchSizeMin(oldEquipment.getBatchSizeMin());
        return equipment;
    }
    
    /**
	 * 删除工作站点的设备信息
	 * @param workStation 工作站点
	 * @param equipments 设备信息
	 * @param sc
	 * @throws ClientException
	 */
	public void deleteEqpStation(WorkStation workStation, List<Equipment> equipments, SessionContext sc) throws ClientException {
		rasManager.deleteEqpStation(workStation, equipments, sc);
	}
	
	 /**
     * 新增工作站点的设备信息
     * @param workStation 工作站点
     * @param equipments 设备信息
     * @param sc
     * @throws ClientException
     */
	public void saveEqpWorkStation(WorkStation workStation, List<Equipment> equipments, SessionContext sc) throws ClientException {
		rasManager.saveEqpWorkStation(workStation, equipments, sc);
	}

	/**
	 * 检查设备Position是否与当前设备匹配
	 * @param orgRrn					厂区ID	
	 * @param equipmentId			设备ID
	 * @param positionName			待检查位置名称
	 * @param isThrowException	是否抛出异常
	 * @return
	 * @throws ClientException
	 */
	public boolean checkEquipmentPosition(long orgRrn, String equipmentId, String positionName, boolean isThrowException) throws ClientException {
		boolean isMatch = false;
		List<PositionSetLine> positionLines = getEquipmentPositionLines(orgRrn, equipmentId, isThrowException);
		if (CollectionUtils.isNotEmpty(positionLines)) {
			for (PositionSetLine positionSetLine : positionLines) {
				if (positionSetLine.getPositionName().equals(positionName)) {
					isMatch = true;
					break;
				}
			}
		}
		if (!isMatch && isThrowException) {
			throw RasExceptionBundle.bundle.EqpPositionNotMatch();
		}
		return isMatch;
	}
	
}
