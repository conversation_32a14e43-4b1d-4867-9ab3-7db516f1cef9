package com.glory.mes.ras.task;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.ejb.EJB;
import javax.ejb.Singleton;
import javax.ejb.Startup;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.apache.log4j.Logger;

import com.glory.common.task.TaskInstanceManager;
import com.glory.common.task.client.TaskManager;
import com.glory.common.task.trans.TaskThreadFactory;
import com.glory.mes.ras.client.RASManager;

@Singleton
@Startup
public class RasTaskManagerBean {

	private static final Logger logger = Logger.getLogger(RasTaskManagerBean.class);
	
	public static final String TASK_GROUP_NAME_MES = "MES";

	@PersistenceContext
	private EntityManager entityManager;
			
	@EJB(lookup="java:global/task/task/TaskManagerBean")
	private TaskManager taskManager;
	
	@EJB
	private RASManager rasManager;
		
	@PostConstruct
	public void initialize() {
		try {
			//初始化service
			while (!TaskInstanceManager.getIsInit().get()) {
				Thread.sleep(1000);
			}
			
			RasTaskContext taskContext = new RasTaskContext();
			taskContext.setEntityManager(entityManager);
			taskContext.setTaskManager(taskManager);
			taskContext.setRasManager(rasManager);
			
			LongEqpStateThread thread = new LongEqpStateThread(taskContext);
			TaskThreadFactory.registerTask(LongEqpStateThread.TASK_NAME, thread);
			
			LongPortStateThread longportStateThread = new LongPortStateThread(taskContext);
			TaskThreadFactory.registerTask(LongPortStateThread.TASK_NAME, longportStateThread);
			taskManager.initTaskGroup(TASK_GROUP_NAME_MES, true);
			
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}
	
	@PreDestroy
	public void destroy() {
		
	}
	
}
