package com.glory.mes.ras.rti;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;

import com.glory.common.rti.RTIExecutorService;
import com.glory.common.rti.model.CommandContext;
import com.glory.common.rti.msg.alarm.AlarmRTI;
import com.glory.framework.core.util.SessionContext;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.eqp.EquipmentAlarm;
import com.glory.mes.ras.eqp.his.EquipmentEventHis;
import com.glory.mes.ras.port.Port;
import com.glory.mes.ras.port.his.PortEventHis;


public class RASRTIUtil {
	
	public static void alarmRti(RTIExecutorService rtiService, String transType, Equipment equipment, EquipmentEventHis his, SessionContext sc) {
		Map<String, String> attributes = new HashMap<String, String>();
		attributes.put(EqpAlarmRTI.ATTRIBUTE_EVENT, his.getEventId());
		attributes.put(EqpAlarmRTI.ATTRIBUTE_COMCLASS, his.getTargetComClass());
		attributes.put(EqpAlarmRTI.ATTRIBUTE_STATE, his.getTargetState());
		attributes.put(EqpAlarmRTI.ATTRIBUTE_SUBSTATE, his.getTargetSubState());
		
		attributes.put(EqpAlarmRTI.ATTRIBUTE_BEFORE_COMCLASS, his.getSourceComClass());
		attributes.put(EqpAlarmRTI.ATTRIBUTE_BEFORE_STATE, his.getSourceState());
		attributes.put(EqpAlarmRTI.ATTRIBUTE_BEFORE_SUBSTATE, his.getSourceSubState());

		AlarmRTI alarmRti = EqpAlarmRTI.getEqpAlarmRTI(transType, equipment, attributes);
		rtiService.scheduleRequest(alarmRti, new Date(), CommandContext.getCommandContext(sc));
	}
	
	public static void alarmRti(RTIExecutorService rtiService, String transType, Port port, PortEventHis his, SessionContext sc) {
		Map<String, String> attributes = new HashMap<String, String>();
		attributes.put(EqpAlarmRTI.ATTRIBUTE_EVENT, his.getEventId());
		attributes.put(EqpAlarmRTI.ATTRIBUTE_COMCLASS, his.getTargetComClass());
		attributes.put(EqpAlarmRTI.ATTRIBUTE_STATE, his.getTargetState());
		attributes.put(EqpAlarmRTI.ATTRIBUTE_SUBSTATE, his.getTargetSubState());
		
		attributes.put(EqpAlarmRTI.ATTRIBUTE_BEFORE_COMCLASS, his.getSourceComClass());
		attributes.put(EqpAlarmRTI.ATTRIBUTE_BEFORE_STATE, his.getSourceState());
		attributes.put(EqpAlarmRTI.ATTRIBUTE_BEFORE_SUBSTATE, his.getSourceSubState());

		AlarmRTI alarmRti = PortAlarmRTI.getPortAlarmRTI(transType, port, attributes);
		rtiService.scheduleRequest(alarmRti, new Date(), CommandContext.getCommandContext(sc));
	}
	
	public static void alarmRti(RTIExecutorService rtiService, String transType, Equipment equipment, Long interval, SessionContext sc) {
		Map<String, String> attributes = new HashMap<String, String>();
		attributes.put(EqpAlarmRTI.ATTRIBUTE_COMCLASS, equipment.getComClass());
		attributes.put(EqpAlarmRTI.ATTRIBUTE_STATE, equipment.getState());
		attributes.put(EqpAlarmRTI.ATTRIBUTE_SUBSTATE, equipment.getSubState());
		
		attributes.put(EqpAlarmRTI.ATTRIBUTE_LONG_STATE_INTERVAL, interval == null ? "" : String.valueOf(interval));

		AlarmRTI alarmRti = EqpAlarmRTI.getEqpAlarmRTI(transType, equipment, attributes);
		rtiService.scheduleRequest(alarmRti, new Date(), CommandContext.getCommandContext(sc));
	}
	
	public static void alarmRti(RTIExecutorService rtiService, String transType, Port port, Long interval, SessionContext sc) {
		Map<String, String> attributes = new HashMap<String, String>();
		attributes.put(EqpAlarmRTI.ATTRIBUTE_COMCLASS, port.getComClass());
		attributes.put(EqpAlarmRTI.ATTRIBUTE_STATE, port.getState());
		attributes.put(EqpAlarmRTI.ATTRIBUTE_SUBSTATE, port.getSubState());
		
		attributes.put(EqpAlarmRTI.ATTRIBUTE_LONG_STATE_INTERVAL, interval == null ? "" : String.valueOf(interval));

		AlarmRTI alarmRti = PortAlarmRTI.getPortAlarmRTI(transType, port, attributes);
		rtiService.scheduleRequest(alarmRti, new Date(), CommandContext.getCommandContext(sc));
	}
	
	@TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
	public static void alarmRti(RTIExecutorService rtiService, String transType, EquipmentAlarm equipmentAlarm, SessionContext sc) {
		AlarmRTI alarmRti = EqpAlarmRTI.getEqpAlarmRTI(transType, equipmentAlarm);
		rtiService.scheduleRequest(alarmRti, new Date(), CommandContext.getCommandContext(sc));
	}	
}
