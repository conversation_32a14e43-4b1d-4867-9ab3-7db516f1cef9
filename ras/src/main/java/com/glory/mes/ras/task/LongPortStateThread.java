package com.glory.mes.ras.task;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.glory.common.task.TaskContext;
import com.glory.common.task.TaskInstanceHis;
import com.glory.common.task.TaskResult;
import com.glory.common.task.TaskThread;

public class LongPortStateThread extends TaskThread {

	private static final Logger logger = Logger.getLogger(LongPortStateThread.class);
	
	public static final String TASK_NAME = "LongPortState";
		
	private RasTaskContext context;
	private List<TaskResult> taskResults;
	
	public LongPortStateThread(TaskContext context) {
		super(context);
		this.context = (RasTaskContext) context;
		this.setName(TASK_NAME);
	}

	@Override
	public List<TaskResult> execute() throws Exception {
		try {
			taskResults = new ArrayList<TaskResult>();
			
			context.getRasManager().longPortStateAlarm();
			
			TaskResult taskResult = new TaskResult();
			taskResult.setResultCode(TaskInstanceHis.RESULT_SUCCESS);
			taskResults.add(taskResult);
		
		} catch (Exception e) {
			TaskResult taskResult = new TaskResult();
			taskResult.setResultCode(TaskInstanceHis.RESULT_EXCEPTION);
			taskResult.setResulteText(e.getMessage());
			taskResults.add(taskResult);
			
			logger.error("execute LongPortStateThread error " + e);
		}
		
		return taskResults;
	}

}
