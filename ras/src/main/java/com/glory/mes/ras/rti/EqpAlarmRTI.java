package com.glory.mes.ras.rti;

import java.util.Map;

import com.glory.common.rti.msg.alarm.AlarmRTI;
import com.glory.common.rti.msg.alarm.AlarmRTIBody;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.eqp.EquipmentAlarm;
import com.glory.msg.RequestHeader;

public class EqpAlarmRTI {

	public static final String TRANSACTION_TYPE_LONGEQPSTATE = "LongEqpState";
	public static final String TRANSACTION_TYPE_CHANGESTATE = "ChangeEqpState";
	public static final String TRANSACTION_TYPE_EQPALARM = "EqpAlarm";
	
	public static final String ATTRIBUTE_EVENT = "Event";
	public static final String ATTRIBUTE_COMCLASS = "ComClass";
	public static final String ATTRIBUTE_STATE = "State";
	public static final String ATTRIBUTE_SUBSTATE = "SubState";
	public static final String ATTRIBUTE_BEFORE_COMCLASS = "BeforeComClass";
	public static final String ATTRIBUTE_BEFORE_STATE = "BeforeState";
	public static final String ATTRIBUTE_BEFORE_SUBSTATE = "BeforeSubState";

	public static final String ATTRIBUTE_LONG_STATE_INTERVAL = "LongStateInterval";

	public static AlarmRTI getEqpAlarmRTI(String transactionType, Equipment equipment, Map<String, String> attributes) {
		AlarmRTI rti = new AlarmRTI(transactionType);
		
		RequestHeader header = rti.getHeader();
		header.setOrgName(equipment.getOrgName());
		header.setOrgRrn(equipment.getOrgRrn());		
		AlarmRTIBody body = rti.getBody();
		body.setCategory(AlarmRTIBody.CATEGORY_MES);
		body.setAlarmType(transactionType);
		body.setObjectType(AlarmRTIBody.OBJECT_TYPE_EQP);
		body.setObjectId(equipment.getEquipmentId());
		body.setEquipmentId(equipment.getEquipmentId());
		body.setEquipmentType(equipment.getEqpType());
		body.setObjectOwner(equipment.getDepartment());
		
		if (attributes != null) {
			if (attributes.containsKey(ATTRIBUTE_EVENT)) {
				body.setAttribute1(attributes.get(ATTRIBUTE_EVENT));
			}
			if (attributes.containsKey(ATTRIBUTE_COMCLASS)) {
				body.setAttribute2(attributes.get(ATTRIBUTE_COMCLASS));
			}
			if (attributes.containsKey(ATTRIBUTE_STATE)) {
				body.setAttribute3(attributes.get(ATTRIBUTE_STATE));
			}
			if (attributes.containsKey(ATTRIBUTE_SUBSTATE)) {
				body.setAttribute4(attributes.get(ATTRIBUTE_SUBSTATE));
			}
			if (attributes.containsKey(ATTRIBUTE_BEFORE_COMCLASS)) {
				body.setAttribute5(attributes.get(ATTRIBUTE_BEFORE_COMCLASS));
			}
			if (attributes.containsKey(ATTRIBUTE_BEFORE_STATE)) {
				body.setAttribute6(attributes.get(ATTRIBUTE_BEFORE_STATE));
			}
			if (attributes.containsKey(ATTRIBUTE_BEFORE_SUBSTATE)) {
				body.setAttribute7(attributes.get(ATTRIBUTE_BEFORE_SUBSTATE));
			}
			if (attributes.containsKey(ATTRIBUTE_LONG_STATE_INTERVAL)) {
				body.setAttribute8(attributes.get(ATTRIBUTE_LONG_STATE_INTERVAL));
			}
		}
		return rti;
	}

	public static AlarmRTI getEqpAlarmRTI(String transactionType, EquipmentAlarm equipmentAlarm) {
		AlarmRTI rti = new AlarmRTI(transactionType);
		
		RequestHeader header = rti.getHeader();
		header.setOrgName(equipmentAlarm.getOrgName());
		header.setOrgRrn(equipmentAlarm.getOrgRrn());		
		AlarmRTIBody body = rti.getBody();
		body.setCategory(AlarmRTIBody.CATEGORY_MES);
		body.setAlarmType(transactionType);
		body.setObjectType(AlarmRTIBody.OBJECT_TYPE_EQP);
		body.setObjectId(equipmentAlarm.getEquipmentId());
		body.setEquipmentId(equipmentAlarm.getEquipmentId());
		body.setEquipmentType(equipmentAlarm.getEquipmentType());
		body.setAlarmCode(equipmentAlarm.getAlarmCode());
		body.setAlarmId(equipmentAlarm.getAlarmId());
		body.setAlarmLevel(equipmentAlarm.getAlarmLevel());
		body.setAlarmState(equipmentAlarm.getAlarmState());
		body.setAlarmText(equipmentAlarm.getAlarmText());
		body.setAlarmTime(equipmentAlarm.getAlarmTime());

		return rti;
	}
}
