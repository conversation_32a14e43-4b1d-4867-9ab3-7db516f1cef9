package com.glory.mes.ras.ejb;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.ejb.Asynchronous;
import javax.ejb.EJB;
import javax.ejb.Local;
import javax.ejb.Remote;
import javax.ejb.Stateless;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.enterprise.inject.Any;
import javax.enterprise.inject.Instance;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.OptimisticLockException;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.glory.common.context.ejb.ContextManagerLocal;
import com.glory.common.rti.RTIExecutorService;
import com.glory.common.state.action.StateEvent;
import com.glory.common.state.ejb.StateManagerLocal;
import com.glory.common.state.model.EventStatus;
import com.glory.common.state.model.StatusModelEvent;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.ejb.ADManagerLocal;
import com.glory.framework.activeentity.ejb.BASManagerLocal;
import com.glory.framework.activeentity.ejb.SysParameterManagerLocal;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ExceptionManager;
import com.glory.framework.core.util.HistoryUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.security.ejb.SecurityManagerLocal;
import com.glory.framework.security.model.ADAccessCategory;
import com.glory.framework.security.model.ADAccessCategoryHis;
import com.glory.framework.security.model.ADUser;
import com.glory.framework.security.model.ADUserGroup;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.base.model.WorkStation;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.config.RasCfMod;
import com.glory.mes.ras.constraint.EqpConstraint;
import com.glory.mes.ras.constraint.EqpConstraintHis;
import com.glory.mes.ras.constraint.RASConstraint;
import com.glory.mes.ras.constraint.RASConstraintCondition;
import com.glory.mes.ras.eqp.Capa;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.eqp.EquipmentAction;
import com.glory.mes.ras.eqp.EquipmentAlarm;
import com.glory.mes.ras.eqp.EquipmentBatchControl;
import com.glory.mes.ras.eqp.EquipmentCapa;
import com.glory.mes.ras.eqp.EquipmentCombine;
import com.glory.mes.ras.eqp.EquipmentCurrent;
import com.glory.mes.ras.eqp.EquipmentGroup;
import com.glory.mes.ras.eqp.EquipmentHold;
import com.glory.mes.ras.eqp.EquipmentStateCascade;
import com.glory.mes.ras.eqp.EquipmentStateColor;
import com.glory.mes.ras.eqp.PositionSet;
import com.glory.mes.ras.eqp.PositionSetLine;
import com.glory.mes.ras.eqp.cdi.EqpContext;
import com.glory.mes.ras.eqp.cdi.IEqpCdiAction;
import com.glory.mes.ras.eqp.his.EquipmentAlarmHis;
import com.glory.mes.ras.eqp.his.EquipmentEventHis;
import com.glory.mes.ras.eqp.his.EquipmentHis;
import com.glory.mes.ras.eqp.his.EquipmentStateCascadeHis;
import com.glory.mes.ras.eqp.his.PositionSetHis;
import com.glory.mes.ras.exception.RasExceptionBundle;
import com.glory.mes.ras.model.EquipmentLine;
import com.glory.mes.ras.model.EquipmentUser;
import com.glory.mes.ras.model.EquipmentWorkStation;
import com.glory.mes.ras.model.RASUserGroupCheck;
import com.glory.mes.ras.model.state.RasEvent;
import com.glory.mes.ras.model.state.RasState;
import com.glory.mes.ras.model.state.RasStatusModel;
import com.glory.mes.ras.model.state.RasSubState;
import com.glory.mes.ras.port.Port;
import com.glory.mes.ras.port.PortRequestReserved;
import com.glory.mes.ras.port.PortTransferState;
import com.glory.mes.ras.port.his.PortEventHis;
import com.glory.mes.ras.port.his.PortHis;
import com.glory.mes.ras.rti.EqpAlarmRTI;
import com.glory.mes.ras.rti.PortAlarmRTI;
import com.glory.mes.ras.rti.RASRTIUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;


@Stateless
@Remote(RASManager.class)
@Local(RASManagerLocal.class)
public class RASManagerBean implements RASManager, RASManagerLocal {
	private static final Logger logger = Logger.getLogger(RASManagerBean.class);
	
	@PersistenceContext
	private EntityManager em;
	
	@EJB 
	private ADManagerLocal adManager;
	
	@EJB
	private SecurityManagerLocal securityManager;
	
	@EJB
	private BASManagerLocal basManager;

	@EJB 
	private SysParameterManagerLocal sysParamManager;

	@EJB 
	private StateManagerLocal stateManager;
	
	@EJB
	private ContextManagerLocal ctxManager;
	
	@EJB(lookup="java:global/rti/rti/RTIExecutorService")
    private RTIExecutorService rtiService;
	
	@Inject
	@Any
	private Instance<IEqpCdiAction> eqpActions;
	
	public EntityManager getEntityManager() {
		return em;
	}
	
	/**
	 * 根据cdiPoint和triggerPoint执行对应的EqpAction
	 * @param eqpContext 设备Context
	 * @param cdiPoint 动作注入点
	 * @param triggerPoint 触发点(PreCheck/PreExecute/PostExecute)
	 */
	public EqpContext executeEqpCdiAction(EqpContext eqpContext, String cdiPoint, String triggerPoint) {
		try {
			List<IEqpCdiAction> triggerActions = EqpContext.getActions(eqpActions.iterator(), cdiPoint, triggerPoint);
			for (IEqpCdiAction triggerAction : triggerActions) {
				eqpContext = triggerAction.invoke(eqpContext);
			}
			return eqpContext;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 保存设备
	 * List<Equipment>
	 */
	public List<Equipment> saveEquipments(long tableRrn, List<Equipment> equipments, SessionContext sc) throws ClientException {
		sc.buildTransInfo();
    	List<Equipment> saveEquipments = new ArrayList<>();
        for (Equipment e : equipments) {
        	Equipment equipment = saveEquipment(tableRrn, e, sc);
        	saveEquipments.add(equipment);
        }
		return saveEquipments;
	}
	
	/**
	 * 保存设备
	 * @param equipment 设备
	 */
	public Equipment saveEquipment(long tableRrn, Equipment equipment, SessionContext sc) throws ClientException {
		try {
			equipment.setIsActive(true);
			boolean editFlag = false;
			if (equipment.getObjectRrn() == null) {
				equipment.setCreatedBy(sc.getUserName());
				equipment.setCreated(new Date());
				//equipment.setStateEntryTime(new Date());
			} else {
				equipment.setUpdatedBy(sc.getUserName());
				equipment.setUpdated(new Date());
				editFlag = true;
			}
			
			List<String> controlStrList = Lists.newArrayList();
			List<EquipmentBatchControl> batchControls = equipment.getBatchControls();
			if (equipment.getObjectRrn() != null) {
				String sql = "DELETE FROM EquipmentBatchControl WHERE ";
				sql += ADBase.BASE_CONDITION_N;
				sql += " AND equipmentId = :equipmentId";
				Query query = em.createQuery(sql);
				query.setParameter("orgRrn", sc.getOrgRrn());
				query.setParameter("equipmentId", equipment.getEquipmentId());
				query.executeUpdate();
			}
			equipment = (Equipment)adManager.saveEntity(tableRrn, equipment, sc);
			
			if (CollectionUtils.isNotEmpty(batchControls)) {
				for (EquipmentBatchControl batchControl : batchControls) {
					batchControl.setObjectRrn(null);
					batchControl.setOrgRrn(sc.getOrgRrn());
					batchControl.setEquipmentId(equipment.getEquipmentId());
					batchControl.setEquipmentRrn(equipment.getObjectRrn());
					adManager.saveEntity(batchControl, sc);
					
					controlStrList.add(batchControl.toString());
				}
			}
			
			//记录历史
			EquipmentHis equipmentHis = new EquipmentHis(equipment, sc);
			if (editFlag) {
				equipmentHis.setTransType(HistoryUtil.TRANSTYPE_UPDATE);
			} else {
				equipmentHis.setTransType(HistoryUtil.TRANSTYPE_CREATE);
			}
			
			if (CollectionUtils.isNotEmpty(controlStrList)) {
				String value = controlStrList.stream().collect(Collectors.joining(";"));
				Map<String, String> commentMap = Maps.newHashMap();
				commentMap.put("BATCHCONTROLS", value);
				equipmentHis.setHisComment(HistoryUtil.buildHisComment(commentMap));
			}
			em.persist(equipmentHis);
			 			
			return equipment;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 通过设备能力RRN获取设备(List)
	 * @param capaRrn 设备能力RRN
	 */
	public List<Equipment> getEquipmentsByCapa(long capaRrn) throws ClientException{
		try {
			Capa capa = em.find(Capa.class, capaRrn);
			if (capa == null) {
				throw RasExceptionBundle.bundle.CapaNotFound();
			}
			String sql = "SELECT Equipment FROM Equipment Equipment WHERE Equipment.objectRrn IN (" +
					"SELECT EquipmentCapa.equipmentRrn FROM EquipmentCapa EquipmentCapa WHERE (EquipmentCapa.orgRrn = :orgRrn OR EquipmentCapa.orgRrn = 0) and EquipmentCapa.capaName = :capaName)";
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", capa.getOrgRrn());
			query.setParameter("capaName", capa.getName());
			List<Equipment> equipments =  (List<Equipment>)query.getResultList();
			if (equipments != null) {
				return equipments;
			}
			return new ArrayList<Equipment>();
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 通过设备能力获取可用设备
	 * @param capaRrn 设备能力RRN
	 * @return
	 * @throws ClientException
	 */
	public List<Equipment> getAvailableEquipmentsByCapa(long capaRrn) throws ClientException{
		try {
			Capa capa = em.find(Capa.class, capaRrn);
			if (capa == null) {
				throw RasExceptionBundle.bundle.CapaNotFound();
			}
			String sql = "SELECT Equipment FROM Equipment Equipment WHERE Equipment.objectRrn IN (" +
					"SELECT EquipmentCapa.equipmentRrn FROM EquipmentCapa EquipmentCapa WHERE ("
					+ "EquipmentCapa.orgRrn = :orgRrn OR EquipmentCapa.orgRrn = 0) and EquipmentCapa.capaName = :capaName and EquipmentCapa.isAvailable = 'Y')";
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", capa.getOrgRrn());
			query.setParameter("capaName", capa.getName());
			List<Equipment> equipments =  (List<Equipment>)query.getResultList();
			if (equipments != null) {
				return equipments;
			}
			return new ArrayList<Equipment>();
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 查找设备的下级子设备
	 * 
	 * @param orgRrn
	 * @param equipmentId 需查询的设备号
	 */
	public List<Equipment> getSubEquipments(long orgRrn, String equipmentId) throws ClientException {
		return getSubEquipments(orgRrn, equipmentId, false);
	}
	

	public List<Equipment> getSubEquipments(long orgRrn, String equipmentId, boolean isCascase) throws ClientException {
		return getSubEquipments(orgRrn, equipmentId, isCascase, true);
	}

	/**
	 * 查找设备的子设备,并支持级联查询
	 *
	 * @param orgRrn
	 * @param equipmentId 需查询的设备号
	 * @param isCascase 是否级联查询
	 * @param exceptionFlag 是否抛出异常
	 */
	public List<Equipment> getSubEquipments(long orgRrn, String equipmentId, boolean isCascase, boolean exceptionFlag) throws ClientException {
		try {
			Equipment equipment = getEquipmentByEquipmentId(orgRrn, equipmentId);
			if (equipment == null) {
				if (!exceptionFlag) {
					return null;
				}
				throw RasExceptionBundle.bundle.EquipmentNoFound();
			}

			return getSubEquipments(orgRrn, equipment, null, isCascase);
		}  catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 查找设备的子设备,并支持级联查询
	 * 
	 * @param orgRrn
	 * @param equipment 需查询的设备
	 * @param allSubEquipments 获得的所有子设备s
	 * @param isCascase 是否级联查询
	 */
	private List<Equipment> getSubEquipments(long orgRrn, Equipment equipment, List<Equipment> allSubEquipments, boolean isCascase) throws ClientException {
		try {
			List<Equipment>	subEquipments = Lists.newArrayList();
			
			if (allSubEquipments == null) {
				allSubEquipments = Lists.newArrayList();
			}
			
			StringBuffer sql = new StringBuffer(" SELECT Equipment FROM Equipment Equipment ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND Equipment.parentEqpRrn = :parentEqpRrn ");

			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("parentEqpRrn", equipment.getObjectRrn());
			List<Equipment> eqps = query.getResultList();
			
			subEquipments.addAll(eqps);
			allSubEquipments.addAll(eqps);
			
			if (isCascase) {
				for (Equipment subEquipment : subEquipments) {
					getSubEquipments(orgRrn, subEquipment, allSubEquipments, isCascase);
				}		
			}
			return allSubEquipments;
		}  catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 删除设备及相关信息,包括
	* 1,设备定义
	* 2,设备权限
	* 3,设备工艺限制
	* 在实际中调用的为LotManager中的deleteEquipment方法(由于需要检查设备是否在使用中)
	*/ 
	public void deleteEquipments(List<Equipment> equipments, SessionContext sc) throws ClientException {
		sc.buildTransInfo();
        for (Equipment e : equipments) {
        	deleteEquipment(e,sc);
        }
	}
	/**
	* 删除设备及相关信息,包括
	* 1,设备定义
	* 2,设备权限
	* 3,设备工艺限制
	* 在实际中调用的为LotManager中的deleteEquipment方法(由于需要检查设备是否在使用中)
	*/ 
	@Override
	public void deleteEquipment(Equipment equipment, SessionContext sc)
			throws ClientException {
		try {
			EqpContext context = new EqpContext();
			context.setSessionContext(sc);
			context.setEquipment(equipment);
			context.setActionType(EqpContext.ACTION_TYPE_DELETE);
			
			context = executeEqpCdiAction(context, IEqpCdiAction.CDI_POINT_DELETE, IEqpCdiAction.TRIGGER_POINT_PRECHECK);
			
			EquipmentHis equipmentHis = new EquipmentHis(equipment, sc);
			equipmentHis.setTransType(HistoryUtil.TRANSTYPE_DELETE);
			em.persist(equipmentHis);
			
			//删掉capa
			String deleteSql = "DELETE FROM EquipmentCapa EquipmentCapa WHERE EquipmentCapa.equipmentRrn = :equipmentRrn";
			Query query = em.createQuery(deleteSql.toString());
			query.setParameter("equipmentRrn", equipment.getObjectRrn());
			query.executeUpdate();
			//删掉与IP的绑定关系
			deleteSql ="DELETE FROM EquipmentWorkStation EquipmentWorkStation WHERE EquipmentWorkStation.equipmentRrn = :equipmentRrn";
			query = em.createQuery(deleteSql.toString());
			query.setParameter("equipmentRrn", equipment.getObjectRrn());
			query.executeUpdate();
			em.remove(em.merge(equipment));
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
		
	}
	
	/**
	 * 设备能力和设备绑定
	 * @param equipment 设备
	 * @param capas 需要绑定的新设备能力
	 * @param oldCapas 绑定设备的旧设备能力
	 */
	@Override
	public void saveEquipmentCapa(Equipment equipment, List<Capa> capas, List<EquipmentCapa> oldCapas,
			SessionContext sc) throws ClientException {
		try {
			//删掉以前绑定的capa
			String sql = "DELETE FROM EquipmentCapa EquipmentCapa WHERE EquipmentCapa.equipmentRrn = :equipmentRrn";
			Query query = em.createQuery(sql.toString());
			query.setParameter("equipmentRrn", equipment.getObjectRrn());
			query.executeUpdate();
			
			//重新绑定capa
			StringBuffer capasBaffer = new StringBuffer();
			if (capas != null && capas.size() > 0) {
				for (Capa capa : capas) {
					EquipmentCapa equipmentCapa = new EquipmentCapa();
					equipmentCapa.setCreated(new Date());
					equipmentCapa.setCreatedBy(sc.getUserName());
					equipmentCapa.setOrgRrn(sc.getOrgRrn());
					equipmentCapa.setEquipmentRrn(equipment.getObjectRrn());
					equipmentCapa.setCapaName(capa.getName());
					equipmentCapa.setCapaRrn(capa.getObjectRrn());
					equipmentCapa.setUpdatedBy(sc.getUserName());
					if (CollectionUtils.isNotEmpty(oldCapas)) {
                        Optional<EquipmentCapa> f = oldCapas.stream().filter(c -> capa.getName().equals(c.getCapaName())).findFirst();
                        if (f.isPresent()) {
                            EquipmentCapa oldCapa = f.get();
                            equipmentCapa.setMaxValue(oldCapa.getMaxValue());
                            equipmentCapa.setMinValue(oldCapa.getMinValue());
                            equipmentCapa.setRangeCheckType(oldCapa.getRangeCheckType());

                            if (oldCapa.getMaxValue() != null && oldCapa.getMinValue() != null) {
                                if (oldCapa.getMaxValue().compareTo(oldCapa.getMinValue()) < 0) {
                                    throw RasExceptionBundle.bundle.MaxMustGreaterThanMin();
                                }
                            }
                        }
                    }

					em.persist(equipmentCapa);
					
					capasBaffer.append(capa.getName() + ";");
				}
			}
			EquipmentHis equipmentHis = new EquipmentHis(equipment, sc);
			equipmentHis.setTransType(EquipmentHis.TRANSTYPE_CHANGECAPA);
			equipmentHis.setHisComment(HistoryUtil.buildHisComment("Capa", capasBaffer.toString()));
			em.persist(equipmentHis);
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * 通过设备RRN获取设备能力
	 * @param equipmentRrn 设备rrn
	 */
	@Override
	public List<Capa> getCapaByEqp(long equipmentRrn) throws ClientException {
		try {
			String sql = "SELECT Capa FROM Capa Capa, EquipmentCapa EquipmentCapa "
					+ "WHERE Capa.name = EquipmentCapa.capaName AND Capa.orgRrn = EquipmentCapa.orgRrn "
					+ "AND EquipmentCapa.equipmentRrn = :equipmentRrn";
			Query query = em.createQuery(sql.toString());
			query.setParameter("equipmentRrn", equipmentRrn);
			return (List<Capa>)query.getResultList();
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}

    /**
     * 根据设备ObjectRrn获取设备与设备能力的绑定信息
     * @param orgRrn
     * @param capaRrn
     * @param eqpRrns
     * @return
     * @throws ClientException
     */
    @Override
    public List<EquipmentCapa> getEquipmentCapaByEqp(long orgRrn, Long capaRrn, Set<Long> eqpRrns)
            throws ClientException {
        try {
            String sql = "SELECT EquipmentCapa FROM EquipmentCapa EquipmentCapa WHERE ";
            sql += ADBase.BASE_CONDITION_N;
            sql += " AND EquipmentCapa.isAvailable = 'Y'";
            sql += " AND EquipmentCapa.equipmentRrn IN :eqpRrns";
            if (capaRrn != null) {
                sql += " AND EquipmentCapa.capaRrn = :capaRrn";
            }

            Query query = em.createQuery(sql);
            query.setParameter("orgRrn", orgRrn);
            query.setParameter("eqpRrns", eqpRrns);
            if (capaRrn != null) {
                query.setParameter("capaRrn", capaRrn);
            }
            return query.getResultList();
        } catch (OptimisticLockException e){
            throw ExceptionManager.handleException(logger, e);
        }
    }

	/**
	 * 通过设备主键查询设备能力
	 * @param equipmentRrn 设备RRN
	 */
	public List<Capa> getAvailableCapaByEqp(long equipmentRrn) throws ClientException{
		return getAvailableCapaByEqp(equipmentRrn,false);
	}

    /**
     * 通过设备主键查询设备能力
     * @param equipmentRrn 设备RRN
	 * @param exceptionFlag 没有查询到数据时是否抛出异常
     */
	public List<Capa> getAvailableCapaByEqp(long equipmentRrn, boolean exceptionFlag) throws ClientException {
		try {
			String sql = "SELECT Capa FROM Capa Capa, EquipmentCapa EquipmentCapa "
					+ "WHERE Capa.name = EquipmentCapa.capaName AND Capa.orgRrn = EquipmentCapa.orgRrn "
					+ "AND EquipmentCapa.equipmentRrn = :equipmentRrn AND EquipmentCapa.isAvailable = 'Y'";
			Query query = em.createQuery(sql.toString());
			query.setParameter("equipmentRrn", equipmentRrn);

			List<Capa> capas = (List<Capa>)query.getResultList();
			if(CollectionUtils.isEmpty(capas) && exceptionFlag){
				throw RasExceptionBundle.bundle.CapaNotFound();
			}
			return capas;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 保存用户组检查设备权限
	* @param userGroupChecks 用户组权限校验
	*/
	public void saveUserGroupCheck(List<ADBase> userGroupChecks, SessionContext sc) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" DELETE FROM RASUserGroupCheck ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", sc.getOrgRrn());
			query.executeUpdate();
			
			adManager.saveEntityList(userGroupChecks, sc);
			
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 检查用户所属的用户组是否需要检查设备权限
	* @return
	*/
	public boolean checkUserGroup(SessionContext sc) throws ClientException {
		boolean checkFlag = false;
		ADUser user = securityManager.getUserByUserName(sc.getOrgRrn(), sc.getUserName());
		user = em.find(ADUser.class, user.getObjectRrn());
		List<ADUserGroup> userGroups = user.getUserGroups();
		if (CollectionUtils.isEmpty(userGroups)) {
			return checkFlag;
		}
		List<RASUserGroupCheck> userGroupChecks = adManager.getEntityList(sc.getOrgRrn(), RASUserGroupCheck.class);
		for (ADUserGroup userGroup : userGroups) {
			for (RASUserGroupCheck check : userGroupChecks) {
				if (userGroup.getObjectRrn().equals(check.getUserGroupRrn())) {
					checkFlag = true;
					break;
				}
			}
		}
		return checkFlag;
	}
	
	/**
	* 检查用户的设备权限
	* @param eqpType 设备类型
	* @param equipmentRrn 设备RRN
	* @param checkUserGroup 是否校验用户组
	* @return
	*/
	public boolean checkEquipmentUser(String eqpType, long equipmentRrn, boolean checkUserGroup, SessionContext sc) throws ClientException {
		try {
			if (checkUserGroup && !checkUserGroup(sc)) {
				return true;
			}
			String whereClause = " userName = '" + sc.getUserName() 
					+ "' AND (eqpType = '" + eqpType
					+ "' OR equipmentRrn = '" + equipmentRrn 
					+ "')";
			List<EquipmentUser> equipmentUsers = adManager.getEntityList(sc.getOrgRrn(), EquipmentUser.class, 1, whereClause, "");
			if (equipmentUsers.size() == 0) {
				return false;
			}
			EquipmentUser equipmentUser = equipmentUsers.get(0);
			if (equipmentUser.getOperateExpiry() != null && equipmentUser.getOperateExpiry().compareTo(new Date()) <= 0) {
				return false;
			}
			if (equipmentUser.getOperateLife() != null) {
				GregorianCalendar gCalendar = new GregorianCalendar();
				gCalendar.setTime(equipmentUser.getOperateLast());
				gCalendar.add(Calendar.DATE, equipmentUser.getOperateLife().intValue());
				if (gCalendar.getTime().compareTo(new Date()) <= 0) {
					return false;
				}
			}
			return true;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 对传进来的设备检查用户是否有权限
	 * @param equipments 进行权限判断的设备
	 * @param checkUserGroup 是否用户组判断
	 * @param sc
	 */
	public List<Equipment> getAvailableEquipmentByUser(List<Equipment> equipments, boolean checkUserGroup, SessionContext sc) throws ClientException {
		try {
			if (checkUserGroup && !checkUserGroup(sc)) {
				return equipments;
			}		
			
			String whereClause = " userName = '" + sc.getUserName() + "'";
			List<EquipmentUser> equipmentUsers = adManager.getEntityList(sc.getOrgRrn(), EquipmentUser.class, Integer.MAX_VALUE, whereClause, " equipmentId ");
			if (equipmentUsers.size() == 0) {
				return equipments;
			}
			
			List<Equipment> availableEquipments = new ArrayList<Equipment>();
			for (Equipment equipment : equipments) {
				boolean isExist = false;
				for (EquipmentUser equipmentUser : equipmentUsers) {	
					if (!StringUtil.isEmpty(equipmentUser.getEquipmentId())) {
						if (equipment.getEquipmentId().equals(equipmentUser.getEquipmentId())) {
							isExist = true;		
						}
					} else if (!StringUtil.isEmpty(equipmentUser.getEqpType())) {
						if (equipment.getEqpType().equals(equipmentUser.getEqpType())) {
							isExist = true;						
						}
					}
					if (isExist) {				
						if (equipmentUser.getOperateExpiry() != null && equipmentUser.getOperateExpiry().compareTo(new Date()) > 0) {
							availableEquipments.add(equipment);
						}
						if (equipmentUser.getOperateLife() != null) {
							GregorianCalendar gCalendar = new GregorianCalendar();
							gCalendar.setTime(equipmentUser.getOperateLast());
							gCalendar.add(Calendar.DATE, equipmentUser.getOperateLife().intValue());
							if (gCalendar.getTime().compareTo(new Date()) > 0) {
								availableEquipments.add(equipment);
							}
						}					
						break;
					}
				}
				
				if (!isExist) {
					availableEquipments.add(equipment);
				}
			}
			return availableEquipments;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 根据设备能力获得可用设备,同时检查设备权限及设备状态
	* 如果权限和状态不可用,则将设备设置为非Available,同时说明原因
	* @return
	*/ 
	public List<Equipment> getAvailableEquipments(long capaRrn, SessionContext sc) throws ClientException{
		List<Equipment> equipments = getAvailableEquipmentsByCapa(capaRrn);
		
		return getAvailableEquipments(equipments, sc);
	}
	
	/**
	* 根据设备能力获得可用设备,同时检查设备权限及设备状态,需要检查批次类型
	* 如果权限和状态不可用,则将设备设置为非Available,同时说明原因
	* @return
	*/ 
	public List<Equipment> getAvailableEquipments(long capaRrn, String lotType, SessionContext sc) throws ClientException{
		List<Equipment> equipments = getAvailableEquipmentsByCapa(capaRrn);
		
		return getAvailableEquipments(equipments, lotType, sc);
	}
	
	/**
	* 检查设备权限及设备状态,不检查批次类型
	* 如果权限和状态不可用,则将设备设置为非Available,同时说明原因
	* @param equipments 设备
	* @return
	*/ 
	public List<Equipment> getAvailableEquipments(List<Equipment> equipments, SessionContext sc) throws ClientException{
		return getAvailableEquipments(equipments, null, sc);
	}
	
	/**
	* 检查设备权限及设备状态
	* 如果权限和状态不可用,则将设备设置为非Available,同时说明原因
	* @param equipments 设备
	* @return
	*/ 
	public List<Equipment> getAvailableEquipments(List<Equipment> equipments, String lotType, SessionContext sc) throws ClientException{
		List<Equipment> availEquipments = new ArrayList<Equipment>();
		
		boolean checkFlag  = checkUserGroup(sc);
		List<EquipmentLine> eqpLines = null;
		boolean isUseLine = MesCfMod.isUseLine(sc.getOrgRrn(), sysParamManager);
		if (isUseLine) {
			eqpLines = adManager.getEntityList(sc.getOrgRrn(), EquipmentLine.class, Integer.MAX_VALUE, 
					" lineId " + sc.getLineClause(), "");
		}
		
		for (Equipment equipment : equipments) {
			if (isUseLine) {
				//检查设备是否在用户可操作的线别中
				boolean existFlag = false;
				for (EquipmentLine eqpLine : eqpLines) {
					if (equipment.getObjectRrn().equals(eqpLine.getEquipmentRrn())) {
						existFlag = true;
						break;
					}
				}
				if (!existFlag) {
					continue;
				}
			}
			
			boolean stateAvaliable = true;
			if (Equipment.HOLDSTATE_OFF.equals(equipment.getHoldState())) {
				if (MesCfMod.isLotProcessByEqpSubState(equipment.getOrgRrn(), sysParamManager) 
						&& !StringUtil.isEmpty(equipment.getSubState())) {
					String subStateId = equipment.getSubState();
					RasSubState subState = getSubState(equipment.getOrgRrn(), subStateId);
					if (!subState.getIsAvailable()) {
						stateAvaliable = false;
						equipment.setIsAvailable(false);
						equipment.setMessage(RasExceptionBundle.bundle.ErrorEqpStateNotAllow());
					} else if (!StringUtil.isEmpty(lotType) && !StringUtil.isEmpty(subState.getAvailableLotType())) {
						//如果有定义检查批次类型
						if (!subState.getAvailableLotTypes().contains(lotType)) {
							stateAvaliable = false;
							equipment.setIsAvailable(false);
							equipment.setMessage(RasExceptionBundle.bundle.ErrorEqpStateNotAllow());
						}
					}
					
				} else {
					String stateId = equipment.getState();
					RasState state = getState(equipment.getOrgRrn(), stateId);
					if (!state.getIsAvailable()) {
						stateAvaliable = false;
						equipment.setIsAvailable(false);
						equipment.setMessage(RasExceptionBundle.bundle.ErrorEqpStateNotAllow());
					} else if (!StringUtil.isEmpty(lotType) && !StringUtil.isEmpty(state.getAvailableLotType())) {
						//如果有定义检查批次类型
						if (!state.getAvailableLotTypes().contains(lotType)) {
							stateAvaliable = false;
							equipment.setIsAvailable(false);
							equipment.setMessage(RasExceptionBundle.bundle.ErrorEqpStateNotAllow());
						}
					}
				}
			} else {
				stateAvaliable = false;
				equipment.setIsAvailable(false);
				equipment.setMessage(RasExceptionBundle.bundle.ErrorEqpStateNotAllow());
			}
			
			
			if (stateAvaliable) {
				if (checkFlag && !checkEquipmentUser(equipment.getEqpType(), equipment.getObjectRrn(), false, sc)) {
					equipment.setIsAvailable(false);
					equipment.setMessage(RasExceptionBundle.bundle.RasEqpUserNoAuthority());
				} else {
					equipment.setIsAvailable(true);
				}
				availEquipments.add(equipment);
			} else {
				equipment.setIsAvailable(false);
				equipment.setMessage(Equipment.HOLDSTATE_OFF.equals(equipment.getHoldState()) 
						? RasExceptionBundle.bundle.EqpStateIsNotAllow().getErrorCode() : RasExceptionBundle.bundle.EqpStateIsHeld().getErrorCode());
				availEquipments.add(equipment);
			}
		}
		return availEquipments;
	}
	
	/**
	* 获得设备状态定义,为null时抛出异常
	* @param orgRrn
	* @param stateId
	*/ 
	public RasState getState(long orgRrn, String stateId) throws ClientException { 
		try {
			StringBuffer sql = new StringBuffer(" SELECT RasState FROM RasState RasState ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND state = :state ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("state", stateId);
			List<RasState> states = query.getResultList();
			if (states.size() == 0) {
				throw RasExceptionBundle.bundle.StateIsNotFound();
			}
			return states.get(0);
		}  catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 获得设备子状态定义,为null时抛出异常
	* @param orgRrn
	* @param subState
	*/ 
	public RasSubState getSubState(long orgRrn, String subState) throws ClientException { 
		try {
			StringBuffer sql = new StringBuffer(" SELECT RasSubState FROM RasSubState RasSubState ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND subState = :subState ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("subState", subState);
			List<RasSubState> states = query.getResultList();
			if (states.size() == 0) {
				throw RasExceptionBundle.bundle.StateIsNotFound();
			}
			return states.get(0);
		}  catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 保存设备状态模型
	 * @param statusModel 设备状态模型
	 */
	public RasStatusModel saveStatusModel(long tableRrn, RasStatusModel statusModel, SessionContext sc) throws ClientException {
		try {
			statusModel.setIsActive(true);
			statusModel.setUpdatedBy(sc.getUserName());
			if (statusModel.getObjectRrn() == null) {
				statusModel.setCreatedBy(sc.getUserName());
				statusModel.setCreated(new Date());
			}
			statusModel = (RasStatusModel)adManager.saveEntity(tableRrn, statusModel, sc);
			
			String whereClause = " modelRrn IS NULL ";
			List<StatusModelEvent> modelEvents = adManager.getEntityList(sc.getOrgRrn(), StatusModelEvent.class, Integer.MAX_VALUE, whereClause, "");
			for (StatusModelEvent modelEvent : modelEvents) {
				adManager.deleteEntity(modelEvent, sc);
			}
			return statusModel;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * 通过设备RRN获取该设备可用事件
	 * @param equipmentRrn 设备RRN
	 */
	public List<RasEvent> getAvaliableEvents(long equipmentRrn, SessionContext sc) throws ClientException {
		try {
			List<RasEvent> eqpEvents = new ArrayList<RasEvent>();
			Equipment equipment = em.find(Equipment.class, equipmentRrn);
			if (equipment.getStatusModelRrn() == null) {
				throw RasExceptionBundle.bundle.LogEventNoStatusModelFound();
			}
			RasStatusModel statusModel = em.find(RasStatusModel.class, equipment.getStatusModelRrn());
			
//			ADUser user = securityManager.doLogin(sc.getOrgRrn(), sc.getUserName());	
			ADUser user = securityManager.getUserByUserName(sc.getOrgRrn(), sc.getUserName());
			user = (ADUser) adManager.getEntity(user);
			for (StatusModelEvent modelEvent : statusModel.getModelEvents()) {
				if (modelEvent.getUserGroups() != null && modelEvent.getUserGroups().size() > 0) {
					for (ADUserGroup group : user.getUserGroups()) {
						for (ADUserGroup modelGroup : modelEvent.getUserGroups()) {
							if (group.getObjectRrn().equals(modelGroup.getObjectRrn())) {
								RasEvent event = em.find(RasEvent.class, modelEvent.getEventRrn());
								EventStatus allowStatus = getCurrentEventStatus(equipment, event);
								if (allowStatus != null) {
									if (!eqpEvents.contains(event)) {
										eqpEvents.add(event);
									}
								}
							}
						}
					}
				} else {
					RasEvent event = em.find(RasEvent.class, modelEvent.getEventRrn());
					EventStatus allowStatus = getCurrentEventStatus(equipment, event);
					if (allowStatus != null) {
						if (!eqpEvents.contains(event)) {
							eqpEvents.add(event);
						}
					}
				}
			}
			return eqpEvents;			
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 记录设备事件,同时修改主设备与子设备状态
	* (注意这里的子设备,必须是在设备的subEquipments栏位中传递过来的设备,而不是所有的子设备)
	* 
	* @param equipment 	主设备
	* @param eventId    设备事件号
	* @param comment 	备注
	* @param sc
	* @return
	*/ 
	public boolean logEvent(Equipment equipment, String eventId, String comment, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			EquipmentAction action = new EquipmentAction();
			action.setActionComment(comment);
			
			return logEvent(equipment, eventId, action, sc);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
	    }
	}
	
	/**
	* 记录设备事件,同时修改主设备与子设备状态
	* (注意这里的子设备,必须是在设备的subEquipments栏位中传递过来的设备,而不是所有的子设备)
	* 
	* @param equipment 	主设备
	* @param eventId    设备事件号
	* @param action 	设备动作
	* @param sc
	* @return
	*/ 
	public boolean logEvent(Equipment equipment, String eventId, EquipmentAction action, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			boolean isChange = logEvent(equipment.getObjectRrn(), eventId, action, sc);
			
			if (equipment.getSubEquipments() != null && equipment.getSubEquipments().size() > 0) {
				for (Equipment subEquipment : equipment.getSubEquipments()) {
					this.logEvent(subEquipment.getObjectRrn(), eventId, action, sc);
				}
			}
			return isChange;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
	    }
	}
	
	
	/**
	* 记录设备事件,同时修改设备状态
	* @param equipmentRrn 	设备ObjectRrn
	* @param eventId 		设备事件号
	* @param comment 		备注
	* @param sc
	* @return
	*/
	public boolean logEvent(long equipmentRrn, String eventId, String comment, SessionContext sc) throws ClientException {
		sc.buildTransInfo();
		try {
			EquipmentAction action = new EquipmentAction();
			action.setActionComment(comment);
			
			return logEvent(equipmentRrn, eventId, action, sc);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 记录设备事件,同时修改设备状态
	* 
	* @param equipmentRrn 	设备ObjectRrn
	* @param eventId 		设备事件号
	* @param action 		动作备注
	* @param sc
	* @return
	*/ 
	public boolean logEvent(long equipmentRrn, String eventId, EquipmentAction action, SessionContext sc) throws ClientException {
		sc.buildTransInfo();
		try {
			Equipment equipment = em.find(Equipment.class, equipmentRrn);
			if (equipment.getStatusModelRrn() == null) {
				throw RasExceptionBundle.bundle.LogEventNoStatusModelFound();
			}

			//检查设备状态模型
			RasStatusModel statusModel = em.find(RasStatusModel.class, equipment.getStatusModelRrn());
			List<StatusModelEvent> modelEvents = statusModel.getModelEvents();
			RasEvent event = null;

			for (StatusModelEvent statusModelEvent : modelEvents) {
				if (eventId.equals(statusModelEvent.getEvent().getEventId())) {
					event = (RasEvent) statusModelEvent.getEvent();
				}
			}

			if (event == null) {
				String whereClause = " eventId = '" + eventId + "'";
				List<RasEvent> eqpEvents = adManager.getEntityList(sc.getOrgRrn(), RasEvent.class, 1, whereClause, "");
				if (CollectionUtils.isNotEmpty(eqpEvents)) {
					event = eqpEvents.get(0);
				}
			}

			if (event == null) {
				if (!RasEvent.EVENT_TRACKIN.equals(eventId) &&
						!RasEvent.EVENT_TRACKOUT.equals(eventId)&&
						!RasEvent.EVENT_ABORT.equals(eventId)) {
					throw RasExceptionBundle.bundle.LogEventNoEventFound();
				} else {
					return false;
				}
			} else {
				event = em.find(RasEvent.class, event.getObjectRrn());
			}
			
			//TrackIn、TrackOut、Abort是系统事件不需要检查设备模型和权限
			if (!RasEvent.EVENT_TRACKIN.equals(event.getEventId()) &&
					!RasEvent.EVENT_TRACKOUT.equals(event.getEventId())&&
					!RasEvent.EVENT_ABORT.equals(event.getEventId())) {
				StatusModelEvent modelEvent = null;
				for (StatusModelEvent cModelEvent : statusModel.getModelEvents()) {
					if (cModelEvent.getEventRrn().equals(event.getObjectRrn())) {
						modelEvent = cModelEvent;
						break;
					}
				}
				if (modelEvent == null) {
					throw RasExceptionBundle.bundle.LogEventNoStatusModelEventFound();
				}
				//检查设备权限
				if (modelEvent.getUserGroups() != null && modelEvent.getUserGroups().size() > 0) {
					ADUser user = securityManager.getUserByUserName(sc.getOrgRrn(), sc.getUserName());
					boolean authorityFlag = false;
					for (ADUserGroup group : user.getUserGroups()) {
						for (ADUserGroup modelGroup : modelEvent.getUserGroups()) {
							if (group.getObjectRrn().equals(modelGroup.getObjectRrn())) {
								authorityFlag = true;
								break;
							}
						}
					}
					if (!authorityFlag) {
						throw RasExceptionBundle.bundle.LogEventUserCannotLogThisEvent();
					}
				}
			}
			
			EventStatus allowStatus = getCurrentEventStatus(equipment, event);
			//如果未找到允许状态,则抛出异常或直接返回
			if (allowStatus == null) {
				if (!RasEvent.EVENT_TRACKIN.equals(eventId) &&
						!RasEvent.EVENT_TRACKOUT.equals(eventId)&&
						!RasEvent.EVENT_ABORT.equals(eventId)) {
					throw RasExceptionBundle.bundle.LogEventStatusNotAllow();
				} else {
					return false;
				}
			}
		
			if (action == null) {
				action = new EquipmentAction();
			}
			action.setEventId(event.getEventId());
			boolean isChange = changeEquipmentState(equipment, allowStatus.getTargetComClass(), allowStatus.getTargetState(), allowStatus.getTargetSubState(), action, sc); 

			if (isChange && sysParamManager.getBooleanSysParameterValue(sc.getOrgRrn(), StateEvent.SYSPARAM_MES_STATE_TRIGGER_EVENT_ACTION)) {
				stateManager.triggerStateEventAction(Equipment.OBJECT_TYPE, 
						equipment.getObjectRrn(), equipment, equipment.getState(), allowStatus.getTargetState(), sc);
			}
				
			return isChange;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 设备状态级联（子设备状态修改触发父设备状态变化）
	 * @param parentEqpRrn 父设备RRN
	 * @param comClass 子设备目标状态大类
	 * @param state 子设备目标状态
	 * @param subState 子设备目标子状态
	 * @param sc
	 * @throws ClientException
	 */
	public void logEventCascade(Long parentEqpRrn, String comClass, String state, String subState, EquipmentAction action, SessionContext sc) throws ClientException {
		sc.buildTransInfo();
		try {
			Equipment parentEqp = em.find(Equipment.class, parentEqpRrn);
			
			String sql = "SELECT EquipmentStateCascade FROM EquipmentStateCascade EquipmentStateCascade WHERE ";
			sql += ADBase.BASE_CONDITION_N;
			sql += " AND isEnable = 'Y'";
			sql += " AND equipmentId = :equipmentId";
			sql += " AND state = :state";
			if (!StringUtil.isEmpty(subState)) {
				sql += " AND subState = :subState";
			}
			
			Query query = em.createQuery(sql);
			query.setParameter("orgRrn", sc.getOrgRrn());
			query.setParameter("equipmentId", parentEqp.getEquipmentId());
			query.setParameter("state", state);
			if (!StringUtil.isEmpty(subState)) {
				query.setParameter("subState", subState);
			}
			List<?> objects = query.getResultList();
			if (CollectionUtils.isEmpty(objects)) {
				return;
			}
			
			EquipmentStateCascade stateCascade = (EquipmentStateCascade) objects.get(0);
			List<Equipment> subEqps = getSubEquipments(sc.getOrgRrn(), parentEqp.getEquipmentId());
			
			Map<String, Boolean> unitAvailables = Maps.newHashMap();
			for (Equipment subEqp : subEqps) {
				unitAvailables.put(subEqp.getEquipmentId(), Boolean.FALSE);
				if (!subEqp.getComClass().equals(comClass)) {
					continue;
				}
				
				if (!subEqp.getState().equals(state)) {
					continue;
				}
				
				if (!StringUtil.isEmpty(subState)) {
					if (!subState.equals(subEqp.getSubState())) {
						continue;
					}
				}
				
				unitAvailables.put(subEqp.getEquipmentId(), Boolean.TRUE);
			}
			
			if (stateCascade.checkCondition(unitAvailables)) {
				changeEquipmentState(parentEqp, comClass, state, subState, action, sc); 
			}
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	* 根据设备当前状态和设备事件,找到第一个满足条件的可转换设备状态
	* @param equipment 		当前设备
	* @param event 			设备事件
	* @return
	*/ 
	public EventStatus getCurrentEventStatus(Equipment equipment, RasEvent event) throws ClientException {
		try {
			//获得事件的状态列表,并根据CHECKFLAG进行分类
			List<EventStatus> eventStatus = event.getEventStatus();
			List<EventStatus> rejectEventStatus = new ArrayList<EventStatus>();
			List<EventStatus> allowEventStatus = new ArrayList<EventStatus>();
			for (EventStatus status : eventStatus) {
				if (EventStatus.CHECKFLAG_REJECT.equalsIgnoreCase(status.getCheckFlag())) {
					rejectEventStatus.add(status);
				} else {
					allowEventStatus.add(status);
				}
			}
			//先检查拒绝状态,看当前设备的状态是否被拒绝,如果被拒绝抛出异常
			for (EventStatus rejectStatus : rejectEventStatus) {
				if (EventStatus.ALL_FLAG.equalsIgnoreCase(rejectStatus.getSourceComClass())) {
					throw RasExceptionBundle.bundle.LogEventStatusNotAllow();
				}
				if (rejectStatus.getSourceComClass().equalsIgnoreCase(equipment.getComClass())
					&& EventStatus.ALL_FLAG.equalsIgnoreCase(rejectStatus.getSourceState())) {
					throw RasExceptionBundle.bundle.LogEventStatusNotAllow();
				}
				if (rejectStatus.getSourceComClass().equalsIgnoreCase(equipment.getComClass())
					&& rejectStatus.getSourceState().equalsIgnoreCase(equipment.getState())
					&& EventStatus.ALL_FLAG.equalsIgnoreCase(rejectStatus.getSourceSubState())) {
					throw RasExceptionBundle.bundle.LogEventStatusNotAllow();
				}
				if (rejectStatus.getSourceComClass().equalsIgnoreCase(equipment.getComClass())
					&& rejectStatus.getSourceState().equalsIgnoreCase(equipment.getState())
					&& rejectStatus.getSourceSubState().equalsIgnoreCase(equipment.getSubState())) {
					throw RasExceptionBundle.bundle.LogEventStatusNotAllow();
				}
			}
			//检查允许状态,找到第一个满足条件的设备状态
			for (EventStatus allowStatus : allowEventStatus) {
				if (EventStatus.ALL_FLAG.equalsIgnoreCase(allowStatus.getSourceComClass())) {
					return allowStatus;
				}
				if (allowStatus.getSourceComClass().equalsIgnoreCase(equipment.getComClass())
					&& EventStatus.ALL_FLAG.equalsIgnoreCase(allowStatus.getSourceState())) {
					return allowStatus;
				}
				if (allowStatus.getSourceComClass().equalsIgnoreCase(equipment.getComClass())
					&& allowStatus.getSourceState().equalsIgnoreCase(equipment.getState())
					&& EventStatus.ALL_FLAG.equalsIgnoreCase(allowStatus.getSourceSubState())) {
					return allowStatus;
				}
				if (allowStatus.getSourceComClass().equalsIgnoreCase(equipment.getComClass())
					&& allowStatus.getSourceState().equalsIgnoreCase(equipment.getState())
					&& allowStatus.getSourceSubState().equalsIgnoreCase(equipment.getSubState())) {
					return allowStatus;
				}
			}
			return null;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 直接变更设备状态(只能用于自动化作业时),MES中作业禁止使用此方法直接修改设备状态
	 * 
	 * @param equipment 待更新状态的设备
	 * @param targeComClass 目标状态大类
	 * @param targetState 目标状态
	 * @param comment 备注
	 * @param sc
	 */
	@Override
	public boolean changeEquipmentState(Equipment equipment, String targeComClass, 
	    		String targetState, String comment, SessionContext sc) throws ClientException {
		EquipmentAction action = new EquipmentAction();
		action.setActionComment(comment);
		return changeEquipmentState(equipment, targeComClass, targetState, null, action, sc);
	}
	/**
	 * 直接变更设备状态(只能用于自动化作业时),MES中作业禁止使用此方法直接修改设备状态
	 *
	 * @param equipment 待更新状态的设备
	 * @param targeComClass 目标状态大类
	 * @param targetState 目标状态
	 * @param comment 备注
	 * @param sc
	 */
	@Override
	public boolean changeEquipmentState(Equipment equipment, String targeComClass,
										String targetState,String targetSubState, String comment, SessionContext sc) throws ClientException {
		EquipmentAction action = new EquipmentAction();
		action.setActionComment(comment);
		return changeEquipmentState(equipment, targeComClass, targetState, targetSubState, action, sc);
	}
	
	/**
	 * 变更设备状态;自动化与MES都会调用此方法, 区别在于action.getEventId()是否有值
	 * 
	 * @param equipment 待更新状态的设备
	 * @param targeComClass 目标状态大类
	 * @param targetState 目标状态
	 * @param targetSubState 目标子状态
	 * @param action 设备Action
	 * @param sc
	 * 
	 * @return 如果设备状态变更成功(或者设备状态一直)则返回true,否则返回false
	 */
    public boolean changeEquipmentState(Equipment equipment, String targeComClass, 
    		String targetState, String targetSubState, EquipmentAction action, SessionContext sc) throws ClientException {
        try {
        	sc.buildTransInfo();
        	
			equipment = em.find(Equipment.class, equipment.getObjectRrn());
        	if (ObjectUtils.equals(equipment.getComClass(), targeComClass)
        			&& ObjectUtils.equals(equipment.getState(), targetState)
        			&& ObjectUtils.equals(equipment.getSubState(), targetSubState)) {
        		//没有变化则不修改
        		return true;
        	}
        	
        	Date lastStateEntryTime = equipment.getStateEntryTime();     	
        	// 获取新的stateEntryTime
        	equipment.setStateEntryTime(new Date());
        	
			EquipmentCurrent current = getEquipmentCurrent(equipment.getObjectRrn(), sc);
            EquipmentEventHis his = new EquipmentEventHis(equipment, current, sc);
    		his.setEventId(EquipmentEventHis.TRANSTYPE_STATE);
    		his.setTargetComClass(targeComClass);
            his.setTargetState(targetState);
            his.setTargetSubState(targetSubState);
    		his.setOperator(sc.getUserName());
			if (action != null) {
				if (StringUtils.isNotEmpty(action.getEventId())) {
					his.setEventId(action.getEventId());
				} 
				his.setActionCode(action.getActionCode());
				his.setActionReason(action.getActionReason());
				his.setActionComment(action.getActionComment());	
			}
			
			EqpContext context = new EqpContext();
			context.setSessionContext(sc);
			context.setData(EqpContext.EQP_LAST_STATE, equipment.getState());
			// 设置新的设备状态
			equipment.setComClass(targeComClass);
			equipment.setState(targetState);
			equipment.setSubState(targetSubState);
			
			context.setEquipment(equipment);
			context.setActionType(EqpContext.ACTION_TYPE_CHANGE_STATE);
			context.setData(EqpContext.DATA_EVENT_HIS, his);
			context.setData(EqpContext.DATA_LAST_STATE_ENTRY_TIME, lastStateEntryTime);
			
        	if (StringUtil.isEmpty(action.getEventId())) {
        		//没有EventID,说明是通过自动化触发
    			context = executeEqpCdiAction(context, IEqpCdiAction.CDI_POINT_AUTO_CHANGE_STATE, IEqpCdiAction.TRIGGER_POINT_POSTEXECUTE);
        		
        		//检查是否允许ManualChange
	          	RasState state = this.getState(sc.getOrgRrn(), equipment.getState());
	        	if (state != null) {
	        		if (state.getIsManualChange()) {
	        			//事件将会被禁止
	        			logger.info("Auto change equipment state is forbidden, Equipment[" + equipment.getEquipmentId() + "], target State[" + targetState + "].");
	        			return false;
	        		}
	        	}
        	}
			
    	    em.persist(his);
    	        
            //修改Equipment状态   
    	    //如果状态发生改变,则stateEntryTime记录状态发生改变的时间
            StringBuffer sql = new StringBuffer(" UPDATE Equipment ");
			sql.append(" SET ");
			sql.append(" comClass = :comClass, ");
			sql.append(" state = :state, ");
			if (!StringUtil.isEmpty(targetSubState)) {
				sql.append(" subState = :subState, ");
			}
			sql.append(" updated = :updated, ");
			sql.append(" updatedBy = :updatedBy, ");
			sql.append(" stateEntryTime = :stateEntryTime ");
			sql.append(" WHERE ");
			sql.append(" objectRrn = :objectRrn ");
			Query query = em.createQuery(sql.toString());
			query.setParameter("comClass", targeComClass);
			query.setParameter("state", targetState);
			if (!StringUtil.isEmpty(targetSubState)) {
				query.setParameter("subState", targetSubState);
			}
			query.setParameter("updated", sc.getTransTime());
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("stateEntryTime", equipment.getStateEntryTime());
			query.setParameter("objectRrn", equipment.getObjectRrn());
			query.executeUpdate();
			em.flush();
			em.clear();
			
			if (sysParamManager.getBooleanSysParameterValue(sc.getOrgRrn(), StateEvent.SYSPARAM_MES_STATE_TRIGGER_EVENT_ACTION)) {
				stateManager.triggerStateEventAction(Equipment.OBJECT_TYPE, 
						equipment.getObjectRrn(), equipment, his.getSourceState(), his.getTargetState(), sc);
			}
			
			if (equipment.getParentEqpRrn() != null) {
				logEventCascade(equipment.getParentEqpRrn(), targeComClass, targetState, targetSubState, action, sc);
			}
			
			context = executeEqpCdiAction(context, IEqpCdiAction.CDI_POINT_CHANGE_STATE, IEqpCdiAction.TRIGGER_POINT_POSTEXECUTE);
			
			//发送修改设备状态消息
			RASRTIUtil.alarmRti(rtiService, EqpAlarmRTI.TRANSACTION_TYPE_CHANGESTATE, equipment, his, sc);
			
			return true;
        } catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
        }    
    }
    
    /**
     * 修改对应设备当前Recipe
     * @param equipment
     * @param equipmentRecipe
     * @return
     * @throws ClientException
     */
    @Override
    public void changeEquipmentRecipe(Equipment equipment, String equipmentRecipe, SessionContext sc) throws ClientException{
		try {
			sc.buildTransInfo();
			
			equipment = em.find(Equipment.class, equipment.getObjectRrn());
			EquipmentCurrent current = getEquipmentCurrent(equipment.getObjectRrn(), sc);
			if (ObjectUtils.equals(current.getCurrentEquipmentRecipe(), equipmentRecipe)) {
				// 如果没有变化则不修改
				return;
			}
			
            EquipmentEventHis his = new EquipmentEventHis(equipment, current, sc);
			his.setEventId(EquipmentEventHis.TRANSTYPE_RECIPE);
			his.setTargetEquipmentRecipe(equipmentRecipe);
			his.setOperator(sc.getUserName());
	        em.persist(his);
	    	
			//修改EquipmentCurrent Recipe
			StringBuffer sql = new StringBuffer(" UPDATE EquipmentCurrent ");
			sql.append(" SET ");
			sql.append(" currentEquipmentRecipe = :currentEquipmentRecipe, ");
			sql.append(" updated = :updated, ");
			sql.append(" updatedBy = :updatedBy ");
			sql.append(" WHERE ");
			sql.append(" equipmentRrn = :equipmentRrn ");
			Query query = em.createQuery(sql.toString());
			query.setParameter("currentEquipmentRecipe", equipmentRecipe);
			query.setParameter("updated", new Date());
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("equipmentRrn", equipment.getObjectRrn());
			query.executeUpdate();
			
			current.setCurrentEquipmentRecipe(equipmentRecipe);
			equipment.setEquipmentCurrent(current);
			
			EqpContext context = new EqpContext();
			context.setSessionContext(sc);
			context.setEquipment(equipment);
			context.setActionType(EqpContext.ACTION_TYPE_CHANGE_RECIPE);
			context.setData(EqpContext.DATA_EVENT_HIS, his);
			
			context = executeEqpCdiAction(context, IEqpCdiAction.CDI_POINT_CHANGE_RECIPE, IEqpCdiAction.TRIGGER_POINT_POSTEXECUTE);
			
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
    }
    
    /**
     * 修改对应设备当前Part
     * @param equipment
     * @param partName
     * @param sc
     * @return
     * @throws ClientException
     */
    @Override
    public void changeEquipmentPart(Equipment equipment, String partName, SessionContext sc) throws ClientException{
    	
		try {
			sc.buildTransInfo();
			
			equipment = em.find(Equipment.class, equipment.getObjectRrn());
			EquipmentCurrent current = getEquipmentCurrent(equipment.getObjectRrn(), sc);
			if (ObjectUtils.equals(current.getCurrentPartName(), partName)) {
				//没有变化则不修改
				return;
			}
			
            //保存Event历史
            EquipmentEventHis his = new EquipmentEventHis(equipment, current, sc);
            his.setEventId(EquipmentEventHis.TRANSTYPE_PART);
            his.setTargetPartName(partName);
            his.setOperator(sc.getUserName());
            em.persist(his);
            
            //修改EquipmentCurrent当前产品        
			StringBuffer sql = new StringBuffer(" UPDATE EquipmentCurrent ");
			sql.append(" SET ");
			sql.append(" currentPartName = :currentPartName, ");
			sql.append(" updated = :updated, ");
			sql.append(" updatedBy = :updatedBy ");
			sql.append(" WHERE ");
			sql.append(" equipmentRrn = :equipmentRrn ");
			Query query = em.createQuery(sql.toString());
			query.setParameter("currentPartName", partName);
			query.setParameter("updated", new Date());
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("equipmentRrn", equipment.getObjectRrn());
			query.executeUpdate();

			current.setCurrentPartName(partName);
			equipment.setEquipmentCurrent(current);

			EqpContext context = new EqpContext();
			context.setSessionContext(sc);
			context.setEquipment(equipment);
			context.setActionType(EqpContext.ACTION_TYPE_CHANGE_PART);
			context.setData(EqpContext.DATA_EVENT_HIS, his);
			
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
    }
    
    /**
     * 修改对应设备当前工单
     * @param equipment
     * @param woId
     * @param sc
     * @return
     * @throws ClientException
     */
    @Override
    public void changeEquipmentWorkOrder(Equipment equipment, String woId, SessionContext sc) throws ClientException{
    	
		try {
			sc.buildTransInfo();
			
			equipment = em.find(Equipment.class, equipment.getObjectRrn());
			EquipmentCurrent current = getEquipmentCurrent(equipment.getObjectRrn(), sc);
			if (ObjectUtils.equals(current.getCurrentWoId(), woId)) {
				//没有变化则不修改
				return;
			}
			
            //保存Event历史
            EquipmentEventHis his = new EquipmentEventHis(equipment, current, sc);
            his.setEventId(EquipmentEventHis.TRANSTYPE_PART);
            his.setTargetWoId(woId);
            his.setOperator(sc.getUserName());
            em.persist(his);
            
            //修改EquipmentCurrent当前产品        
			StringBuffer sql = new StringBuffer(" UPDATE EquipmentCurrent ");
			sql.append(" SET ");
			sql.append(" currentWoId = :currentWoId, ");
			sql.append(" updated = :updated, ");
			sql.append(" updatedBy = :updatedBy ");
			sql.append(" WHERE ");
			sql.append(" equipmentRrn = :equipmentRrn ");
			Query query = em.createQuery(sql.toString());
			query.setParameter("currentWoId", woId);
			query.setParameter("updated", new Date());
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("equipmentRrn", equipment.getObjectRrn());
			query.executeUpdate();

			current.setCurrentWoId(woId);
			equipment.setEquipmentCurrent(current);

			EqpContext context = new EqpContext();
			context.setSessionContext(sc);
			context.setEquipment(equipment);
			context.setActionType(EqpContext.ACTION_TYPE_CHANGE_WORKORDER);
			context.setData(EqpContext.DATA_EVENT_HIS, his);
			
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
    }

    /**
     * 修改设备communicationState,并记录历史
     * @param equipment 设备
     * @param communicationState 通讯状态
     * @param comment 备注
     */
    @Override
    public void changeEquipmentCommunicationState(Equipment equipment,
            String communicationState, String comment, SessionContext sc) throws ClientException {
        try {
            sc.buildTransInfo();

            //保存Event历史
            EquipmentEventHis his = new EquipmentEventHis(equipment, null, sc);
            his.setEventId(EquipmentEventHis.TRANSTYPE_CONTROL_STATE);
            his.setTargetCommunicationState(communicationState);
            his.setOperator(sc.getUserName());
            his.setActionComment(comment);
            em.persist(his);
            
            //修改Equipment状态         
          //如果状态发生改变,则communicationStateEntryTime记录状态发生改变的时间
            StringBuffer sql = new StringBuffer(" UPDATE Equipment ");
			sql.append(" SET ");
			sql.append(" communicationState = :communicationState, ");		
			if (communicationState != null && !communicationState.equals(equipment.getCommunicationState())) {
				sql.append(" communicationStateEntryTime = :communicationStateEntryTime, ");
			}				
			sql.append(" updated = :updated, ");
			sql.append(" updatedBy = :updatedBy ");
			sql.append(" WHERE ");
			sql.append(" objectRrn = :objectRrn ");
			Query query = em.createQuery(sql.toString());
			query.setParameter("communicationState", communicationState);
			if (communicationState != null && !communicationState.equals(equipment.getCommunicationState())) {
				query.setParameter("communicationStateEntryTime", new Date());
			}
			query.setParameter("updated", new Date());
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("objectRrn", equipment.getObjectRrn());
			query.executeUpdate();	
        } catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
        }
    }
    
    /**
     * 修改设备processMode,并记录历史
     * @param equipment 设备ID
     * @param comment 备注
     */
    @Override
    public void changeEquipmentProcessMode(Equipment equipment,
            String processMode, String comment, SessionContext sc) throws ClientException {
        try {
            sc.buildTransInfo();

            //保存Event历史
            EquipmentCurrent current = getEquipmentCurrent(equipment.getObjectRrn(), sc);
            
            EquipmentEventHis his = new EquipmentEventHis(equipment, current, sc);
            his.setEventId(EquipmentEventHis.TRANSTYPE_PROCESS_MODE);
            his.setTargetProcessMode(processMode);
            his.setOperator(sc.getUserName());
            his.setActionComment(comment);
            em.persist(his);
            
            //修改EquipmentCurrent状态
            StringBuffer sql = new StringBuffer(" UPDATE EquipmentCurrent ");
			sql.append(" SET ");
			sql.append(" currentProcessMode = :currentProcessMode, ");
			sql.append(" updated = :updated, ");
			sql.append(" updatedBy = :updatedBy ");
			sql.append(" WHERE ");
			sql.append(" equipmentRrn = :equipmentRrn ");
			Query query = em.createQuery(sql.toString());
			query.setParameter("currentProcessMode", processMode);
			query.setParameter("updated", new Date());
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("equipmentRrn", equipment.getObjectRrn());
			query.executeUpdate();	
        } catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
        }
    }
    
    /**
     * 根据设备ID获取设备信息
     */
    public Equipment getEquipmentByEquipmentId(long orgRrn, String equipmentId) throws ClientException {
    	return getEquipmentByEquipmentId(orgRrn, equipmentId, true);
    }
    
    /**
     * 根据设备ID获取设备信息
     * @param equipmentId 设备id
     * @param exceptionFlag 是否抛出异常
     */
    @Override
	public Equipment getEquipmentByEquipmentId(long orgRrn, String equipmentId, boolean exceptionFlag) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" SELECT Equipment FROM Equipment Equipment ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND equipmentId = :equipmentId ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("equipmentId", equipmentId);
			List<Equipment> equipmentList = query.getResultList();
			if (equipmentList == null || equipmentList.size() == 0) {
				if (!exceptionFlag) {
					return null;
				}
				throw RasExceptionBundle.bundle.EquipmentNoFound();
			}
			Equipment equipment = equipmentList.get(0);
			return equipment;
		}  catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * 根据区域ID获取设备信息
	 * @param locationId 区域ID
	 */
	@Override
	public List<Equipment> getEquipmentsByLocationId(long orgRrn, String locationId, boolean exceptionFlag) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" SELECT Equipment FROM Equipment Equipment ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND location = :location ");

			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("location", locationId);
			List<Equipment> equipmentList = query.getResultList();
			if (equipmentList == null || equipmentList.size() == 0) {
				if (!exceptionFlag) {
					return null;
				}
				throw RasExceptionBundle.bundle.EquipmentNoFound();
			}
			return equipmentList;
		}  catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}


	/**
     * 根据区域ID获取设备信息
     * @param locationId 区域ID
     */
    @Override
	public List<Equipment> getEquipmentsByLocationId(long orgRrn, String locationId) throws ClientException {
		return getEquipmentsByLocationId(orgRrn, locationId, false);
	}
	/**
	 * 根据BAY ID获取设备信息
	 * @param subLocationId BAY ID
	 * @param exceptionFlag 是否抛出异常
	 */
	@Override
	public List<Equipment> getEquipmentsBySubLocationId(long orgRrn, String subLocationId, boolean exceptionFlag) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" SELECT Equipment FROM Equipment Equipment ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND subLocation = :subLocation ");

			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("subLocation", subLocationId);
			List<Equipment> equipmentList = query.getResultList();
			if (equipmentList == null || equipmentList.size() == 0) {
				if (!exceptionFlag) {
					return null;
				}
				throw RasExceptionBundle.bundle.EquipmentNoFound();
			}
			return equipmentList;
		}  catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
    /**
     * 根据BAY ID获取设备信息
     * @param subLocationId BAY ID
     */
    @Override
	public List<Equipment> getEquipmentsBySubLocationId(long orgRrn, String subLocationId) throws ClientException {
		return getEquipmentsBySubLocationId(orgRrn, subLocationId, false);
	}

	/**
	 * 根据Name获取设备能力信息
	 * @param name 设备能力名称
	 */
	public Capa getCapaByName(long orgRrn, String name) throws ClientException{
		return getCapaByName(orgRrn,name,true);
	}

	/**
	 * 根据Name获取设备能力信息
	 * @param orgRrn 厂区ID
	 * @param name  设备能力名称
	 * @param exceptionFlag 没有查询到数据时是否抛出异常
	 * @return Capa
	 * @throws ClientException
	 */
	public Capa getCapaByName(long orgRrn, String name, boolean exceptionFlag) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" SELECT Capa FROM Capa Capa ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND name = :name ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("name", name);
			List<Capa> capaList = query.getResultList();
			if (capaList == null || capaList.size() == 0) {
				if(!exceptionFlag){
					return null;
				}
				throw RasExceptionBundle.bundle.CapaNotFound();
			}
			Capa capa = capaList.get(0);
			return capa;
		}  catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据设备端口ID获取设备信息
	 * @param orgRrn
	 * @param portId
	 * @return
	 * @throws ClientException
	 */
	public Equipment getEquipmentByPortId(long orgRrn, String portId) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" SELECT Equipment FROM Equipment Equipment, Port Port ");
			sql.append(" WHERE Port.orgRrn = :orgRrn ");
			sql.append(" AND Port.orgRrn = Equipment.orgRrn ");
			sql.append(" AND Port.portId = :portId ");
			sql.append(" AND Port.parentEqpRrn = Equipment.objectRrn ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("portId", portId);
			List<Equipment> equipmentList = query.getResultList();
			if (equipmentList == null || equipmentList.size() == 0) {
				throw RasExceptionBundle.bundle.EquipmentNoFound();
			}
			Equipment equipment = equipmentList.get(0);
			return equipment;
		}  catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	* Hold设备
	* @param equipmentRrn 	设备ObjectRrn
	* @param actionComment 		备注
	* @param sc
	* @return
	*/ 
	public void holdEquipment(long equipmentRrn, String actionCode, String actionReason, String actionComment, SessionContext sc) throws ClientException {
	    Equipment equipment = em.find(Equipment.class, equipmentRrn);
	    
	    EquipmentHold equipmentHold = new EquipmentHold();
	    equipmentHold.setHoldCode(actionCode);
	    equipmentHold.setHoldReason(actionReason);
	    equipmentHold.setHoldComment(actionComment);
	    
        holdEquipment(equipment, equipmentHold, false, sc);
	}
	
	/**
	 * Hold设备
	 * @param equipment 	设备
	 * @param equipmentHold 		设备hold信息
	 * @param isThrowCheckHoldException 		相同holdCode是否抛出异常
	 * @param sc
	 * @return
	 */ 
	public void holdEquipment(Equipment equipment, EquipmentHold equipmentHold, boolean isThrowCheckHoldException, SessionContext sc) throws ClientException {
	    try {
	        sc.buildTransInfo();
	        
	        List<EquipmentHold> equipmentHolds = getEquipmentHolds(equipment.getObjectRrn());
	        
	        if (MesCfMod.isHoldCodeRepeateCheck(sc.getOrgRrn(), sysParamManager)) {
	        	if (equipmentHolds != null) {
	                for (EquipmentHold hold : equipmentHolds) {
	                    if (hold.getHoldCode().equals(equipmentHold.getHoldCode())) {
	                        //如果有相同的HoldCode,则不再重复Hold
	                        if (isThrowCheckHoldException) {
	                            throw RasExceptionBundle.bundle.EqpHoldCodeRepeat();
	                        }
	                        return;
	                    }
	                }
	        	}
            }
	        
	        if (CollectionUtils.isEmpty(equipmentHolds)) {
	            equipmentHold.setSeqNo(1L);
            } else {
                equipmentHold.setSeqNo(equipmentHolds.size() + 1L);
            }
	        equipmentHold.setObjectRrn(null);
	        equipmentHold.setCreatedBy(sc.getUserName());
	        equipmentHold.setUpdatedBy(sc.getUserName());
	        equipmentHold.setOrgRrn(sc.getOrgRrn());
	        equipmentHold.setIsActive(true);
	        equipmentHold.setEquipmentRrn(equipment.getObjectRrn());
	        equipmentHold.setHoldTime(new Date());
	        equipmentHold.setHoldUserName(sc.getUserName());
            em.persist(equipmentHold);
	        
	        //保存Event历史
            EquipmentHis his = new EquipmentHis(equipment, sc);
	        his.setTransType(EquipmentHis.TRANSTYPE_HOLD);
	        his.setHoldState(Equipment.HOLDSTATE_ON);
	        his.setActionCode(equipmentHold.getHoldCode());
	        his.setActionReason(equipmentHold.getHoldReason());
	        his.setActionComment(equipmentHold.getHoldComment());
	        
	        Map<String, String> map = new LinkedHashMap<String, String>();
			if (!StringUtil.isEmpty(equipmentHold.getHoldOwner())) {
				map.put("HOLDOWNER", equipmentHold.getHoldOwner());
			}
			map.put("EQPHOLD", String.valueOf(equipmentHold.getObjectRrn()));
			his.setHisComment(HistoryUtil.buildHisComment(map));
			
	        em.persist(his);
	        
	        StringBuffer sql = new StringBuffer(" UPDATE Equipment ");
	        sql.append(" SET ");
	        sql.append(" holdState = :holdState, ");
	        if (equipment.getHoldStateEntryTime() == null) {
	        	sql.append(" holdStateEntryTime = :holdStateEntryTime, ");
	        }
	        sql.append(" updated = :updated, ");
	        sql.append(" updatedBy = :updatedBy ");
	        sql.append(" WHERE ");
	        sql.append(" objectRrn = :objectRrn ");
	        Query query = em.createQuery(sql.toString());
	        query.setParameter("holdState", Equipment.HOLDSTATE_ON);
	        if (equipment.getHoldStateEntryTime() == null) {
	        	query.setParameter("holdStateEntryTime", new Date());
	        }
	        query.setParameter("updated", new Date());
	        query.setParameter("updatedBy", sc.getUserName());
	        query.setParameter("objectRrn", equipment.getObjectRrn());
	        query.executeUpdate();	
	        
	        EqpContext context = new EqpContext();
			context.setSessionContext(sc);
			context.setEquipment(equipment);
			context = executeEqpCdiAction(context, IEqpCdiAction.CDI_POINT_HOLD, IEqpCdiAction.TRIGGER_POINT_POSTEXECUTE);
			
	    } catch (Exception e) {
	        throw ExceptionManager.handleException(logger, e);
	    }
	}
	
	/**
	* release Hold的设备
	* @param sc
	* @return
	*/ 
	public void releaseEquipment(Equipment equipment, List<EquipmentHold> equipmentHolds, EquipmentAction equipmentAction, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			List<Long> holdRrns = Lists.newArrayList();
			//检查是否允许Release
			for (EquipmentHold equipmentHold : equipmentHolds) {
			    //根据Hold Password判断是否可以Release该批次
                if (!StringUtil.isEmpty(equipmentHold.getHoldPwd())) {
                    if (!equipmentHold.getHoldPwd().equals(equipmentAction.getActionPwd())) {
                        throw RasExceptionBundle.bundle.EqpReleasePwdError();
                    }
                }
                
                //根据HoldOwner判断是否可以Release该批次
                if (!StringUtil.isEmpty(equipmentHold.getHoldOwner())) {
                    ADUser user = securityManager.getUserByUserName(sc.getUserName());
                    if (!securityManager.isInOwner(sc.getOrgRrn(), user.getObjectRrn(), equipmentHold.getHoldOwner())) {
                        throw RasExceptionBundle.bundle.EqpReleaseOwnerNotAllow();
                    }
                }
                
                em.remove(em.find(EquipmentHold.class, equipmentHold.getObjectRrn()));
                
                holdRrns.add(equipmentHold.getObjectRrn());
            }
			
			equipmentHolds = getEquipmentHolds(equipment.getObjectRrn());
			
			//保存Event历史
            EquipmentHis his = new EquipmentHis(equipment, sc);
            his.setTransType(EquipmentHis.TRANSTYPE_RELEASE);
            his.setHoldState(Equipment.HOLDSTATE_OFF);
            his.setActionCode(equipmentAction.getActionCode());
			his.setActionReason(equipmentAction.getActionReason());
			his.setActionComment(equipmentAction.getActionComment());
			his.setHisComment(HistoryUtil.buildHisComment("EQPHOLD", StringUtils.join(holdRrns, ";")));
            em.persist(his);
            
            if (CollectionUtils.isEmpty(equipmentHolds)) {
                StringBuffer sql = new StringBuffer(" UPDATE Equipment ");
                sql.append(" SET ");
                sql.append(" holdState = :holdState, ");
                sql.append(" holdStateEntryTime = null, ");
                sql.append(" updated = :updated, ");
                sql.append(" updatedBy = :updatedBy ");
                sql.append(" WHERE ");
                sql.append(" objectRrn = :objectRrn ");
                Query query = em.createQuery(sql.toString());
                query.setParameter("holdState", Equipment.HOLDSTATE_OFF);
                query.setParameter("updated", new Date());
                query.setParameter("updatedBy", sc.getUserName());
                query.setParameter("objectRrn", equipment.getObjectRrn());
                query.executeUpdate();	
            } 
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
     * 获得批次Hold列表
     * @param equipmentRrn 设备rrn
     */
    public List<EquipmentHold> getEquipmentHolds(long equipmentRrn) throws ClientException {
        return getEquipmentHolds(equipmentRrn, false);
    }

	/**
	 * 获得批次Hold列表
	 * @param equipmentRrn 设备rrn
	 * @param exceptionFlag 是否抛出异常
	 */
	public List<EquipmentHold> getEquipmentHolds(long equipmentRrn, boolean exceptionFlag) throws ClientException {
		StringBuffer sql = new StringBuffer(" SELECT EquipmentHold FROM EquipmentHold EquipmentHold ");
		sql.append(" WHERE equipmentRrn = :equipmentRrn ORDER BY seqNo DESC ");
		try {
			Query query = em.createQuery(sql.toString());
			query.setParameter("equipmentRrn", equipmentRrn);
			List<EquipmentHold> eqpHolds = query.getResultList();
			if (eqpHolds == null || eqpHolds.size() == 0) {
				if (!exceptionFlag) {
					return null;
				}
				throw RasExceptionBundle.bundle.EquipmentHoldNoFound();
			}
			return eqpHolds;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
    /**
     * 通过线别RRN查询该线别上的设备信息
     * @param lineRrn 线别rrn
     */
	public List<Equipment> getLineEquipments(long lineRrn) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("SELECT Equipment FROM Equipment Equipment, ");
			sql.append(" EquipmentLine EquipmentLine ");
			sql.append(" WHERE EquipmentLine.equipmentRrn = Equipment.objectRrn ");
			sql.append(" AND EquipmentLine.lineRrn = :lineRrn ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("lineRrn", lineRrn);
			return (List<Equipment>)query.getResultList();
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	 /**
     * 查询在多个线别中的所有设备信息
     * @param lineIds 线别
     */
	public List<Equipment> getLineEquipments(long orgRrn, List<String> lineIds, String location) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("SELECT Equipment FROM Equipment Equipment ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			if (!StringUtil.isEmpty(location)) {
				sql.append(" AND Equipment.location = :location ");
			}
			sql.append(" AND objectRrn IN ( ");
			sql.append(" SELECT equipmentRrn FROM EquipmentLine EquipmentLine ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND lineId IN (:lineIds) ");
			sql.append(" ) ");
			sql.append(" ORDER BY equipmentId ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("lineIds", lineIds);
			if (!StringUtil.isEmpty(location)) {
				query.setParameter("location", location);
			}
			return (List<Equipment>)query.getResultList();
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 通过设备ID查询设备线别信息
	 * @param equipmentId 设备ID
	 */
	public List<EquipmentLine> getLineEquipmentsByEquipmentId(long orgRrn, String equipmentId) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("SELECT EquipmentLine FROM EquipmentLine EquipmentLine ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND equipmentId = :equipmentId ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("equipmentId", equipmentId);
			return (List<EquipmentLine>)query.getResultList();
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * @param lineRrn
	 * @param lineEquipments
	 * @param sc
	 * 更新line相关的EquipmentLine信息
	 * 
	 * @result
	 */
	public void saveLineEquipments(long lineRrn, List<EquipmentLine> lineEquipments, SessionContext sc)
			throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" DELETE FROM EquipmentLine EquipmentLine ");
			sql.append(" WHERE ");
			sql.append(" lineRrn = :lineRrn ");
			Query query = em.createQuery(sql.toString());
			query.setParameter("lineRrn", lineRrn);
			query.executeUpdate();
			
			for (EquipmentLine lineEquipment : lineEquipments) {
				em.persist(lineEquipment);
			}
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 获得当前区域的设备状态颜色定义
	 * @param orgRrn
	 */
	public List<EquipmentStateColor> getEqpStateColors(long orgRrn) throws ClientException{
		try{
			StringBuffer hql = new StringBuffer();
			hql.append("SELECT s FROM EquipmentStateColor s  WHERE ");
			hql.append(ADBase.BASE_CONDITION_N);
			hql.append(" ORDER BY s.objectRrn" );
			Query query = em.createQuery(hql.toString());
			query.setParameter("orgRrn", orgRrn);
			List<EquipmentStateColor> resultList = query.getResultList();
			return resultList;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	} 
	
	/**
	 * 保存设备工艺限制
	 * @see com.glory.mes.ras.client.RASManager#saveSpecSet(com.glory.mes.ras.constraint.RASConstraint, com.glory.framework.core.util.SessionContext)
	 */
	public RASConstraint saveRasConstraint(RASConstraint rasConstraint, SessionContext sc) throws ClientException {
		try {
			if (rasConstraint.getObjectRrn() == null) {
				rasConstraint.setOrgRrn(sc.getOrgRrn());
				rasConstraint.setIsActive(true);
				rasConstraint.setCreatedBy(sc.getUserName());
				rasConstraint.setCreated(new Date());
				rasConstraint.setStatus(RASConstraint.STATUS_UNFROZNE);
				if(rasConstraint.getVersion()==null){
					RASConstraint lastRasConstraint = basManager.getLastVersionControl(rasConstraint.getOrgRrn(), rasConstraint.getClass(), rasConstraint.getName());
					if (lastRasConstraint != null) {
						rasConstraint.setVersion(lastRasConstraint.getVersion() + 1);
					} else {
						rasConstraint.setVersion(1L);
					}
				}
				em.persist(rasConstraint);
			}
			rasConstraint.setUpdatedBy(sc.getUserName());
			em.merge(rasConstraint);
			return rasConstraint;
		} catch (Exception e){ 
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 保存工艺限制设置
	* @param eqpConstraint 设备工艺限制
	* @return
	*/ 
	public void saveEqpConstraint(EqpConstraint eqpConstraint, SessionContext sc) throws ClientException {
		try {
			eqpConstraint.setOrgRrn(sc.getOrgRrn());
			eqpConstraint.setIsActive(true);
			eqpConstraint.setCreated(new Date());
			eqpConstraint.setCreatedBy(sc.getUserName());
			eqpConstraint.setUpdated(new Date());
			eqpConstraint.setUpdatedBy(sc.getUserName());
			eqpConstraint.setUpdatedUser(sc.getUserName());
			
			RASConstraint rasConstraint = new RASConstraint();
			if (eqpConstraint.getConstraintRrn() != null) {
				//通过工艺限制的ObjectRrn找到对应的工艺限制
				rasConstraint.setObjectRrn(eqpConstraint.getConstraintRrn());
				rasConstraint = (RASConstraint) adManager.getEntity(rasConstraint);
				eqpConstraint.setConstraintRrn(rasConstraint.getObjectRrn());
				eqpConstraint.setConstraintName(rasConstraint.getName());
				eqpConstraint.setConstraintVersion(rasConstraint.getVersion());
			} 
			em.merge(eqpConstraint);
			EqpConstraintHis eqpConstraintHis = new EqpConstraintHis(eqpConstraint);
			eqpConstraintHis.setTransType("CONSTRAINT");
			em.merge(eqpConstraintHis);
		} catch (Exception e){ 
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	* 工艺限制细项查询
	* @param equipmentRrn 设备RRN
	 * @return 
	* @return
	*/ 
	public List<RASConstraintCondition> getConstraintCondition(Long equipmentRrn, SessionContext sc) throws ClientException {
		try {
			List<RASConstraintCondition> conditions = new ArrayList<RASConstraintCondition>();
			//根据设备ID找到设备对象
			Equipment equipment = em.find(Equipment.class, equipmentRrn);
			
			if(equipment != null){
				//根据该设备类型和设备名称，找到工艺限制设置
				String whereCause = " (objectType = 'EQPTYPE' AND objectId = '" + equipment.getEqpType() + "') " +
						"OR (objectType = 'EQP' AND objectId = '" + equipment.getEquipmentId() + "')";
				List<EqpConstraint> eqpConstraints = adManager.getEntityList(sc.getOrgRrn(), EqpConstraint.class, Integer.MAX_VALUE, whereCause, "");
				//根据工艺限制设置里的工艺限制定义找到工艺限制的内容行
				for (EqpConstraint eqpConstraint : eqpConstraints) {
					List<RASConstraintCondition> constraintConditions = adManager.getEntityList(sc.getOrgRrn(),
							RASConstraintCondition.class, Integer.MAX_VALUE, " constraintRrn = " + eqpConstraint.getConstraintRrn(), "");				
					for (RASConstraintCondition condition : constraintConditions) {
						condition.setConstraintName(eqpConstraint.getConstraintName());
						condition.setConstraintVersion(eqpConstraint.getConstraintVersion());
						condition.setIsEnable(eqpConstraint.getIsEnable());
						condition.setEffectDateFrom(eqpConstraint.getEffectDateFrom());
						condition.setEffectDateTo(eqpConstraint.getEffectDateTo());
					}
					conditions.addAll(constraintConditions);
				}
			}
			return conditions;			
		} catch (Exception e){ 
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	public Port getPortById(long orgRrn, String equipmentId, String portId,String portNum) throws ClientException{
		return getPortById(orgRrn,equipmentId,portId,portNum,true);
	}
	
	/**
	 * 通过设备名称，port名称，端口号查询port信息
	 * @param equipmentId 设备名称
	 * @param portId port名称
	 * @param portNum 端口号
	 */
	@Override
	public Port getPortById(long orgRrn, String equipmentId, String portId,String portNum,boolean exceptionFlag) throws ClientException {
		if(StringUtils.isNotBlank(portId)) {
			return getPortById(orgRrn, equipmentId, portId,exceptionFlag);
		}else if(StringUtils.isNotBlank(portNum)) {
			return getPortByPortNum(orgRrn, equipmentId, portNum,exceptionFlag);
		}else if(!exceptionFlag) {
			return null;
		}
		throw RasExceptionBundle.bundle.PortIdPortNumIsAllNull();
	}
	
	public Port getPortById(long orgRrn, String equipmentId, String portId) throws ClientException {
		return getPortById(orgRrn, equipmentId, portId, true);
	}
	
	/**
	 * 通过设备ID和portId获取port信息
	 * @param equipmentId 设备ID
	 * @param portId 设备端口ID
	 * @param exceptionFlag 没有查询到数据时是否抛出异常
	 */
	@Override
	public Port getPortById(long orgRrn, String equipmentId, String portId, boolean exceptionFlag) throws ClientException {
		try {
			Equipment equipment = getEquipmentByEquipmentId(orgRrn, equipmentId, exceptionFlag);
			if (equipment == null) {
				if(!exceptionFlag) {
					return null;
				}
				throw RasExceptionBundle.bundle.EquipmentNoFound();
			}
			StringBuffer sql = new StringBuffer(" SELECT Port FROM Port Port ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND Port.portId = :portId ");
			sql.append(" AND Port.parentEqpRrn = :parentEqpRrn ");

			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("portId", portId);
			query.setParameter("parentEqpRrn", equipment.getObjectRrn());
			List<Port> ports = query.getResultList();

			if (ports == null || ports.size() == 0) {
				if(!exceptionFlag) {
					return null;
				}
				throw RasExceptionBundle.bundle.EquipmentPortNotFound();
			}
			return  ports.get(0);
		}  catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	public Port getPortByPortNum(long orgRrn, String equipmentId, String portNum) throws ClientException{
		return getPortByPortNum(orgRrn, equipmentId, portNum, true);
	}
	
	/**
	 * 通过设备ID和portNum获取port信息
	 * @param equipmentId 设备名称
	 * @param portNum 端口号
	 * @param exceptionFlag 没有查询到数据时是否抛出异常
	 */

	@Override
	public Port getPortByPortNum(long orgRrn, String equipmentId, String portNum, boolean exceptionFlag) throws ClientException {
		try {
			Equipment equipment = getEquipmentByEquipmentId(orgRrn, equipmentId, exceptionFlag);
			if (equipment == null) {
				if(!exceptionFlag) {
					return null;
				}
				throw RasExceptionBundle.bundle.EquipmentNoFound();
			}
			StringBuffer sql = new StringBuffer(" SELECT Port FROM Port Port ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND Port.portNum = :portNum ");
			sql.append(" AND Port.parentEqpRrn = :parentEqpRrn ");

			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("portNum", portNum);
			query.setParameter("parentEqpRrn", equipment.getObjectRrn());
			List<Port> ports = query.getResultList();

			if (ports == null || ports.size() == 0) {
				if(!exceptionFlag) {
					return null;
				}
				throw RasExceptionBundle.bundle.EquipmentPortNotFound();
			}
			return  ports.get(0);
		}  catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 获得设备的所有Port信息
	 * @param orgRrn
	 * @param equipmentId
	 * 
	 * @return List<Port>
	 */
	@Override
	public List<Port> getPortsByEquipment(long orgRrn, String equipmentId) throws ClientException {
		return getPortsByEquipment(orgRrn, equipmentId, true);
	}
	
	/**
	 * 获得设备的所有Port信息
	 * @param orgRrn
	 * @param equipmentId
	 * 
	 * @return List<Port>
	 */
	@Override
	public List<Port> getPortsByEquipment(long orgRrn, String equipmentId, boolean exceptionFlag) throws ClientException {
		try {
			Equipment equipment = getEquipmentByEquipmentId(orgRrn, equipmentId);
			if (equipment == null) {
				throw RasExceptionBundle.bundle.EquipmentNoFound();
			}
			StringBuffer sql = new StringBuffer(" SELECT Port FROM Port Port ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND Port.parentEqpRrn = :parentEqpRrn ");
			sql.append(" ORDER BY portId ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("parentEqpRrn", equipment.getObjectRrn());
			List<Port> ports = query.getResultList();

			if (exceptionFlag && CollectionUtils.isEmpty(ports)) {
				throw RasExceptionBundle.bundle.EquipmentPortNotFound();
			}
			return  ports;
		}  catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	
	/**
	 * 获得Sorting设备可用的Port
	 * @param orgRrn
	 * @param equipmentId
	 */
	public List<Port> getAvailablePorts(long orgRrn, String equipmentId) throws ClientException {
		try {
			List<Port> ports = getPortsByEquipment(orgRrn, equipmentId, false);
			List<Port> availPorts = ports.stream().filter(p -> p.isAvailable() && PortTransferState.STATE_READYTOLOAD.equals(p.getTransferState())).collect(Collectors.toList());
			return availPorts;
		}  catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 保存port
	 * @param port 设备端口
	 */
	@Override
	public Port savePort(Port port, SessionContext sc) throws ClientException {
		try {
			boolean editFlag = true;
			if (port.getObjectRrn() == null) {
				port.setIsActive(true);
				port.setCreated(new Date());
				port.setCreatedBy(sc.getUserName());
				editFlag = false;
			}
			if (port.getParentEqpRrn() == null) {
				throw RasExceptionBundle.bundle.EquipmentNoFound();
			}
			Equipment equipment = em.find(Equipment.class, port.getParentEqpRrn());
			if (equipment == null) {
				throw RasExceptionBundle.bundle.EquipmentNoFound();
			}
			
			
			port.setParentEqpId(equipment.getEquipmentId());
			port.setUpdated(new Date());
			port.setUpdatedBy(sc.getUserName());
			
			port = em.merge(port);
			
			//保证portNum不能重复
			List<Port> ports = getPortsByEquipment(sc.getOrgRrn(), equipment.getEquipmentId());
			List<String> portNumList = Lists.newArrayList();
			for (Port epqPort : ports) {
				if (StringUtils.isNotBlank(epqPort.getPortNum())) {
					if (portNumList.contains(epqPort.getPortNum()) && epqPort.getPortNum().equals(port.getPortNum())){
						throw RasExceptionBundle.bundle.PortNumIsRepeat();
					}
					portNumList.add(epqPort.getPortNum());
				}
			}
			PortHis portHis = new PortHis(port);
			if (editFlag) {
				portHis.setTransType(HistoryUtil.TRANSTYPE_UPDATE);
			} else {
				portHis.setTransType(HistoryUtil.TRANSTYPE_CREATE);
			}
			em.persist(portHis);
		
			return port;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 删除port
	 * @param port
	 */
	@Override
	public void deletePort(Port port, SessionContext sc) throws ClientException {
		try {
			PortHis portHis = new PortHis(port);
			portHis.setTransType(HistoryUtil.TRANSTYPE_DELETE);
			portHis.setUpdatedBy(sc.getUserName());
			portHis.setUpdated(new Date());
			em.persist(portHis);
			
			em.remove(em.merge(port));
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}		
	}
	
	/**
	 * 修改设备端口的搬送状态
	 * @param port
	 * @param transportState 搬送端口状态
	 * @param comment 备注
	 */
	public void changePortTransferState(Port port, String transportState, String comment, SessionContext sc)
			throws ClientException {
		changePortTransferState(port, transportState, null, comment, sc);
	}
	
	/**
	 * 修改设备端口的搬送状态
	 * @param port 
	 * @param transportState 搬送端口状态
	 * @param portUseType 端口正在使用状态
	 * @param comment 备注
	 */
	@Override
	public void changePortTransferState(Port port, String transportState, String portUseType, String comment, SessionContext sc)
			throws ClientException {
		changePortTransferState(port, transportState, portUseType, null, comment, sc);
	}

	/**
	 * 修改设备端口的搬送状态，指定可以切换的原搬送状态
	 * @param port
	 * @param transportState 搬送端口状态
	 * @param portUseType 端口正在使用状态
	 * @param originTransferStates 原搬送端口状态
	 * @param comment 备注
	 */
	@Override
	public void changePortTransferState(Port port, String transportState, String portUseType, List<String> originTransferStates, String comment, SessionContext sc)
			throws ClientException {
		try {
			sc.buildTransInfo();

			//如果搬送状态发生改变,则transferStateEntryTime记录状态发生改变的时间
			StringBuffer sql = new StringBuffer(" UPDATE Port ");
			sql.append(" SET ");
			sql.append(" transferState = :transferState, ");
			if (!StringUtil.isEmpty(portUseType)) {
				sql.append(" portUseType = :portUseType, ");
			}
			if (transportState != null && !transportState.equals(port.getTransferState())) {
				sql.append(" transferStateEntryTime = :transferStateEntryTime, ");
			}
			sql.append(" updated = :updated, ");
			sql.append(" updatedBy = :updatedBy, ");
			sql.append(" lockVersion = :newLockVersion ");
			sql.append(" WHERE ");
			sql.append(" objectRrn = :objectRrn ");
			if (CollectionUtils.isNotEmpty(originTransferStates)) {
				sql.append(" and transferState in (:originTransferStates)");
			}
			Query query = em.createQuery(sql.toString());
			query.setParameter("transferState", transportState);
			if (!StringUtil.isEmpty(portUseType)) {
				query.setParameter("portUseType", portUseType);
			}
			if (transportState != null && !transportState.equals(port.getTransferState())) {
				query.setParameter("transferStateEntryTime", new Date());
			}
			if (CollectionUtils.isNotEmpty(originTransferStates)) {
				query.setParameter("originTransferStates", originTransferStates);
			}
			query.setParameter("updated", new Date());
			query.setParameter("newLockVersion", port.getLockVersion()+1);
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("objectRrn", port.getObjectRrn());
			int count = query.executeUpdate();

			//修改Port状态并记录历史
			PortEventHis his = new PortEventHis(port, sc);
			his.setEventId(PortEventHis.TRANSTYPE_TRANSFER_STATE);
			his.setTargetComClass(port.getComClass());
			his.setTargetState(port.getState());
			his.setTargetSubState(port.getSubState());
			his.setOperator(sc.getUserName());
			if (count == 0) {
				his.setTransferState(port.getTransferState());
				his.setActionComment((comment == null ? "" : comment) + (originTransferStates != null ? originTransferStates.toString() : ""));
			} else {
				his.setTransferState(transportState);
				his.setActionComment(comment);
			}
			em.persist(his);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * 新起事务修改设备端口的搬送状态
	 * @param port 
	 * @param transportState 搬送端口状态
	 * @param portUseType 端口正在使用状态
	 * @param comment 备注
	 */
	@Override
	@TransactionAttribute(value = TransactionAttributeType.REQUIRES_NEW)
	public void changePortTransferStateNewTrans(Port port, String transportState, String portUseType, String comment, SessionContext sc) throws ClientException {
		changePortTransferState(port, transportState, portUseType, comment, sc);
	}

	/**
	 * 新起事务修改设备端口的搬送状态
	 * @param port
	 * @param eventId 事件ID
	 * @param comment 备注
	 */
	public Port changePortTransferStateByEvent(Port port, 
			String eventId, String comment, SessionContext sc) throws ClientException {
		try {				
			sc.buildTransInfo();
			
			//修改Port状态并记录历史			
			PortEventHis his = new PortEventHis(port, sc);
			his.setEventId(eventId);
			
			PortTransferState state = new PortTransferState();
			state.initialize(port.getTransferState());
			state.send(eventId);
			
			StringBuffer sql = new StringBuffer(" UPDATE Port ");
			sql.append(" SET ");
			sql.append(" transferState = :transferState, ");
			if (state.getCurrentState().getStateId() != null && !state.getCurrentState().getStateId().equals(port.getTransferState())) {
				sql.append(" transferStateEntryTime = :transferStateEntryTime, ");
			}
			sql.append(" updated = :updated, ");
			sql.append(" updatedBy = :updatedBy ");
			sql.append(" WHERE ");
			sql.append(" objectRrn = :objectRrn ");
			Query query = em.createQuery(sql.toString());
			query.setParameter("transferState", state.getCurrentState().getStateId());
			if (state.getCurrentState().getStateId() != null && !state.getCurrentState().getStateId().equals(port.getTransferState())) {
				query.setParameter("transferStateEntryTime", new Date());
			}
			query.setParameter("updated", new Date());
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("objectRrn", port.getObjectRrn());
			query.executeUpdate();
			
			his.setTransferState(port.getTransferState());
			his.setTargetComClass(port.getComClass());
			his.setTargetState(port.getState());
			his.setTargetSubState(port.getSubState());
			his.setOperator(sc.getUserName());
			his.setActionComment(comment);
			em.persist(his);
			return port;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 修改Port Type
	 * @param port
	 * @param targetType 目标type
	 */
	public void changePortType(Port port, String targetType, SessionContext sc) throws ClientException {	
		try {
			sc.buildTransInfo();
			
			StringBuffer sql = new StringBuffer(" UPDATE Port ");
			sql.append(" SET ");
			sql.append(" portType = :portType, ");
			sql.append(" updated = :updated, ");
			sql.append(" updatedBy = :updatedBy ");
			sql.append(" WHERE ");
			sql.append(" objectRrn = :objectRrn ");
			Query query = em.createQuery(sql.toString());
			query.setParameter("portType", targetType);
			query.setParameter("updated", new Date());
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("objectRrn", port.getObjectRrn());
			query.executeUpdate();
			
			PortHis his = new PortHis(port);
			his.setUpdated(new Date());
			his.setUpdatedBy(sc.getUserName());
			his.setTransType(PortHis.TRANSTYPE_CHANGETYPE);
			his.setPortType(targetType);
			em.persist(his);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 修改Port UseType
	 * @param port
	 * @param targetUseType 端口正在使用状态
	 */
	public void changePortUseType(Port port, String targetUseType, SessionContext sc) throws ClientException {	
		try {
			sc.buildTransInfo();
			
			StringBuffer sql = new StringBuffer(" UPDATE Port ");
			sql.append(" SET ");
			sql.append(" portUseType = :portUseType, ");
			sql.append(" updated = :updated, ");
			sql.append(" updatedBy = :updatedBy ");
			sql.append(" WHERE ");
			sql.append(" objectRrn = :objectRrn ");
			Query query = em.createQuery(sql.toString());
			query.setParameter("portUseType", targetUseType);
			query.setParameter("updated", new Date());
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("objectRrn", port.getObjectRrn());
			query.executeUpdate();
			
			PortHis his = new PortHis(port);
			his.setUpdated(new Date());
			his.setUpdatedBy(sc.getUserName());
			his.setTransType(PortHis.TRANSTYPE_CHANGEUSETYPE);
			his.setPortUseType(targetUseType);
			em.persist(his);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * 修改Port AccessMode
	 * @param port
	 * @param targetAccessMode
	 */
	public void changePortAccessMode(Port port, String targetAccessMode, SessionContext sc) throws ClientException {	
		try {
			sc.buildTransInfo();
			
			StringBuffer sql = new StringBuffer(" UPDATE Port ");
			sql.append(" SET ");
			sql.append(" accessState = :accessState, ");
			sql.append(" updated = :updated, ");
			sql.append(" updatedBy = :updatedBy ");
			sql.append(" WHERE ");
			sql.append(" objectRrn = :objectRrn ");
			Query query = em.createQuery(sql.toString());
			query.setParameter("accessState", targetAccessMode);
			query.setParameter("updated", new Date());
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("objectRrn", port.getObjectRrn());
			query.executeUpdate();
			
			PortHis his = new PortHis(port);
			his.setTransType(PortHis.TRANSTYPE_CHANGEACCESSMODE);
			his.setUpdatedBy(sc.getUserName());
			his.setAccessState(targetAccessMode);
			em.persist(his);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 修改Port状态并记录Port历史
	 * @param port
	 * @param targetComClass 目标状态大类
	 * @param targetState 目标状态
	 * @param targetSubState 目标子状态
	 * @param comment 备注
	 */
	@Override
	public void changePortState(Port port, String targetComClass, String targetState, String targetSubState, String comment, SessionContext sc)
			throws ClientException {	
		try {
			sc.buildTransInfo();
			
			PortEventHis his = new PortEventHis(port, sc);
			his.setEventId(PortEventHis.TRANSTYPE_STATE);
			his.setTargetComClass(targetComClass);
			his.setTargetState(targetState);
			his.setTargetSubState(targetSubState);
			his.setOperator(sc.getUserName());
			his.setActionComment(comment);
			em.persist(his);	
			
			StringBuffer sql = new StringBuffer(" UPDATE Port ");
			sql.append(" SET ");
			sql.append(" comClass = :comClass, ");
			sql.append(" state = :state, ");
			if (targetSubState != null) {
				sql.append(" subState = :subState, ");
			}
			sql.append(" updated = :updated, ");
			sql.append(" updatedBy = :updatedBy ");
			sql.append(" WHERE ");
			sql.append(" objectRrn = :objectRrn ");
			Query query = em.createQuery(sql.toString());
			query.setParameter("comClass", targetComClass);
			query.setParameter("state", targetState);
			if (targetSubState != null) {
				query.setParameter("subState", targetSubState);
			}
			query.setParameter("updated", new Date());
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("objectRrn", port.getObjectRrn());
			query.executeUpdate();
			
			//发送修改prot状态消息
			RASRTIUtil.alarmRti(rtiService, PortAlarmRTI.TRANSACTION_TYPE_CHANGESTATE, port, his, sc);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 通过事件修改port状态并记录历史
	 * @param portRrn port主键
	 * @param eventId 事件ID
	 * @param comment 备注
	 * @param checkAuthorityFlag 是否检查设备权限
	 */
	public Port changePortStateByEvent(long portRrn, String eventId,
			String comment, boolean checkAuthorityFlag, SessionContext sc) throws ClientException {	
		try {
			sc.buildTransInfo();
			
			String whereClause = " eventId = '" + eventId + "'";
			List<RasEvent> eqpEvents = adManager.getEntityList(sc.getOrgRrn(), RasEvent.class, 1, whereClause, "");
			if (eqpEvents.size() == 0) {
				throw RasExceptionBundle.bundle.LogEventNoEventFound();
			}
			RasEvent event = eqpEvents.get(0);
			event = em.find(RasEvent.class, event.getObjectRrn());
			
			Port port = em.find(Port.class, portRrn);			
			if (port.getStatusModelRrn() == null) {
				throw RasExceptionBundle.bundle.LogEventNoStatusModelFound();
			}
			//检查设备状态模型
			RasStatusModel statusModel = em.find(RasStatusModel.class, port.getStatusModelRrn());
			StatusModelEvent modelEvent = null;
			for (StatusModelEvent cModelEvent : statusModel.getModelEvents()) {
				if (cModelEvent.getEventRrn().equals(event.getObjectRrn())) {
					modelEvent = cModelEvent;
					break;
				}
			}
			if (modelEvent == null) {
				throw RasExceptionBundle.bundle.LogEventNoStatusModelEventFound();
			}
			
			//检查设备权限
			if (checkAuthorityFlag) {
				if (modelEvent.getUserGroups() != null && modelEvent.getUserGroups().size() > 0) {
//					ADUser user = securityManager.doLogin(sc.getOrgRrn(), sc.getUserName());
					ADUser user = securityManager.getUserByUserName(sc.getOrgRrn(), sc.getUserName());
					boolean authorityFlag = false;
					for (ADUserGroup group : user.getUserGroups()) {
						for (ADUserGroup modelGroup : modelEvent.getUserGroups()) {
							if (group.getObjectRrn().equals(modelGroup.getObjectRrn())) {
								authorityFlag = true;
								break;
							}
						}
					}
					if (!authorityFlag) {
						throw RasExceptionBundle.bundle.LogEventUserCannotLogThisEvent();
					}
				}	
			}
			
			EventStatus allowStatus = getCurrentEventStatus(port, event);
			//如果未找到允许状态,则抛出异常或直接返回
			if (allowStatus == null) {
				throw RasExceptionBundle.bundle.LogEventStatusNotAllow();
			}
			
			//修改Port状态并记录历史			
			changePortState(port, allowStatus.getTargetComClass(), allowStatus.getTargetState(), allowStatus.getTargetSubState(), comment, sc);
	
			return port;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 根据Port当前状态和设备事件,找到第一个满足条件的可转换Port状态
	* @param port 		当前Port
	* @param event 		Port事件
	* @return
	*/ 
	public EventStatus getCurrentEventStatus(Port port, RasEvent event) throws ClientException {
		try {
			//获得事件的状态列表,并根据CHECKFLAG进行分类
			List<EventStatus> eventStatus = event.getEventStatus();
			List<EventStatus> rejectEventStatus = new ArrayList<EventStatus>();
			List<EventStatus> allowEventStatus = new ArrayList<EventStatus>();
			for (EventStatus status : eventStatus) {
				if (EventStatus.CHECKFLAG_REJECT.equalsIgnoreCase(status.getCheckFlag())) {
					rejectEventStatus.add(status);
				} else {
					allowEventStatus.add(status);
				}
			}
			//先检查拒绝状态,看当前设备的状态是否被拒绝,如果被拒绝抛出异常
			for (EventStatus rejectStatus : rejectEventStatus) {
				if (EventStatus.ALL_FLAG.equalsIgnoreCase(rejectStatus.getSourceComClass())) {
					throw RasExceptionBundle.bundle.LogEventStatusNotAllow();
				}
				if (rejectStatus.getSourceComClass().equalsIgnoreCase(port.getComClass())
					&& EventStatus.ALL_FLAG.equalsIgnoreCase(rejectStatus.getSourceState())) {
					throw RasExceptionBundle.bundle.LogEventStatusNotAllow();
				}
				if (rejectStatus.getSourceComClass().equalsIgnoreCase(port.getComClass())
					&& rejectStatus.getSourceState().equalsIgnoreCase(port.getState())
					&& EventStatus.ALL_FLAG.equalsIgnoreCase(rejectStatus.getSourceSubState())) {
					throw RasExceptionBundle.bundle.LogEventStatusNotAllow();
				}
				if (rejectStatus.getSourceComClass().equalsIgnoreCase(port.getComClass())
					&& rejectStatus.getSourceState().equalsIgnoreCase(port.getState())
					&& rejectStatus.getSourceSubState().equalsIgnoreCase(port.getSubState())) {
					throw RasExceptionBundle.bundle.LogEventStatusNotAllow();
				}
			}
			//检查允许状态,找到第一个满足条件的设备状态
			for (EventStatus allowStatus : allowEventStatus) {
				if (EventStatus.ALL_FLAG.equalsIgnoreCase(allowStatus.getSourceComClass())) {
					return allowStatus;
				}
				if (allowStatus.getSourceComClass().equalsIgnoreCase(port.getComClass())
					&& EventStatus.ALL_FLAG.equalsIgnoreCase(allowStatus.getSourceState())) {
					return allowStatus;
				}
				if (allowStatus.getSourceComClass().equalsIgnoreCase(port.getComClass())
					&& allowStatus.getSourceState().equalsIgnoreCase(port.getState())
					&& EventStatus.ALL_FLAG.equalsIgnoreCase(allowStatus.getSourceSubState())) {
					return allowStatus;
				}
				if (allowStatus.getSourceComClass().equalsIgnoreCase(port.getComClass())
					&& allowStatus.getSourceState().equalsIgnoreCase(port.getState())
					&& allowStatus.getSourceSubState().equalsIgnoreCase(port.getSubState())) {
					return allowStatus;
				}
			}
			return null;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 通过portRRN查询该port可用的事件信息
	 * @param portRrn
	 */
	public List<RasEvent> getPortAvaliableEvents(long portRrn, SessionContext sc) throws ClientException {
		try {
			List<RasEvent> eqpEvents = new ArrayList<RasEvent>();
			Port port = em.find(Port.class, portRrn);
			if (port.getStatusModelRrn() == null) {
				throw RasExceptionBundle.bundle.LogEventNoStatusModelFound();
			}
			RasStatusModel statusModel = em.find(RasStatusModel.class, port.getStatusModelRrn());
			
//			ADUser user = securityManager.doLogin(sc.getOrgRrn(), sc.getUserName());
			ADUser user = securityManager.getUserByUserName(sc.getOrgRrn(), sc.getUserName());
			for (StatusModelEvent modelEvent : statusModel.getModelEvents()) {
				if (modelEvent.getUserGroups() != null && modelEvent.getUserGroups().size() > 0) {
					for (ADUserGroup group : user.getUserGroups()) {
						for (ADUserGroup modelGroup : modelEvent.getUserGroups()) {
							if (group.getObjectRrn().equals(modelGroup.getObjectRrn())) {
								RasEvent event = em.find(RasEvent.class, modelEvent.getEventRrn());
								EventStatus allowStatus = getCurrentEventStatus(port, event);
								if (allowStatus != null) {
									if (!eqpEvents.contains(event)) {
										eqpEvents.add(event);
									}
								}
							}
						}
					}
				} else {
					RasEvent event = em.find(RasEvent.class, modelEvent.getEventRrn());
					EventStatus allowStatus = getCurrentEventStatus(port, event);
					if (allowStatus != null) {
						if (!eqpEvents.contains(event)) {
							eqpEvents.add(event);
						}
					}
				}
			}
			return eqpEvents;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 保存设备position集
	 * @param positionSet 设备position集
	 */
    @Override
    public PositionSet savePositionSet(PositionSet positionSet, SessionContext sc) throws ClientException {
        try {
            positionSet.setOrgRrn(sc.getOrgRrn());
            positionSet.setUpdated(new Date());
            positionSet.setUpdatedBy(sc.getUserName());
            boolean editFlag = false;
            if (positionSet.getObjectRrn() == null) {
                positionSet.setCreated(new Date());
                positionSet.setCreatedBy(sc.getUserName());
            } else {
                editFlag = true;
            }
            positionSet = (PositionSet) adManager.saveEntity(positionSet, sc);
            //保存历史
            PositionSetHis positionSetHis = new PositionSetHis(positionSet);
            positionSetHis.setPositionSetRrn(positionSet.getObjectRrn());
            if (editFlag) {
            	positionSetHis.setTransType(HistoryUtil.TRANSTYPE_UPDATE);
            } else {
            	positionSetHis.setTransType(HistoryUtil.TRANSTYPE_CREATE);
            }
            em.persist(positionSetHis);
            return positionSet;
        } catch (Exception e){ 
			throw ExceptionManager.handleException(logger, e);
        }  
        
    }

    /**
     * 删除设备position集
     * @param positionSet
     */
    @Override
    public void deletePositionSet(PositionSet positionSet, SessionContext sc)
            throws ClientException {
        try {
            em.remove(em.find(PositionSet.class, positionSet.getObjectRrn()));
            //保存历史
            PositionSetHis positionSetHis = new PositionSetHis(positionSet);
            positionSetHis.setPositionSetRrn(positionSet.getObjectRrn());
            positionSetHis.setTransType(HistoryUtil.TRANSTYPE_DELETE);
            em.persist(positionSetHis);
        } catch (Exception e){ 
			throw ExceptionManager.handleException(logger, e);
        }
    }
    
    /**
     * 获取设备物料位置
     * @param equipmentId 设备名称
     * @param positionName 位置名称
     */
    public PositionSetLine getEquipmentPositonLine(Long orgRrn, String equipmentId, String positionName, boolean isThrowException) throws ClientException {   	
    	try {
    		Equipment equipment = getEquipmentByEquipmentId(orgRrn, equipmentId);
    		if (equipment.getPositionSetRrn() != null) {
    			//只有设备上定义了物料位置才检查
	    		List<PositionSetLine> positionSetLines = adManager.getEntityList(orgRrn, PositionSetLine.class, 
	    				Integer.MAX_VALUE, " positionSetRrn = " + equipment.getPositionSetRrn() + " and positionName = '" + positionName + "'", "");
	    		if (!positionSetLines.isEmpty()) {
	    			return positionSetLines.get(0);
	    		}
    		}
    		if (isThrowException) {
    			throw RasExceptionBundle.bundle.EqpPositionSetIsNull();
    		}
    		return null;
    	} catch (Exception e){ 
    		throw ExceptionManager.handleException(logger, e);
        }
    }

	/**
	 * 查询设备所有的PositionLine <br>
	 *
	 * @param orgRrn        区域号
	 * @param equipmentId   设备号
	 * @param exceptionFlag 是否抛出异常
	 * @return List<PositionSetLine>
	 */
	public List<PositionSetLine> getEquipmentPositionLines(Long orgRrn, String equipmentId, boolean exceptionFlag) throws ClientException {
		try {
			Equipment equipment = getEquipmentByEquipmentId(orgRrn, equipmentId, exceptionFlag);
			if (equipment.getPositionSetRrn() != null) {
				//只有设备上定义了物料位置才检查
				List<PositionSetLine> positionSetLines = adManager.getEntityList(orgRrn, PositionSetLine.class,
						Integer.MAX_VALUE, " positionSetRrn = " + equipment.getPositionSetRrn(), "");
				if (!positionSetLines.isEmpty()) {
					return positionSetLines;
				}
			}
			return null;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

    /**
     * 保存设备工作站点和工作站点的绑定关系
     * @param workStation 工作站点
     * @param equipmentWorkStations 设备工作站点
     */
    @Override
    public WorkStation saveIpBindingEqp(WorkStation workStation, List<EquipmentWorkStation> equipmentWorkStations, SessionContext sc)
            throws ClientException {
        sc.buildTransInfo();
        try {
          if (workStation.getObjectRrn() != null) {
              List<EquipmentWorkStation> preEquipmentWorkStations = adManager.getEntityList(sc.getOrgRrn(), EquipmentWorkStation.class, 
                      Integer.MAX_VALUE, " stationRrn = " + workStation.getObjectRrn(), null);

              for (EquipmentWorkStation equipmentWorkStation : preEquipmentWorkStations) {
                adManager.deleteEntity(equipmentWorkStation, sc);
              }
              em.flush();
          } else {
              workStation.setOrgRrn(sc.getOrgRrn());
              workStation.setCreated(new Date());
              workStation.setCreatedBy(sc.getUserName());
              workStation.setUpdated(new Date());
              workStation.setUpdatedBy(sc.getUserName());
              em.persist(workStation);
          }

          for (EquipmentWorkStation equipmentWorkStation : equipmentWorkStations) {
              equipmentWorkStation.setOrgRrn(sc.getOrgRrn());
              equipmentWorkStation.setCreated(new Date());
              equipmentWorkStation.setCreatedBy(sc.getUserName());
              equipmentWorkStation.setUpdated(new Date());
              equipmentWorkStation.setUpdatedBy(sc.getUserName());
              equipmentWorkStation.setStationRrn(workStation.getObjectRrn());

            adManager.saveEntity(equipmentWorkStation, sc);
          }
          return workStation;
        } catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
        }
        
    }

    /**
     * 删除设备工作站点
     * @param workStation 工作站点
     */
    @Override
    public void deleteIpBindingEqp(WorkStation workStation, SessionContext sc)
            throws ClientException {
        sc.buildTransInfo();
        try {
          List<EquipmentWorkStation> preEquipmentWorkStations = adManager.getEntityList(sc.getOrgRrn(), EquipmentWorkStation.class, 
                  Integer.MAX_VALUE, " stationRrn = " + workStation.getObjectRrn(), null);

          for (EquipmentWorkStation equipmentWorkStation : preEquipmentWorkStations) {
            adManager.deleteEntity(equipmentWorkStation, sc);
          }
          em.flush();
          adManager.deleteEntity(workStation, sc);
       } catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
       }
    }
    
    /**
     * 保存设备Alarm信息
     * @param equipmentAlarm 设备alarm信息
     * @param sc
     * @throws ClientException
     */
    public void saveEquipmentAlarm(EquipmentAlarm equipmentAlarm, SessionContext sc) throws ClientException {
    	StringBuffer sql = new StringBuffer(" SELECT EquipmentAlarm FROM EquipmentAlarm EquipmentAlarm ");
    	sql.append(" WHERE ");
    	sql.append(ADBase.BASE_CONDITION_N);
    	sql.append(" AND equipmentId = :equipmentId ");
    	sql.append(" AND alarmId = :alarmId ");
    	sql.append(" AND alarmState = '" + EquipmentAlarm.ALARM_STATE_HAPPEN + "'");
		if (StringUtils.isNotEmpty(equipmentAlarm.getUnitId())) {
			sql.append(" AND unitId = :unitId ");
		}
		Query query = em.createQuery(sql.toString());
		query.setParameter("orgRrn", sc.getOrgRrn());
		query.setParameter("equipmentId", equipmentAlarm.getEquipmentId());
		query.setParameter("alarmId", equipmentAlarm.getAlarmId());
		if (StringUtils.isNotEmpty(equipmentAlarm.getUnitId())) {
			query.setParameter("unitId", equipmentAlarm.getUnitId());
		}
		List<EquipmentAlarm> equipmentAlarms = query.getResultList();
    	if (EquipmentAlarm.ALARM_STATE_HAPPEN.equals(equipmentAlarm.getAlarmState())) {
    		if (CollectionUtils.isEmpty(equipmentAlarms)) {
    			equipmentAlarm.setOrgRrn(sc.getOrgRrn());
    			equipmentAlarm.setCreated(sc.getTransTime());
    			equipmentAlarm.setCreatedBy(sc.getUserName());
    			equipmentAlarm.setUpdated(sc.getTransTime());
    			equipmentAlarm.setUpdatedBy(sc.getUserName());
    			em.persist(equipmentAlarm);
    		}
    		
    		EquipmentAlarmHis his = new EquipmentAlarmHis();
			his.setOrgRrn(sc.getOrgRrn());
			his.setIsActive(true);
			his.setCreated(new Date());
			his.setCreatedBy(sc.getUserName());
			his.setUpdated(new Date());
			his.setUpdatedBy(sc.getUserName());
			his.setAlarmId(equipmentAlarm.getAlarmId());
			his.setAlarmCode(equipmentAlarm.getAlarmCode());
			his.setAlarmState(equipmentAlarm.getAlarmState());
			his.setAlarmLevel(equipmentAlarm.getAlarmLevel());
			his.setAlarmText(equipmentAlarm.getAlarmText());
			his.setAlarmTime(equipmentAlarm.getAlarmTime());
			his.setAlarmUser(equipmentAlarm.getAlarmUser());
			his.setEquipmentId(equipmentAlarm.getEquipmentId());
			his.setEquipmentType(equipmentAlarm.getEquipmentType());
			his.setPortId(equipmentAlarm.getPortId());
			his.setUnitId(equipmentAlarm.getUnitId());
			his.setSource(equipmentAlarm.getSource());
			em.persist(his);
			
			// 发送设备警报信息
			if (RasCfMod.isEquipmentAlarmForwardToAms(sc.getOrgRrn(), sysParamManager)) {
				RASRTIUtil.alarmRti(rtiService, EqpAlarmRTI.TRANSACTION_TYPE_EQPALARM, equipmentAlarm, sc);
			}
    	} else if (EquipmentAlarm.ALARM_STATE_RELIEVE.equals(equipmentAlarm.getAlarmState())) {
    		for (EquipmentAlarm alarm : equipmentAlarms) {
    			EquipmentAlarmHis his = new EquipmentAlarmHis();
    			his.setOrgRrn(sc.getOrgRrn());
    			his.setIsActive(true);
    			his.setCreated(new Date());
    			his.setCreatedBy(sc.getUserName());
    			his.setUpdated(new Date());
    			his.setUpdatedBy(sc.getUserName());
    			his.setAlarmId(alarm.getAlarmId());
    			his.setAlarmCode(alarm.getAlarmCode());
    			his.setAlarmState(EquipmentAlarm.ALARM_STATE_RELIEVE);
    			his.setAlarmLevel(alarm.getAlarmLevel());
    			his.setAlarmText(alarm.getAlarmText());
    			his.setAlarmTime(alarm.getAlarmTime());
    			his.setAlarmUser(alarm.getAlarmUser());
    			his.setEquipmentId(alarm.getEquipmentId());
    			his.setEquipmentType(alarm.getEquipmentType());
    			his.setPortId(alarm.getPortId());
    			his.setUnitId(alarm.getUnitId());
    			his.setSource(alarm.getSource());
    			em.persist(his);
    			
    			alarm.setUpdated(new Date());
    			alarm.setUpdatedBy(sc.getUserName());
    			alarm.setAlarmState(EquipmentAlarm.ALARM_STATE_RELIEVE);
    			em.merge(alarm);
    		}
    		// 发送设备警报信息
    		if (RasCfMod.isEquipmentAlarmForwardToAms(sc.getOrgRrn(), sysParamManager)) {
    			RASRTIUtil.alarmRti(rtiService, EqpAlarmRTI.TRANSACTION_TYPE_EQPALARM, equipmentAlarm, sc);
    		}
    	}
    }

    /**
     * 新增工作站点的设备信息
     * @param workStation 工作站点
     * @param equipments 设备信息
     * @param sc
     * @throws ClientException
     */
	public void saveEqpWorkStation(WorkStation workStation, List<Equipment> equipments, SessionContext sc) throws ClientException {
		try {
			workStation = em.find(WorkStation.class, workStation.getObjectRrn());
			if (workStation == null) {
				throw RasExceptionBundle.bundle.EqpWorkStationNotFound();
			}
			for (Equipment equipment : equipments) {
				equipment = getEquipmentByEquipmentId(sc.getOrgRrn(), equipment.getEquipmentId());
				EquipmentWorkStation equipmentWorkStation = getEqpStation(workStation.getObjectRrn(),
						equipment.getObjectRrn(), sc.getOrgRrn());
				if (equipmentWorkStation != null) {
					em.remove(equipmentWorkStation);
				}
				equipmentWorkStation = new EquipmentWorkStation();
				equipmentWorkStation.setCreated(new Date());
				equipmentWorkStation.setCreatedBy(sc.getUserName());
				equipmentWorkStation.setUpdated(new Date());
				equipmentWorkStation.setUpdatedBy(sc.getUserName());
				equipmentWorkStation.setOrgRrn(sc.getOrgRrn());
				equipmentWorkStation.setStationRrn(workStation.getObjectRrn());
				equipmentWorkStation.setEquipmentId(equipment.getEquipmentId());
				equipmentWorkStation.setEquipmentRrn(equipment.getObjectRrn());
				em.persist(equipmentWorkStation);
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
    
	/**
	 * 查询工作站点的设备信息
	 * @param stationRrn 工作站点Rrn
	 * @param eqpRrn 设备Rrn
	 * @param orgRrn
	 * @return
	 * @throws ClientException
	 */
	public EquipmentWorkStation getEqpStation(Long stationRrn, Long eqpRrn, Long orgRrn) throws ClientException {
		StringBuffer hql = new StringBuffer("SELECT EquipmentWorkStation FROM EquipmentWorkStation EquipmentWorkStation");
		hql.append(" WHERE ");
		hql.append(ADBase.BASE_CONDITION_N);
		hql.append(" AND stationRrn = ");
		hql.append(stationRrn);
		hql.append(" AND equipmentRrn = ");
		hql.append(eqpRrn);
		Query query = em.createQuery(hql.toString());
		query.setParameter("orgRrn", orgRrn);
		List<EquipmentWorkStation> stations = query.getResultList();
		if (stations == null || stations.size() == 0) {
			return null;
		}
		return stations.get(0);
	}
	
	/**
	 * 删除工作站点的设备信息
	 * @param workStation 工作站点
	 * @param equipments 设备信息
	 * @param sc
	 * @throws ClientException
	 */
	public void deleteEqpStation(WorkStation workStation, List<Equipment> equipments, SessionContext sc) throws ClientException {
		workStation = em.find(WorkStation.class, workStation.getObjectRrn());
		if (workStation == null) {
			throw RasExceptionBundle.bundle.WorkStationNotFound();
		}
		for (Equipment equipment : equipments) {
			equipment = getEquipmentByEquipmentId(sc.getOrgRrn(), equipment.getEquipmentId());
			EquipmentWorkStation equipmentWorkStation = getEqpStation(workStation.getObjectRrn(),
					equipment.getObjectRrn(), sc.getOrgRrn());
			if (equipmentWorkStation != null) {
				em.remove(equipmentWorkStation);
			}
		}
	}


	/**
	* Hold设备端口
	* @param portRrn 	设备端口ObjectRrn
	* @param actionComment 		备注
	* @param sc
	* @return
	*/ 
	public void holdPort(long portRrn, String actionCode, String actionReason, String actionComment,
			SessionContext sc) throws ClientException {	
		try {
			StringBuffer sql = new StringBuffer(" UPDATE Port ");
			sql.append(" SET ");
			sql.append(" holdState = :holdState, ");
			sql.append(" updated = :updated, ");
			sql.append(" updatedBy = :updatedBy ");
			sql.append(" WHERE ");
			sql.append(" objectRrn = :objectRrn ");
			Query query = em.createQuery(sql.toString());
			query.setParameter("holdState", Port.HOLDSTATE_ON);
			query.setParameter("updated", new Date());
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("objectRrn", portRrn);
			query.executeUpdate();	
			
			Port port = em.find(Port.class, portRrn);		
			PortHis his = new PortHis(port);
			his.setUpdated(new Date());
			his.setUpdatedBy(sc.getUserName());
			his.setTransType(PortHis.TRANSTYPE_HOLD);
			his.setHoldState(Port.HOLDSTATE_ON);
			his.setActionCode(actionCode);
			his.setActionReason(actionReason);
			his.setActionComment(actionComment);
			em.persist(his);	
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	/**
	* release Hold的设备端口
	* @param portRrn 	设备ObjectRrn
	* @param actionComment 		备注
	* @param sc
	* @return
	*/ 
	public void releasePort(long portRrn, String actionCode, String actionReason, String actionComment, 
			SessionContext sc) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" UPDATE Port ");
			sql.append(" SET ");
			sql.append(" holdState = :holdState, ");
			sql.append(" updated = :updated, ");
			sql.append(" updatedBy = :updatedBy ");
			sql.append(" WHERE ");
			sql.append(" objectRrn = :objectRrn ");
			Query query = em.createQuery(sql.toString());
			query.setParameter("holdState", Port.HOLDSTATE_OFF);
			query.setParameter("updated", new Date());
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("objectRrn", portRrn);
			query.executeUpdate();	
			
			Port port = em.find(Port.class, portRrn);
			PortHis his = new PortHis(port);
			his.setUpdated(new Date());
			his.setUpdatedBy(sc.getUserName());
			his.setTransType(PortHis.TRANSTYPE_RELEASE);		
			his.setActionCode(actionCode);
			his.setActionReason(actionReason);
			his.setActionComment(actionComment);
			em.persist(his);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 获取Port派工预留信息
	 * @param transType 事务类型
	 * @param portRrn 端口RRN
	 * @param portId 端口ID
	 * @param durableId 载具ID
	 * @param orgRrn
	 * @return
	 * @throws ClientException
	 */
	@Override
	public List<PortRequestReserved> getPortRequestReserved(String transType, Long portRrn, String portId, String durableId, Long orgRrn)
			throws ClientException {
		try {
			StringBuffer sql = new StringBuffer("SELECT PortRequestReserved FROM PortRequestReserved PortRequestReserved");
			sql.append(" WHERE");
			sql.append(ADBase.BASE_CONDITION_N);
			if (!StringUtil.isEmpty(transType)) {
				sql.append(" AND transType = :transType");
			}
			if (portRrn != null) {
				sql.append(" AND portRrn = :portRrn");
			}
			if (!StringUtil.isEmpty(portId)) {
				sql.append(" AND portId = :portId");
			}
			if (!StringUtil.isEmpty(durableId)) {
				sql.append(" AND durableId = :durableId");
			}
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			if (!StringUtil.isEmpty(transType)) {
				query.setParameter("transType", transType);
			}
			if (portRrn != null) {
				query.setParameter("portRrn", portRrn);
			}
			if (!StringUtil.isEmpty(portId)) {
				query.setParameter("portId", portId);
			}
			if (!StringUtil.isEmpty(durableId)) {
				query.setParameter("durable", durableId);
			}
			List<PortRequestReserved> reservedList = query.getResultList();
			return reservedList;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 保存派工预留信息
	 * @param reserved port预留信息
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	@Override
	public PortRequestReserved savePortRequestReserved(PortRequestReserved reserved, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			if (reserved.getObjectRrn() == null) {
				reserved.setOrgRrn(sc.getOrgRrn());
				reserved.setIsActive(true);
				reserved.setCreatedBy(sc.getUserName());
				reserved.setCreated(new Date());
				reserved.setUpdatedBy(sc.getUserName());
				reserved.setUpdated(new Date());

				em.persist(reserved);
			} else {
				reserved.setUpdatedBy(sc.getUserName());
				reserved.setUpdated(new Date());
				reserved = em.merge(reserved);
			}
			return reserved;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 移除派工预留信息
	 * @param reserved 预留信息
	 * @throws ClientException
	 */
	public void removePortRequestReserved(PortRequestReserved reserved) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer("DELETE FROM PortRequestReserved PortRequestReserved");
			sql.append(" WHERE");
			sql.append(" objectRrn = :objectRrn");
			Query query = em.createQuery(sql.toString());
			query.setParameter("objectRrn", reserved.getObjectRrn());
			query.executeUpdate();
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * HoldCapa把EquipmentCapa表的isAvailable设定为'N'
	 * @param capaName 设备能力名称
	 * @param equipmentId 设备ID
	 * @param actionCode 动作码
	 * @param actionComment 动作备注
	 * @throws ClientException
	 */
	public void holdCapa(String capaName, String equipmentId, 
			String actionCode, String actionComment, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			//保存Event历史
			Equipment equipment = getEquipmentByEquipmentId(sc.getOrgRrn(), equipmentId);
			Capa capa = getCapaByName(sc.getOrgRrn(), capaName);
            EquipmentHis his = new EquipmentHis(equipment, sc);
	        his.setTransType(EquipmentHis.TRANSTYPE_HOLD_CAPA);
	        his.setActionCode(actionCode);
		    his.setActionReason(capa.getName());
		    his.setActionComment(actionComment);
		    his.setHisComment(HistoryUtil.buildHisComment("Capa", capaName));
	        em.persist(his);
	            
	        StringBuffer sql = new StringBuffer(" UPDATE EquipmentCapa ");
			sql.append(" SET ");
			sql.append(" isAvailable = 'N', ");
			sql.append(" updated = :updated, ");
			sql.append(" updatedBy = :updatedBy ");
			sql.append(" WHERE ");
			sql.append(" capaRrn = :capaRrn ");
			sql.append(" AND equipmentRrn = :equipmentRrn ");
			Query query = em.createQuery(sql.toString());
			query.setParameter("updated", new Date());
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("capaRrn", capa.getObjectRrn());
			query.setParameter("equipmentRrn", equipment.getObjectRrn());
			query.executeUpdate();		
			
		} catch (Exception e) { 	
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * ReleaseCapa把EquipmentCapa表的isAvailable设定为'Y'
	 * @param capaName 设备能力名称
	 * @param equipmentId 设备ID
	 * @param actionCode 动作码
	 * @param actionComment 动作备注
	 * @throws ClientException
	 */
	public void releaseCapa(String capaName, String equipmentId, 
			String actionCode, String actionComment, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			//保存Event历史
			Equipment equipment = getEquipmentByEquipmentId(sc.getOrgRrn(), equipmentId);
			Capa capa = getCapaByName(sc.getOrgRrn(), capaName);
			
			EquipmentHis his = new EquipmentHis(equipment, sc);
            his.setTransType(EquipmentHis.TRANSTYPE_RELEASE_CAPA);
            his.setActionCode(actionCode);
			his.setActionReason(capa.getName());
			his.setActionComment(actionComment);
		    his.setHisComment(HistoryUtil.buildHisComment("Capa", capaName));
            em.persist(his);
            
			StringBuffer sql = new StringBuffer(" UPDATE EquipmentCapa ");
			sql.append(" SET ");
			sql.append(" isAvailable = 'Y', ");
			sql.append(" updated = :updated, ");
			sql.append(" updatedBy = :updatedBy ");
			sql.append(" WHERE ");
			sql.append(" capaRrn = :capaRrn ");
			sql.append(" AND equipmentRrn = :equipmentRrn ");
			Query query = em.createQuery(sql.toString());
			query.setParameter("updated", new Date());
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("capaRrn", capa.getObjectRrn());
			query.setParameter("equipmentRrn", equipment.getObjectRrn());
			query.executeUpdate();				
			
		} catch (Exception e) { 	
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 设备长时间状态检查
	 * 如果超出Alarm时间则发送警报
	 */
	@Asynchronous
	public void longEqpStateAlarm() throws ClientException {
		StringBuffer sql = new StringBuffer(" SELECT Equipment, EquipmentStateColor FROM Equipment Equipment, EquipmentStateColor EquipmentStateColor ");
		sql.append(" WHERE ");
		sql.append(" Equipment.orgRrn = EquipmentStateColor.orgRrn ");
		sql.append(" AND Equipment.state = EquipmentStateColor.state ");
		sql.append(" AND Equipment.stateEntryTime IS NOT NULL ");
		sql.append(" AND EquipmentStateColor.alarmInterval IS NOT NULL ");
		try {
			Query query = em.createQuery(sql.toString());
			List<Object> objects = query.getResultList();
			
			for (Object object : objects) {
				Object[] obj = (Object[])object;
				Equipment equipment = (Equipment)obj[0];
				EquipmentStateColor state =  (EquipmentStateColor)obj[1];
				
				Date stateDate = equipment.getStateEntryTime();
				Date newDate = new Date();
				long date = (newDate.getTime() - stateDate.getTime())/(1000 * 60);
				if(state.getAlarmInterval().compareTo(date) < 0) {
					//发送Alarm消息
					SessionContext sc = new SessionContext();
				    sc.buildTransInfo();
					//TODO 处理trace
					RASRTIUtil.alarmRti(rtiService, EqpAlarmRTI.TRANSACTION_TYPE_LONGEQPSTATE, equipment, state.getAlarmInterval(), sc);
				}
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * Port长时间状态检查
	 * 如果超出Alarm时间则发送警报
	 */
	@Asynchronous
	public void longPortStateAlarm() throws ClientException {
		StringBuffer sql = new StringBuffer(" SELECT Port, EquipmentStateColor FROM Port Port, EquipmentStateColor EquipmentStateColor ");
		sql.append(" WHERE ");
		sql.append(" Port.orgRrn = EquipmentStateColor.orgRrn ");
		sql.append(" AND Port.transferState = EquipmentStateColor.state ");
		sql.append(" AND Port.transferStateEntryTime IS NOT NULL ");
		sql.append(" AND EquipmentStateColor.alarmInterval IS NOT NULL ");
		try {
			Query query = em.createQuery(sql.toString());
			List<Object> objects = query.getResultList();
			
			for (Object object : objects) {
				Object[] obj = (Object[])object;
				Port port = (Port)obj[0];
				EquipmentStateColor state =  (EquipmentStateColor)obj[1];
				
				Date stateDate = port.getTransferStateEntryTime();
				Date newDate = new Date();
				long date = (newDate.getTime() - stateDate.getTime())/(1000 * 60);
				if(state.getAlarmInterval().compareTo(date) < 0) {
					//发送Alarm消息
					SessionContext sc = new SessionContext();
				    sc.buildTransInfo();
					//TODO 处理trace
					RASRTIUtil.alarmRti(rtiService, PortAlarmRTI.TRANSACTION_TYPE_LONGSTATE, port, state.getAlarmInterval(), sc);
				}
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 跟据主设备，获取其他的组合设备
	 * @param equipment 设备
	 * @param subEquipments 子设备
	 */
	public Equipment saveEquipmentCombine(Equipment equipment, List<Equipment> subEquipments, SessionContext sc) throws ClientException {
		try {			
			sc.buildTransInfo();
			
			StringBuffer sql = new StringBuffer("DELETE FROM EquipmentCombine EquipmentCombine");
			sql.append(" WHERE");
			sql.append(" equipmentRrn = :equipmentRrn");
			Query query = em.createQuery(sql.toString());
			query.setParameter("equipmentRrn", equipment.getObjectRrn());
			query.executeUpdate();
			
			for (Equipment subEquipment : subEquipments) {
				EquipmentCombine equipmentCombine = new EquipmentCombine();
				equipmentCombine.setOrgRrn(sc.getOrgRrn());
				equipmentCombine.setCreatedBy(sc.getUserName());
				equipmentCombine.setUpdatedBy(sc.getUserName());
				equipmentCombine.setEquipmentId(equipment.getEquipmentId());
				equipmentCombine.setEquipmentRrn(equipment.getObjectRrn());
				equipmentCombine.setSubEquipmentId(subEquipment.getEquipmentId());
				equipmentCombine.setSubEquipmentRrn(subEquipment.getObjectRrn());
				
				List<Capa> capas = getAvailableCapaByEqp(equipment.getObjectRrn());
				if (capas != null && capas.size() > 0) {
					equipmentCombine.setCapaName(capas.get(0).getName());
				}
			
				List<Capa> subCapas = getAvailableCapaByEqp(subEquipment.getObjectRrn());
				if (subCapas != null && subCapas.size() > 0) {
					equipmentCombine.setSubCapaName(subCapas.get(0).getName());
				}
				
				em.persist(equipmentCombine);
			}
			
			return equipment;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 跟据主设备，获取其他的组合设备
	 * @param parentEquipmentRrn 主设备Rrn
	 */
	public List<Equipment> getSubEquipmentCombineByParent(long parentEquipmentRrn) throws ClientException {
		try {			
			String sql = "SELECT Equipment FROM Equipment Equipment WHERE Equipment.objectRrn IN (" +
					"SELECT EquipmentCombine.subEquipmentRrn FROM EquipmentCombine EquipmentCombine WHERE EquipmentCombine.equipmentRrn = :equipmentRrn)";
			Query query = em.createQuery(sql.toString());
			query.setParameter("equipmentRrn", parentEquipmentRrn);
			
			List<Equipment> equipments =  (List<Equipment>)query.getResultList();
			if (equipments != null) {
				return equipments;
			}
			return new ArrayList<Equipment>();
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 跟据主设备和子设备能力，获取其他的组合设备
	 * @param parentEquipmentRrn 主设备Rrn
	 */
	public List<Equipment> getSubEquipmentCombineByParentAndSubCapa(long parentEquipmentRrn, String subCapaName) throws ClientException {
		try {			
			String sql = "SELECT Equipment FROM Equipment Equipment WHERE Equipment.objectRrn IN (" +
					"SELECT EquipmentCombine.subEquipmentRrn FROM EquipmentCombine EquipmentCombine WHERE EquipmentCombine.equipmentRrn = :equipmentRrn and EquipmentCombine.subCapaName = :subCapaName)";
			Query query = em.createQuery(sql.toString());
			query.setParameter("equipmentRrn", parentEquipmentRrn);
			query.setParameter("subCapaName", subCapaName);
			
			List<Equipment> equipments =  (List<Equipment>)query.getResultList();
			if (equipments != null) {
				return equipments;
			}
			return new ArrayList<Equipment>();
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 获取EquipmentCurrent,如果不存在则自动创建
	 * 
	 * @param equipmentRrn 设备ObjectRrn
	 * @param sc
	 */
	public EquipmentCurrent getEquipmentCurrent(long equipmentRrn, SessionContext sc) throws ClientException {
		try {
			return getEquipmentCurrent(equipmentRrn, true, sc);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 获取EquipmentCurrent(支持EquipmentCurrent不存在时自动创建)
	 * 
	 * @param equipmentRrn 设备ObjectRrn
	 * @param isCreateIfNotExist 如果EquipmentCurrent不存在是否创建一个新的EquipmentCurrent
	 * @param sc
	 */
	public EquipmentCurrent getEquipmentCurrent(long equipmentRrn, boolean isCreateIfNotExist, SessionContext sc) throws ClientException {
		try {
			String sql = "SELECT EquipmentCurrent FROM EquipmentCurrent EquipmentCurrent WHERE ";
			sql += " equipmentRrn = :equipmentRrn ";
			
			Query query = em.createQuery(sql);
			query.setParameter("equipmentRrn", equipmentRrn);
			
			List<EquipmentCurrent> equipmentCurrents = query.getResultList();
			if (CollectionUtils.isEmpty(equipmentCurrents)) {
				if (isCreateIfNotExist) {
					Equipment equipment = new Equipment();
					equipment.setObjectRrn(equipmentRrn);
					
					EquipmentCurrent current = new EquipmentCurrent(equipment, sc);
					em.persist(current);
					em.flush();
					return current;
				}
			}
						
			return equipmentCurrents.get(0);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	
	/**
	 * 更新设备当前信息
	 * 
	 * @param equipments 待更新的设备
	 * @param isUpdateEquipmentRecipe 是否更新设备Recipe
	 * @param currentEquipmentRecipe 待更新的设备Recipe
	 * @param isUpdateRecipe 是否更新Logic Recipe
	 * @param currentRecipeName 待更新的Logic Recipe
	 * @param isUpdateProcessMode 是否更新设备ProcessMode
	 * @param currentProcessMode 待更新的设备ProcessMode
	 * @param isUpdateCurrentPart 是否更新设备当前产品
	 * @param currentPartName 待更新的设备当前产品
	 * @param isUpdateCurrentPart 是否更新设备当前产品
	 * @param currentPartName 待更新的设备当前产品
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	@Override
	public void updateEquipmentCurrent(List<Equipment> equipments, 
			boolean isUpdateEquipmentRecipe, String currentEquipmentRecipe, boolean isUpdateRecipe, String currentRecipeName,
			boolean isUpdateProcessMode, String currentProcessMode, boolean isUpdateCurrentPart, String currentPartName, 
			boolean isUpdateCurrentWorkOrder, String currentWoId, SessionContext sc) throws ClientException {
		try {
			if (CollectionUtils.isNotEmpty(equipments)) {
				for (Equipment equipment : equipments) {
					EquipmentCurrent current = getEquipmentCurrent(equipment.getObjectRrn(), sc);
					
					StringBuffer sql = new StringBuffer(" UPDATE EquipmentCurrent ");
					sql.append(" SET ");
					if (isUpdateEquipmentRecipe) {
						sql.append(" currentRecipeName = :currentRecipeName, ");
					}
					if (isUpdateRecipe) {
						sql.append(" currentEquipmentRecipe = :currentEquipmentRecipe, ");
					}
					if (isUpdateProcessMode) {
						sql.append(" currentProcessMode = :currentProcessMode, ");
					}
					if (isUpdateCurrentPart) {
						sql.append(" currentPartName = :currentPartName, ");
					}
					if (isUpdateCurrentWorkOrder) {
						sql.append(" currentWoId = :currentWoId, ");
					}
					sql.append(" updated = :updated, ");
					sql.append(" updatedBy = :updatedBy ");
					sql.append(" WHERE ");
					sql.append(" equipmentRrn = :equipmentRrn ");
					Query query = em.createQuery(sql.toString());
					if (isUpdateEquipmentRecipe) {
				    	query.setParameter("currentEquipmentRecipe", currentEquipmentRecipe);
					}
					if (isUpdateRecipe) {
				    	query.setParameter("currentRecipeName", currentRecipeName);
					}
					if (isUpdateProcessMode) {
				    	query.setParameter("currentProcessMode", currentProcessMode);
					}
					if (isUpdateCurrentPart) {
				    	query.setParameter("currentPartName", currentPartName);
					}
					if (isUpdateCurrentWorkOrder) {
				    	query.setParameter("currentWoId", currentWoId);
					}
			    	query.setParameter("updated", new Date());
			    	query.setParameter("updatedBy", sc.getUserName());
			    	query.setParameter("equipmentRrn", equipment.getObjectRrn());
			    	query.executeUpdate();

					//记录历史
					EquipmentEventHis his = new EquipmentEventHis(equipment, current, sc);
					his.setEventId(EquipmentEventHis.TRANSTYPE_CHANGE_CURRENT);
					his.setOperator(sc.getUserName());
					if (isUpdateEquipmentRecipe) {
						 his.setTargetEquipmentRecipe(currentEquipmentRecipe);
					} else {
						 his.setTargetEquipmentRecipe(current.getCurrentEquipmentRecipe());
					}
					if (isUpdateRecipe) {
						his.setCurrentRecipeName(currentRecipeName);
					} else {
						 his.setCurrentRecipeName(current.getCurrentRecipeName());
					}
					if (isUpdateProcessMode) {
						his.setTargetProcessMode(currentProcessMode);
					} else {
						his.setTargetProcessMode(current.getCurrentProcessMode());
					}
					if (isUpdateCurrentPart) {
						his.setTargetPartName(currentPartName);
					} else {
						 his.setTargetPartName(current.getCurrentPartName());
					}
					if (isUpdateCurrentWorkOrder) {
						his.setTargetWoId(currentWoId);
					} else {
						 his.setTargetWoId(current.getCurrentWoId());
					}
		            em.persist(his);
				}
			}
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 保存设备状态级联变化
	 * @param stateCascade 设备状态级联
	 * @param transType 事件类型
	 */
	@Override
	public EquipmentStateCascade saveEqpStateCascade(EquipmentStateCascade stateCascade, String transType, SessionContext sc) throws ClientException {
		sc.buildTransInfo();
		try {
			if (stateCascade.getObjectRrn() == null) {
				transType = EquipmentStateCascadeHis.TRANSTYPE_CREATE;
			} else {
				if (StringUtil.isEmpty(transType)) {
					transType = EquipmentStateCascadeHis.TRANSTYPE_UPDATE;
				}
			}
			
			stateCascade = (EquipmentStateCascade) adManager.saveEntity(stateCascade, sc);
			
			EquipmentStateCascadeHis his = new EquipmentStateCascadeHis(stateCascade, sc);
			his.setTransType(transType);
			em.persist(his);
			
			return stateCascade;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 删除设备状态级联变化（子设备状态变化触发父设备状态变化）
	 * @stateCascade 设备状态级联
	 */
	@Override
	public void deleteEqpStateCascade(EquipmentStateCascade stateCascade, SessionContext sc) throws ClientException {
		sc.buildTransInfo();
		try {
			EquipmentStateCascadeHis his = new EquipmentStateCascadeHis(stateCascade, sc);
			his.setTransType(EquipmentStateCascadeHis.TRANSTYPE_DELETE);
			em.persist(his);
			
			adManager.deleteEntity(stateCascade, sc);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 批量保存port
	 * @param ports 待保存的Port
	 * @param sc
	 */
	@Override
	public void saveUploadPortList (List<Port> ports, SessionContext sc) throws ClientException {
		try {
			for(Port port : ports) {
				if (port.getParentEqpRrn() == null && StringUtils.isEmpty(port.getParentEqpId())) {
					throw RasExceptionBundle.bundle.EquipmentNoFound();
				}
				if (port.getParentEqpRrn() == null) {
					Equipment eqp = getEquipmentByEquipmentId(sc.getOrgRrn(), port.getParentEqpId(), true);
					port.setParentEqpRrn(eqp.getObjectRrn());
				}
				if (StringUtils.isEmpty(port.getParentEqpId())) {
					Equipment eqp = em.find(Equipment.class, port.getParentEqpRrn());
					port.setParentEqpId(eqp.getEquipmentId());
				}
				
				port.setIsActive(true);
				port.setCreated(new Date());
				port.setCreatedBy(sc.getUserName());
				port.setUpdated(new Date());
				port.setUpdatedBy(sc.getUserName());
				port = em.merge(port);
				
				PortHis portHis = new PortHis(port);
				portHis.setTransType(HistoryUtil.TRANSTYPE_CREATE);
				em.persist(portHis);
			}
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 查询设备下的Internal Buffer
	 * @param parentEqpRrn 主设备RRN
	 * @param parentEqpId 主设备ID
	 */
	@Override
	public List<Equipment> getInternalBufferByParentEqp(long orgRrn, Long parentEqpRrn, String parentEqpId) throws ClientException {
		try {
			if (parentEqpRrn == null && !StringUtil.isEmpty(parentEqpId)) {
				Equipment parentEqp = getEquipmentByEquipmentId(orgRrn, parentEqpId);
				parentEqpRrn = parentEqp.getObjectRrn();
			}
			
			if (parentEqpRrn != null) {
				String sql = "SELECT Equipment FROM Equipment Equipment WHERE ";
				sql += ADBase.BASE_CONDITION_N;
				sql += " AND ";
				sql += " parentEqpRrn = :parentEqpRrn ";
				sql += " AND ";
				sql += " category = :category ";
				
				Query query = em.createQuery(sql);
				query.setParameter("orgRrn", orgRrn);
				query.setParameter("parentEqpRrn", parentEqpRrn);
				query.setParameter("category", Equipment.CATEGORY_BUFFER);
				
				List<Equipment> buffers = query.getResultList();
				return buffers;
			}
			
			return null;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 查询设备的BatchControl
	 * @param orgRrn
	 * @param eqpId
	 * @return
	 * @throws ClientException
	 */
	@Override
	public List<EquipmentBatchControl> getEquipmentBatchControls(long orgRrn, String eqpId) throws ClientException {
		try {
			if (StringUtil.isEmpty(eqpId)) {
				return Lists.newArrayList();
			}
			
			String sql = "SELECT EquipmentBatchControl FROM EquipmentBatchControl EquipmentBatchControl WHERE ";
			sql += ADBase.BASE_CONDITION_N;
			sql += " AND ";
			sql += " equipmentId = :equipmentId ";
			
			Query query = em.createQuery(sql);
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("equipmentId", eqpId);
			
			List<EquipmentBatchControl> batchControls = query.getResultList();
			return batchControls;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * 按设备组获取设备
	 * @param orgRrn
	 * @param eqpGroup
	 * @param exceptionFlag 是否抛出异常
	 * @return List<Equipment>
	 * @throws ClientException
	 */
	public List<Equipment> getEquipmentByEqpGroup(long orgRrn, String eqpGroup, boolean exceptionFlag) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" SELECT Equipment FROM Equipment Equipment ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND eqpGroup = :eqpGroup ");

			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("eqpGroup", eqpGroup);
			List<Equipment> equipmentList = query.getResultList();
			if (equipmentList == null || equipmentList.size() == 0) {
				if (!exceptionFlag) {
					return null;
				}
				throw RasExceptionBundle.bundle.EquipmentNoFound();
			}
			return equipmentList;
		}  catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 按设备组获取设备
	 * @param orgRrn
	 * @param eqpGroup
	 * @return List<Equipment>
	 * @throws ClientException
	 */
	public List<Equipment> getEquipmentByEqpGroup(long orgRrn, String eqpGroup) throws ClientException {
		return getEquipmentByEqpGroup(orgRrn, eqpGroup, false);
	}
	
	/**
	 * 保存设备组，设备组与设备关系，设备组与用户组，用户关系
	 * @param equipmentGroup 设备组
	 * @param equipmentList 设备列表
	 * @param userGroups 用户组列表
	 * @param users 用户列表
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public EquipmentGroup saveEquipmentGroup(EquipmentGroup equipmentGroup, 
			List<Equipment> equipmentList, List<ADUserGroup> userGroups, List<ADUser> users, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			equipmentGroup.setIsActive(true);
			equipmentGroup.setOrgRrn(sc.getOrgRrn());	
			if (equipmentGroup.getObjectRrn() == null) {
				List<EquipmentGroup> equipmentGroups = adManager.getEntityList(sc.getOrgRrn(), EquipmentGroup.class, Integer.MAX_VALUE, " name = '" + equipmentGroup.getName() + "'", "");
				if (equipmentGroups != null && equipmentGroups.size() > 0) {
					throw RasExceptionBundle.bundle.EquipmentGroupNameRepeat();
				}
				equipmentGroup.setCreatedBy(sc.getUserName());	
				equipmentGroup.setUpdatedBy(sc.getUserName());
				em.persist(equipmentGroup);
			} else {
				equipmentGroup.setUpdatedBy(sc.getUserName());
				em.merge(equipmentGroup);
			}
			
			//获取数据库中绑定了设备组的设备列表
			List<Equipment> queryEquipmentList = getEquipmentByEqpGroup(sc.getOrgRrn(), equipmentGroup.getName());
			if (CollectionUtils.isNotEmpty(queryEquipmentList) && CollectionUtils.isNotEmpty(equipmentList)) {
				//移除页面设备列表
				queryEquipmentList.removeAll(equipmentList);
			}
			//对页面设备列表进行绑定设备组
			if (CollectionUtils.isNotEmpty(equipmentList)) {
				for (Equipment equipment : equipmentList) {
					if (!equipmentGroup.getName().equals(equipment.getEqpGroup())) {
						equipment.setUpdatedBy(sc.getUserName());
						equipment.setEqpGroup(equipmentGroup.getName());
						em.merge(equipment);
						
						EquipmentHis equipmentHis = new EquipmentHis(equipment, sc);
						equipmentHis.setTransType(EquipmentHis.TRANSTYPE_ASSIGN_EQPGROUP);
						em.persist(equipmentHis);
					}			
				}			
			}
			//对非页面设备列表进行解绑设备组
			if (CollectionUtils.isNotEmpty(queryEquipmentList)) {
				for (Equipment queryEquipment : queryEquipmentList) {
					queryEquipment.setUpdatedBy(sc.getUserName());
					queryEquipment.setEqpGroup(null);
					em.merge(queryEquipment);
					
					EquipmentHis equipmentHis = new EquipmentHis(queryEquipment, sc);
					equipmentHis.setTransType(EquipmentHis.TRANSTYPE_DEASSIGN_EQPGROUP);
					em.persist(equipmentHis);
				}
			}
			
			List<ADAccessCategory> queryAccessCategoryList = securityManager.getADAccessCategory(sc.getOrgRrn(), 
					EquipmentGroup.class.getSimpleName(), null, equipmentGroup.getName());
			if (CollectionUtils.isNotEmpty(users)) {	
				for (ADUser user : users) {
					boolean exist = false;
					if (CollectionUtils.isNotEmpty(queryAccessCategoryList)) {
						for (ADAccessCategory queryAccessCategory : queryAccessCategoryList) {
							if (user.getObjectRrn().equals(queryAccessCategory.getUserRrn())) {
								exist = true;	
							}
						}
					}
					if (!exist) {
						ADAccessCategory accessCategory = new ADAccessCategory();
						accessCategory.setIsActive(true);
						accessCategory.setOrgRrn(sc.getOrgRrn());
						accessCategory.setCreatedBy(sc.getUserName());
						accessCategory.setUpdatedBy(sc.getUserName());
						accessCategory.setCategoryType(EquipmentGroup.class.getSimpleName());
						accessCategory.setCategoryName(equipmentGroup.getName());
						accessCategory.setUserRrn(user.getObjectRrn());
						securityManager.getEntityManager().persist(accessCategory);
						
						ADAccessCategoryHis accessCategoryHis = new ADAccessCategoryHis(accessCategory, sc);
						accessCategoryHis.setTransTime(new Date());
						accessCategoryHis.setTransType(ADAccessCategoryHis.TRANSTYPE_CREATE);
						securityManager.getEntityManager().persist(accessCategoryHis);
					}
				}
			}
			
			if (CollectionUtils.isNotEmpty(userGroups)) {				
				for (ADUserGroup userGroup : userGroups) {
					boolean exist = false;
					if (CollectionUtils.isNotEmpty(queryAccessCategoryList)) {
						for (ADAccessCategory queryAccessCategory : queryAccessCategoryList) {
							if (userGroup.getObjectRrn().equals(queryAccessCategory.getUserGroupRrn())) {
								exist = true;	
							}
						}
					}
					if (!exist) {
						ADAccessCategory accessCategory = new ADAccessCategory();
						accessCategory.setIsActive(true);
						accessCategory.setOrgRrn(sc.getOrgRrn());
						accessCategory.setCreatedBy(sc.getUserName());
						accessCategory.setUpdatedBy(sc.getUserName());
						accessCategory.setCategoryType(EquipmentGroup.class.getSimpleName());
						accessCategory.setCategoryName(equipmentGroup.getName());
						accessCategory.setUserGroupRrn(userGroup.getObjectRrn());
						securityManager.getEntityManager().persist(accessCategory);
						
						ADAccessCategoryHis accessCategoryHis = new ADAccessCategoryHis(accessCategory, sc);
						accessCategoryHis.setTransTime(new Date());
						accessCategoryHis.setTransType(ADAccessCategoryHis.TRANSTYPE_CREATE);
						securityManager.getEntityManager().persist(accessCategoryHis);
					}
				}
			}
			
			if (CollectionUtils.isNotEmpty(queryAccessCategoryList)) {
				//删除不存在的
				for (ADAccessCategory queryAccessCategory : queryAccessCategoryList) {
					boolean exist = false;
					if (CollectionUtils.isNotEmpty(users)) {	
						for (ADUser user : users) {
							if (user.getObjectRrn().equals(queryAccessCategory.getUserRrn())) {
								exist = true;	
							}	
						}
					}
					if (CollectionUtils.isNotEmpty(userGroups)) {				
						for (ADUserGroup userGroup : userGroups) {
							if (userGroup.getObjectRrn().equals(queryAccessCategory.getUserGroupRrn())) {
								exist = true;	
							}
						}
					}
					if (!exist) {						
						ADAccessCategoryHis accessCategoryHis = new ADAccessCategoryHis(queryAccessCategory, sc);
						accessCategoryHis.setTransTime(new Date());
						accessCategoryHis.setTransType(ADAccessCategoryHis.TRANSTYPE_DELETE);
						securityManager.getEntityManager().persist(accessCategoryHis);
						
						queryAccessCategory = securityManager.getEntityManager().find(ADAccessCategory.class, queryAccessCategory.getObjectRrn());
						securityManager.getEntityManager().remove(queryAccessCategory);				
					}
				}					
			}

			return equipmentGroup;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 删除设备组
	 * @param equipmentGroupRrn 设备组主键
	 * @throws ClientException
	 */
	public void deleteEquipmentGroup(long equipmentGroupRrn, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			EquipmentGroup equipmentGroup = em.find(EquipmentGroup.class, equipmentGroupRrn);
			if (equipmentGroup != null) {
				List<Equipment> equipmentLists = adManager.getEntityList(sc.getOrgRrn(), Equipment.class, Integer.MAX_VALUE, " eqpGroup = '" + equipmentGroup.getName() + "'", "");
				if (CollectionUtils.isNotEmpty(equipmentLists)) {
					throw RasExceptionBundle.bundle.EquipmentGroupExistEquipment();
				}
				
				List<ADAccessCategory> queryAccessCategoryList = securityManager.getADAccessCategory(sc.getOrgRrn(), 
						EquipmentGroup.class.getSimpleName(), null, equipmentGroup.getName());
				if (CollectionUtils.isNotEmpty(queryAccessCategoryList)) {
					for (ADAccessCategory queryAccessCategory : queryAccessCategoryList) {
						ADAccessCategoryHis accessCategoryHis = new ADAccessCategoryHis(queryAccessCategory, sc);
						accessCategoryHis.setTransTime(new Date());
						accessCategoryHis.setTransType(ADAccessCategoryHis.TRANSTYPE_DELETE);
						securityManager.getEntityManager().persist(accessCategoryHis);
						
						securityManager.getEntityManager().remove(queryAccessCategory);		
					}
				}
				
				em.remove(equipmentGroup);
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
}
