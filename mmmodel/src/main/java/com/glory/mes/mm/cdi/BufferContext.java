package com.glory.mes.mm.cdi;

import com.glory.framework.core.util.SessionContext;
import com.glory.mes.mm.inv.model.Storage;
import com.glory.mes.mm.inv.model.Warehouse;
import com.google.common.collect.Maps;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 */
@SuppressWarnings("serial")
public class BufferContext implements Serializable {
	public static final String ACTION_NAME_BUFFER_DATA = "BufferDataRequestAction";

	public static final String ACTION_NAME_RACK_FORBIDDEN = "RackForbiddenRequestAction";

	private String actionType;

	private String actionName;

	private SessionContext sessionContext;

	private Map<String, Object> datas;

	private Storage storage;

	private Warehouse warehouse;

	public String getActionName() {
		return actionName;
	}

	public void setActionName(String actionName) {
		this.actionName = actionName;
	}

	public Storage getStorage() {
		return storage;
	}

	public void setStorage(Storage storage) {
		this.storage = storage;
	}

	public Warehouse getWarehouse() {
		return warehouse;
	}

	public void setWarehouse(Warehouse warehouse) {
		this.warehouse = warehouse;
	}

	public BufferContext() {
	}

	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}

	public SessionContext getSessionContext() {
		return sessionContext;
	}

	public void setSessionContext(SessionContext sessionContext) {
		this.sessionContext = sessionContext;
	}

	public Object getData(String key) {
		if (datas != null) {
			return datas.get(key);
		}
		return null;
	}

	public void setData(String key, Object data) {
		if (datas == null) {
			datas = Maps.newHashMap();
		}
		datas.put(key, data);
	}
	
}
