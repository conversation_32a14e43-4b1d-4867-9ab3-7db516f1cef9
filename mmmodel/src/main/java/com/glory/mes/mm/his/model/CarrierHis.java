package com.glory.mes.mm.his.model;

import com.glory.framework.core.util.SessionContext;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.his.model.DurableHis;

public class CarrierHis extends DurableHis {

	private static final long serialVersionUID = 1L;
	
	public static final String TRANS_LOADCOMPLETE = "LOADCOMPLETE";
	public static final String TRANS_UNLOADCOMPLETE = "UNLOADCOMPLETE";
	public static final String TRANS_BUFFERIN = "BUFFERIN";
	public static final String TRANS_BUFFEROUT = "BUFFEROUT";
	
	public static final String TRANS_CHANGELOCATION = "CHANGELOCATION";
	
	public static String TRANS_CREATE_CARRIERBATCH = "CREATECARRIERBATCH";
	public static String TRANS_CANCEL_CARRIERBATCH = "CANCELCARRIERBATCH";
	
	public CarrierHis() {}
	
	public CarrierHis(Carrier carrier, SessionContext sc) {
		super(carrier, sc);
	}
}
