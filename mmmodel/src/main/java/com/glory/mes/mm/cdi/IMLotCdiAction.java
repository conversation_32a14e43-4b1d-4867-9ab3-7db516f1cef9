package com.glory.mes.mm.cdi;

import com.glory.framework.core.cdi.ICdiAction;

public interface IMLotCdiAction extends ICdiAction {
	
	public static final String CDI_POINT_CHANGE_STATE = "ChangeState";
	public static final String CDI_POINT_IQC = "IQC";

	/**
	 * PreCheck:在方法执行前检查(用于限制检查)
	 * PreExecute:在方法执行前执行
	 * PostExecute:在方法执后前执行
	 */
	public static final String TRIGGER_POINT_PRECHECK = "PreCheck";
	public static final String TRIGGER_POINT_PREEXECUTE = "PreExecute";
	public static final String TRIGGER_POINT_POSTEXECUTE = "PostExecute";	
	
	public String getCdiPointName();
	
	/**
	 * 触发类型
	 */
	public String getTriggerPoint();

	public MLotContext invoke(MLotContext context) throws Exception;

	public default boolean isDefaultEnable() {
		return true;	
	}
	
	/**
	 * 是否允许Disable
	 */
	public default boolean isAllowDisable() {
		return true;
	}
	
}
