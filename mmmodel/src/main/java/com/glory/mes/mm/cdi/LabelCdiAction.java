package com.glory.mes.mm.cdi;

import java.util.Map;

import com.glory.framework.core.cdi.ICdiAction;
import com.glory.framework.core.exception.ClientException;

public interface LabelCdiAction extends ICdiAction {

	public static final String ACTION_NAME = "LabelAction";
	
	public static final String PROPERTY_TEMPLATE_CONTENT_BINARY = "templateContentBinary";
	public static final String PROPERTY_TEMPLATE_NAME = "templateName";
	public static final String PROPERTY_TEMPLATE_TYPE = "templateType";
	
	public Map<String, Object> getLabelByName(long orgRrn, String name) throws ClientException;
}
