package com.glory.mes.mm.his.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;


import com.glory.framework.activeentity.model.ADBaseHisSeq;
import com.glory.framework.core.util.IDynamicComponent;


public class MLotSBDHis extends ADBaseHisSeq implements IDynamicComponent {

	private static final long serialVersionUID = 1L;
	
	public static String TRANS_SCRAPLOT = "SCRAPLOT";
	public static String TRANS_UNSCRAPLOT = "UNSCRAPLOT";
	public static String TRANS_IDENTIFY_COMPONENT="IDENTIFY_COMPONENT";

	private String updatedBy;
	
	private Date transTime;
	
	private String hisSeq;
	
	private Long hisSeqNo;
	
	private String transType;
	
	private Long mLotRrn;
	
	private String mLotId;
	
	private Long mComponentRrn;
	
	private String mComponentId;
	
	private String position;
	
	private String materialName;
	
	private Long materialVersion;
	
	private BigDecimal mainQty;
	
	private BigDecimal subQty;
	
	private Long warehouseRrn;
	
	private String warehouseId;
	 
	private String storageType;
	 
	private String storageId;

	private String equipmentId;

	private String equipmentUnitId;
	
	private String actionStepName;
	
	private String actionCode;
	
	private String actionReason;

	private String actionComment;
	
	//责任人
	private String actionOperator;
	
	//责任组
	private String actionOwner;

	private Map udf;
	
	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getTransTime() {
		return transTime;
	}

	public void setTransTime(Date transTime) {
		this.transTime = transTime;
	}

	public String getHisSeq() {
		return hisSeq;
	}

	public void setHisSeq(String hisSeq) {
		this.hisSeq = hisSeq;
	}

	public Long getHisSeqNo() {
		return hisSeqNo;
	}

	public void setHisSeqNo(Long hisSeqNo) {
		this.hisSeqNo = hisSeqNo;
	}

	public String getTransType() {
		return transType;
	}

	public void setTransType(String transType) {
		this.transType = transType;
	}

	public Long getmLotRrn() {
		return mLotRrn;
	}

	public void setmLotRrn(Long mLotRrn) {
		this.mLotRrn = mLotRrn;
	}

	public String getmLotId() {
		return mLotId;
	}

	public void setmLotId(String mLotId) {
		this.mLotId = mLotId;
	}

	public Long getmComponentRrn() {
		return mComponentRrn;
	}

	public void setmComponentRrn(Long mComponentRrn) {
		this.mComponentRrn = mComponentRrn;
	}

	public String getmComponentId() {
		return mComponentId;
	}

	public void setmComponentId(String mComponentId) {
		this.mComponentId = mComponentId;
	}

	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public String getMaterialName() {
		return materialName;
	}

	public void setMaterialName(String materialName) {
		this.materialName = materialName;
	}

	public Long getMaterialVersion() {
		return materialVersion;
	}

	public void setMaterialVersion(Long materialVersion) {
		this.materialVersion = materialVersion;
	}

	public BigDecimal getMainQty() {
		return mainQty;
	}

	public void setMainQty(BigDecimal mainQty) {
		this.mainQty = mainQty;
	}

	public BigDecimal getSubQty() {
		return subQty;
	}

	public void setSubQty(BigDecimal subQty) {
		this.subQty = subQty;
	}

	public Long getWarehouseRrn() {
		return warehouseRrn;
	}

	public void setWarehouseRrn(Long warehouseRrn) {
		this.warehouseRrn = warehouseRrn;
	}

	public String getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(String warehouseId) {
		this.warehouseId = warehouseId;
	}

	public String getStorageType() {
		return storageType;
	}

	public void setStorageType(String storageType) {
		this.storageType = storageType;
	}

	public String getStorageId() {
		return storageId;
	}

	public void setStorageId(String storageId) {
		this.storageId = storageId;
	}

	public String getEquipmentId() {
		return equipmentId;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	public String getEquipmentUnitId() {
		return equipmentUnitId;
	}

	public void setEquipmentUnitId(String equipmentUnitId) {
		this.equipmentUnitId = equipmentUnitId;
	}

	public String getActionStepName() {
		return actionStepName;
	}

	public void setActionStepName(String actionStepName) {
		this.actionStepName = actionStepName;
	}

	public String getActionCode() {
		return actionCode;
	}

	public void setActionCode(String actionCode) {
		this.actionCode = actionCode;
	}

	public String getActionReason() {
		return actionReason;
	}

	public void setActionReason(String actionReason) {
		this.actionReason = actionReason;
	}

	public String getActionComment() {
		return actionComment;
	}

	public void setActionComment(String actionComment) {
		this.actionComment = actionComment;
	}

	public String getActionOperator() {
		return actionOperator;
	}

	public void setActionOperator(String actionOperator) {
		this.actionOperator = actionOperator;
	}

	public String getActionOwner() {
		return actionOwner;
	}

	public void setActionOwner(String actionOwner) {
		this.actionOwner = actionOwner;
	}

	@Override
	public Map getUdf() {
		return udf;
	}

	@Override
	public void setUdf(Map dynamicComponent) {
		this.udf = dynamicComponent;
	}
}
