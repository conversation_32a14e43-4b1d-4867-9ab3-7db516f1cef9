package com.glory.mes.mm.his.model;

import java.math.BigDecimal;
import java.util.Map;

import com.glory.framework.base.model.VersionControl;
import com.glory.framework.core.util.IDynamicComponent;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.tenant.model.TenantSupport;
import com.glory.mes.mm.model.Material;

/**
 * 物料定义
 * 包括原物料、产品等都统一管理为物料
 * 通过CLASS来区别是那种类型的物料
 */
public class MaterialHis extends VersionControl implements IDynamicComponent, TenantSupport {
	
	private static final long serialVersionUID = 1L;
		
	private String transType;
	
	private Long materialRrn;
	
	private String displayVerison;

	private String isGlobal;
	
	/**
	 * 产品对应的工艺名称
	 * 在物料为产品时使用
	 */
	private String processName;
	
	/**
	 * 产品对应的工艺版本
	 * 在物料为产品时使用
	 */
	private Long processVersion;
	
	private String tenantId;
	
	/**
	 * 客户代码 
	 */
	private String partnerCode;
	
	/**
	 * EAN码是国际物品编码协会制定的一种商品用条码
	 * European Article Number(国标GB-12094-1998)
	 */
	private String ean;

	/**
	 * 一般在在连锁零售行业中使用,保存库存控制的最小可用单位
	 * 每种产品均对应有唯一的SKU
	 * 对于同一款商品,如果有多颜色，也会有多个SKU
	 * Stock Keeping Unit
	 */
	private String sku;
	
	/**
	 * 物料单位
	 * 如果使用了UOMConversion
	 * 此处必须为物料的最小单位 
	 */
	private String uomId;
			
	/**
	 * 物料子类别(在原物料时使用)
	 * 在物料类别的基础上对物料进行进一步的分组
	 * 可以为不同的subCategory的物料定义不同的Attribute(MaterialAttribute)
	 */
	private String category;
	
	/**
	 * 物料类型
	 */
	private String materialType;
	
	/**
	 * 主材料类型
	 * (对应Lot的mainMatType,在投料时自动带到Lot上)
	 */
	private String mainMatType;

	/**
	 * 子材料类型
	 * (对应Lot的subMatType,在投料时自动带到Lot上)
	 */
	private String subMatType;
	
	/**
	 * 物料组别1
	 */
	private String group1;
	
	/**
	 * 物料组别2
	 */
	private String group2;
	
	/**
	 * 物料组别3
	 */
	private String group3;	
	
	/**
	 * 物料组别4
	 */
	private String group4;
	
	/**
	 * 物料ABC分类法
	 * 对于库存的所有物料,按照全年货币价值从大到小排序,分为三大类
	 * 分别称为A类、B类和C类
	 * A类物料价值最高，受到高度重视，处于中间的B类物料受重视程度稍差，而C类物料价值低，仅进行例行控制管理
	 */
	private String classification;
	
	/**
	 * 物料规格1
	 */
	private String spec1;
	

	/**
	 * 物料规格2
	 */
	private String spec2;
	
	/**
	 * 物料规格3
	 */
	private String spec3;
	
	/**
	 * 物料规格4
	 */
	private String spec4;
	
	private String bomVerified;
	
	private Long statusModelRrn;

	/**
	 * 是否生产物料
	 */
	private String isProduction = "N";
	
	/**
	 * 是否虚拟料
	 */
	private String isPhantom;
	
		
	/**
	 * 安全库存量
	 */
	private BigDecimal safetyStockQty;
	
	/**
	 * 最大库存量
	 */
	private BigDecimal maxStockQty;
	
	/**
	 * 包装数量
	 */
	private BigDecimal numberOfPack;
	
	/**
	 * 包装层级
	 */
	private String packageHierarchyName;
	
	/**
	 * 托盘数量
	 */
	private BigDecimal numberOfPallet;
	
	/**
	 * Batch类型
	 */
	private String batchType;
	
	/**
	 * 批量大小
	 */
	private BigDecimal lotSize;
	
	private BigDecimal subLotSize;
	
	/**
	 * 批号规则
	 */
	private String idGenerator;

	/**
	 * 是否时间敏感物料
	 */
	private String isTimeSensitive = "N";
	
	/**
	 * 保质期
	 */
	private Long shelfWarning;
	
	/**
	 * 保质期
	 */
	private Long shelfLife;
	
	/**
	 * 保质期单位
	 */
	private String shelfLifeUnit;
	
	/**
	 * 使用有效期(车间寿命)
	 */
	private Long floorLife;
	
	/**
	 * 使用有效期单位
	 */
	private String floorLifeUnit;
	
	/**
	 * 何时使用,如生产接受时使用/设备绑定时使用/手动使用等
	 * 此栏位决定了,使用有效期何时开始生效
	 */
	private String floorLifeActivator;
	
	/**
	 * 次数有警告
	 */
	private Long limitWarning;
	


	/**
	 * 次数有效期
	 */
	private Long limitLife;
	
	/**
	 * 体积
	 */
	private BigDecimal volume;
	
	/**
	 * 重量
	 */
	private BigDecimal weight;
	
	/**
	 * 宽度
	 */
	private BigDecimal shelfWidth;
	
	/**
	 * 高度
	 */
	private BigDecimal shelfHeight;
	
	/**
	 * 深度
	 */
	private BigDecimal shelfDepth;
		
	/**
	 * 责任人1
	 */
	private String owner1;
	
	/**
	 * 责任人2
	 */
	private String owner2;
	
	/**
	 * 备注
	 */
	private String comments;
	
	private String reserved1;

	private String reserved2;

	private String reserved3;

	private String reserved4;

	private String reserved5;
	
	private String reserved6;
	
	private String reserved7;
	
	private String reserved8;

	
	private String actionCode;
	
	private String actionReason;
	
	private String actionComment;
	
	private String hisComment;
	
	private Map udf;
	
	public MaterialHis() {
	}
	
	public MaterialHis(Material material) {
		PropertyUtil.copyProperties(this, material);
		this.setObjectRrn(null);
		this.setCreated(null);
		this.setCreatedBy(null);
		this.setLockVersion(null);
		this.setMaterialRrn(material.getObjectRrn());
	}
	
	public String getTransType() {
		return transType;
	}

	public void setTransType(String transType) {
		this.transType = transType;
	}

	public Long getMaterialRrn() {
		return materialRrn;
	}

	public void setMaterialRrn(Long materialRrn) {
		this.materialRrn = materialRrn;
	}
	
	public Boolean getIsGlobal() {
		return "Y".equalsIgnoreCase(this.isGlobal) ? true : false;
	}

	public void setIsGlobal(Boolean isGlobal) {
		if(isGlobal != null) {
			this.isGlobal = isGlobal ? "Y" : "N";
		}else {
			this.isGlobal = "N";
		}
	}
	
	public String getDisplayVerison() {
		return displayVerison;
	}

	public void setDisplayVerison(String displayVerison) {
		this.displayVerison = displayVerison;
	}
	
	public String getProcessName() {
		return processName;
	}

	public void setProcessName(String processName) {
		this.processName = processName;
	}

	public Long getProcessVersion() {
		return processVersion;
	}

	public void setProcessVersion(Long processVersion) {
		this.processVersion = processVersion;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getPartnerCode() {
		return partnerCode;
	}

	public void setPartnerCode(String partnerCode) {
		this.partnerCode = partnerCode;
	}

	public String getEan() {
		return ean;
	}

	public void setEan(String ean) {
		this.ean = ean;
	}

	public String getSku() {
		return sku;
	}

	public void setSku(String sku) {
		this.sku = sku;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getMaterialType() {
		return materialType;
	}

	public void setMaterialType(String materialType) {
		this.materialType = materialType;
	}

	public String getMainMatType() {
		return mainMatType;
	}

	public void setMainMatType(String mainMatType) {
		this.mainMatType = mainMatType;
	}
	
	public String getSubMatType() {
		return subMatType;
	}

	public void setSubMatType(String subMatType) {
		this.subMatType = subMatType;
	}
	
	public String getUomId() {
		return uomId;
	}

	public void setUomId(String uomId) {
		this.uomId = uomId;
	}

	public String getGroup1() {
		return group1;
	}

	public void setGroup1(String group1) {
		this.group1 = group1;
	}

	public String getGroup2() {
		return group2;
	}

	public void setGroup2(String group2) {
		this.group2 = group2;
	}

	public String getGroup3() {
		return group3;
	}

	public void setGroup3(String group3) {
		this.group3 = group3;
	}

	public String getGroup4() {
		return group4;
	}

	public void setGroup4(String group4) {
		this.group4 = group4;
	}

	public String getClassification() {
		return classification;
	}

	public void setClassification(String classification) {
		this.classification = classification;
	}

	public String getSpec1() {
		return spec1;
	}

	public void setSpec1(String spec1) {
		this.spec1 = spec1;
	}

	public String getSpec2() {
		return spec2;
	}

	public void setSpec2(String spec2) {
		this.spec2 = spec2;
	}

	public String getSpec3() {
		return spec3;
	}

	public void setSpec3(String spec3) {
		this.spec3 = spec3;
	}

	public String getSpec4() {
		return spec4;
	}

	public void setSpec4(String spec4) {
		this.spec4 = spec4;
	}

	public Boolean getIsProduction() {
		return "Y".equalsIgnoreCase(this.isProduction) ? true : false;
	}

	public void setIsProduction(Boolean isProduction) {
		if(isProduction != null) {
			this.isProduction = isProduction ? "Y" : "N";
		}else {
			this.isProduction = "N";
		}
		
	}
	
	public Boolean getIsPhantom() {
		return "Y".equalsIgnoreCase(this.isPhantom) ? true : false;
	}

	public void setIsPhantom(Boolean isPhantom) {
		if(isPhantom != null) {
			this.isPhantom = isPhantom ? "Y" : "N";
		}else {
			this.isPhantom = "N";
		}
	}
	
	public String getBomVerified() {
		return bomVerified;
	}

	public void setBomVerified(String bomVerified) {
		this.bomVerified = bomVerified;
	}
	
	public Long getStatusModelRrn() {
		return statusModelRrn;
	}

	public void setStatusModelRrn(Long statusModelRrn) {
		this.statusModelRrn = statusModelRrn;
	}
	
	public BigDecimal getSafetyStockQty() {
		return safetyStockQty;
	}

	public void setSafetyStockQty(BigDecimal safetyStockQty) {
		this.safetyStockQty = safetyStockQty;
	}

	public BigDecimal getMaxStockQty() {
		return maxStockQty;
	}

	public void setMaxStockQty(BigDecimal maxStockQty) {
		this.maxStockQty = maxStockQty;
	}

	public BigDecimal getNumberOfPack() {
		return numberOfPack;
	}

	public void setNumberOfPack(BigDecimal numberOfPack) {
		this.numberOfPack = numberOfPack;
	}

	public String getPackageHierarchyName() {
		return packageHierarchyName;
	}

	public void setPackageHierarchyName(String packageHierarchyName) {
		this.packageHierarchyName = packageHierarchyName;
	}

	public BigDecimal getNumberOfPallet() {
		return numberOfPallet;
	}

	public void setNumberOfPallet(BigDecimal numberOfPallet) {
		this.numberOfPallet = numberOfPallet;
	}

	public String getBatchType() {
        return batchType;
    }

    public void setBatchType(String batchType) {
        this.batchType = batchType;
    }

    public BigDecimal getLotSize() {
		return lotSize;
	}

	public void setLotSize(BigDecimal lotSize) {
		this.lotSize = lotSize;
	}

	public BigDecimal getSubLotSize() {
		return subLotSize;
	}

	public void setSubLotSize(BigDecimal subLotSize) {
		this.subLotSize = subLotSize;
	}
	
	public String getIdGenerator() {
		return idGenerator;
	}

	public void setIdGenerator(String idGenerator) {
		this.idGenerator = idGenerator;
	}

	public Boolean getIsTimeSensitive() {
		return "Y".equalsIgnoreCase(this.isTimeSensitive) ? true : false;
	}

	public void setIsTimeSensitive(Boolean isTimeSensitive) {
		if(isTimeSensitive != null) {
			this.isTimeSensitive = isTimeSensitive ? "Y" : "N";
		}else {
			this.isTimeSensitive = "N";
		}
	}

	public Long getShelfLife() {
		return shelfLife;
	}

	public void setShelfLife(Long shelfLife) {
		this.shelfLife = shelfLife;
	}
	
	public Long getShelfWarning() {
		return shelfWarning;
	}

	public void setShelfWarning(Long shelfWarning) {
		this.shelfWarning = shelfWarning;
	}

	public String getShelfLifeUnit() {
		return shelfLifeUnit;
	}

	public void setShelfLifeUnit(String shelfLifeUnit) {
		this.shelfLifeUnit = shelfLifeUnit;
	}

	public Long getFloorLife() {
		return floorLife;
	}

	public void setFloorLife(Long floorLife) {
		this.floorLife = floorLife;
	}

	public String getFloorLifeUnit() {
		return floorLifeUnit;
	}

	public void setFloorLifeUnit(String floorLifeUnit) {
		this.floorLifeUnit = floorLifeUnit;
	}

	public String getFloorLifeActivator() {
		return floorLifeActivator;
	}

	public void setFloorLifeActivator(String floorLifeActivator) {
		this.floorLifeActivator = floorLifeActivator;
	}
	
	public Long getLimitWarning() {
		return limitWarning;
	}

	public void setLimitWarning(Long limitWarning) {
		this.limitWarning = limitWarning;
	}
	
	public Long getLimitLife() {
		return limitLife;
	}

	public void setLimitLife(Long limitLife) {
		this.limitLife = limitLife;
	}

	public BigDecimal getVolume() {
		return volume;
	}

	public void setVolume(BigDecimal volume) {
		this.volume = volume;
	}

	public BigDecimal getWeight() {
		return weight;
	}

	public void setWeight(BigDecimal weight) {
		this.weight = weight;
	}

	public BigDecimal getShelfWidth() {
		return shelfWidth;
	}

	public void setShelfWidth(BigDecimal shelfWidth) {
		this.shelfWidth = shelfWidth;
	}

	public BigDecimal getShelfHeight() {
		return shelfHeight;
	}

	public void setShelfHeight(BigDecimal shelfHeight) {
		this.shelfHeight = shelfHeight;
	}

	public BigDecimal getShelfDepth() {
		return shelfDepth;
	}

	public void setShelfDepth(BigDecimal shelfDepth) {
		this.shelfDepth = shelfDepth;
	}

	public String getOwner1() {
		return owner1;
	}

	public void setOwner1(String owner1) {
		this.owner1 = owner1;
	}
	
	public String getOwner2() {
		return owner2;
	}
	
	public void setOwner2(String owner2) {
		this.owner2 = owner2;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getReserved1() {
		return reserved1;
	}

	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}

	public String getReserved2() {
		return reserved2;
	}

	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}

	public String getReserved3() {
		return reserved3;
	}

	public void setReserved3(String reserved3) {
		this.reserved3 = reserved3;
	}

	public String getReserved4() {
		return reserved4;
	}

	public void setReserved4(String reserved4) {
		this.reserved4 = reserved4;
	}

	public String getReserved5() {
		return reserved5;
	}

	public void setReserved5(String reserved5) {
		this.reserved5 = reserved5;
	}

	public String getReserved6() {
		return reserved6;
	}

	public void setReserved6(String reserved6) {
		this.reserved6 = reserved6;
	}

	public String getReserved7() {
		return reserved7;
	}

	public void setReserved7(String reserved7) {
		this.reserved7 = reserved7;
	}

	public String getReserved8() {
		return reserved8;
	}

	public void setReserved8(String reserved8) {
		this.reserved8 = reserved8;
	}
	
	@Override
	public Map getUdf() {
		return udf;
	}

	@Override
	public void setUdf(Map dynamicComponent) {
		this.udf = dynamicComponent;
	}

	public String getActionCode() {
		return actionCode;
	}

	public void setActionCode(String actionCode) {
		this.actionCode = actionCode;
	}

	public String getActionReason() {
		return actionReason;
	}

	public void setActionReason(String actionReason) {
		this.actionReason = actionReason;
	}

	public String getActionComment() {
		return actionComment;
	}

	public void setActionComment(String actionComment) {
		this.actionComment = actionComment;
	}

	public String getHisComment() {
		return hisComment;
	}

	public void setHisComment(String hisComment) {
		this.hisComment = hisComment;
	}

}
