package com.glory.mes.mm.his.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.glory.framework.activeentity.model.ADBaseHisSeq;
import com.glory.framework.core.util.IDynamicComponent;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.mes.mm.model.PackageRule;

public class PackageRuleHis extends ADBaseHisSeq implements IDynamicComponent {

	private static final long serialVersionUID = 1L;
	    
	private String updatedBy;

	private String transType;
	
	private Date transTime;

	private String hisSeq;
	
	private Long hisSeqNo;
		
	private Long packageRuleRrn;
		
	private String name;
	
	private String description;
	
	private Integer style = 0;
	
	private String packageTypeName;
	
	private String isDefault;
	
	private String defaultLabelName;
	
	/**
	 * 包装数量计数类型
	 */
	private String sourceQtyType;

	/**
	 * 包装数量
	 */
	private BigDecimal sourceMainQty;
	
	private String mergeRuleName;
	
	private Map udf;
	
	public PackageRuleHis() {}
		
	public PackageRuleHis(PackageRule packageRule, SessionContext sc) {
		PropertyUtil.copyProperties(this, packageRule);
		this.setObjectRrn(null);
	
		this.setUpdatedBy(sc.getUserName());
		this.setTransTime(new Date());
		this.setHisSeq(sc.getTransRrn());
		this.setHisSeqNo((long)sc.getTransSeqNo());
		
		this.setPackageRuleRrn(packageRule.getObjectRrn());
		this.setIsDefault(packageRule.getIsDefault());
		if (packageRule.getUdf() != null) {
			Map udf = new HashMap();
			udf.putAll(packageRule.getUdf());
			this.setUdf(udf);
		}
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public String getTransType() {
		return transType;
	}

	public void setTransType(String transType) {
		this.transType = transType;
	}

	public Date getTransTime() {
		return transTime;
	}

	public void setTransTime(Date transTime) {
		this.transTime = transTime;
	}

	public String getHisSeq() {
		return hisSeq;
	}

	public void setHisSeq(String hisSeq) {
		this.hisSeq = hisSeq;
	}

	public Long getHisSeqNo() {
		return hisSeqNo;
	}

	public void setHisSeqNo(Long hisSeqNo) {
		this.hisSeqNo = hisSeqNo;
	}

	public Long getPackageRuleRrn() {
		return packageRuleRrn;
	}

	public void setPackageRuleRrn(Long packageRuleRrn) {
		this.packageRuleRrn = packageRuleRrn;
	}
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getStyle() {
		return style;
	}

	public void setStyle(Integer style) {
		this.style = style;
	}

	public String getPackageTypeName() {
		return packageTypeName;
	}

	public void setPackageTypeName(String packageTypeName) {
		this.packageTypeName = packageTypeName;
	}

	public Boolean getIsDefault() {
		return "Y".equalsIgnoreCase(this.isDefault) ? true : false; 
	}
	
	public void setIsDefault(Boolean isDefault) {
		this.isDefault = isDefault ? "Y" : "N";
	}

	public String getDefaultLabelName() {
		return defaultLabelName;
	}

	public void setDefaultLabelName(String defaultLabelName) {
		this.defaultLabelName = defaultLabelName;
	}

	public String getSourceQtyType() {
		return sourceQtyType;
	}

	public void setSourceQtyType(String sourceQtyType) {
		this.sourceQtyType = sourceQtyType;
	}

	public BigDecimal getSourceMainQty() {
		return sourceMainQty;
	}

	public void setSourceMainQty(BigDecimal sourceMainQty) {
		this.sourceMainQty = sourceMainQty;
	}

	public String getMergeRuleName() {
		return mergeRuleName;
	}

	public void setMergeRuleName(String mergeRuleName) {
		this.mergeRuleName = mergeRuleName;
	}

	@Override
	public Map getUdf() {
		return udf;
	}

	@Override
	public void setUdf(Map dynamicComponent) {
		this.udf = dynamicComponent;
	}
	
}
