package com.glory.mes.mm.bom.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;

import com.glory.framework.activeentity.model.ADBaseObjectSeq;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.framework.core.util.IDynamicComponent;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.model.Material;
import com.google.common.collect.Lists;

/**
 * 所有BOMLine的抽象类
 */
@MappedSuperclass
public class AbstractBomLine extends ADBaseObjectSeq implements Comparable<AbstractBomLine>, IDynamicComponent {
	
	private static final long serialVersionUID = 1L;
	
	/** Component(Material) = CO */
	public static final String ITEMCATEGORY_COMPONENT = "CO";
	/** Tools = TL */
	public static final String ITEMCATEGORY_TOOLS = "TL";
	/** Co-Product = CP 
	 *  联产品指用同一种原料经过同一个生产过程，生产出两种或两种以上的不同的产品
	 */
	public static final String ITEMCATEGORY_CO_PRODUCT = "CP";
	
	public static final String FLUSH_TYPE_BOM = "BOM";
	public static final String FLUSH_TYPE_NONE = "NONE";
	
	@Column(name = "BOM_RRN")
	private Long bomRrn;

	/**
	 * BOMLine在BOM中的顺序
	 */
	@Column(name="SEQ_NO")
	private Long seqNo;
	
	/**
	 * ITEM的类别
	 */
	@Column(name="ITEM_CATEGORY")
	private String itemCategory;
	
	/**
	 * BOMLine所在的Step名称
	 */
	@Column(name="STEP_NAME")
	private String stepName;
	
	/**
	 * BOMLine所在的Step版本
	 */
	@Column(name="STEP_VERSION")
	private Long stepVersion;
	
	/**
	 * 对应物料的ObjectRrn
	 */
	@Column(name="MATERIAL_RRN")
	private Long materialRrn;
	
	/**
	 * 对应物料的名称
	 */
	@Column(name="MATERIAL_NAME")
	private String materialName;
	
	@Column(name="MATERIAL_VERSION")
	private Long materialVersion;
	
	/**
	 * 对应物料的名称
	 */
	@Column(name="MATERIAL_DESC")
	private String materialDesc;

	/**
	 * 对应物料的类型
	 */
	@Column(name="MATERIAL_TYPE")
	private String materialType;
	
	/**
	 * 对应物料的单位用量
	 */
	@Column(name="UNIT_QTY")
	private BigDecimal unitQty;
	
	/**
	 * 按照百分批方式定义物料与batchQty配合使用
	 */
	@Column(name="IS_QTY_PERCENT")
	private String isQtyPercent = "N";
	
	/**
	 * 对应物料的单位用量
	 */
	@Column(name="BATCH_QTY")
	private BigDecimal batchQty;
	
	/**
	 * 对应物料的单位
	 */
	@Column(name="UOM_ID")
	private String uomId;
	
	/**
	 * 对应物料的固定损耗率
	 */
	@Column(name="FIXED_QTY")
	private BigDecimal fixedQty;
	
	/**
	 * 对应物料的损耗率
	 * 实际所需数量 = fixedQty + 数量 * unitQty * (1 + lossRate)
	 */
	@Column(name="LOSS_RATE")
	private BigDecimal lossRate;
	
	/**
	 * BOMLine生效时间(未使用)
	 */
	@Column(name="VALID_FROM")
	private Date validFrom;
	
	/**
	 * BOMLine失效时间(未使用)
	 */
	@Column(name="VALID_TO")
	private Date validTo;
	
	/**
	 * 主物料
	 * 主物料在投料的时候使用
	 * 在投料时就需要选择对应的主物料
	 */
	@Column(name="IS_MAIN")
	private String isMain = "N";
	
	/**
	 * 关键物料
	 * 关键物料在批次过站的时候进行确认
	 * 一般在进站时检查物料是否正确
	 * 出站时记录对应的使用数量
	 * 需要和特殊的TrackIn/Out配合使用
	 */
	@Column(name="IS_CRITICAL")
	private String isCritical = "N";
	
	/**
	 * 键合物料
	 * 需要和特殊的键合TrackIn/Out配合使用
	 */
	@Column(name="IS_ASSEMBLY")
	private String isAssembly = "N";
	
	/**
	 * 是否需要生产
	 * 如果生产则展开下级BOM，否则将产品作为原材料
	 */
	@Column(name="IS_PRODUCTION")
	private String isProduction = "N";
	/**
	 * 可选料,该物料在实际使用过程中可使用也可不使用
	 */
	@Column(name="IS_OPTIONAL")
	private String isOptional = "N";
	
	/**
	 * 倒扣料方式
	 */
	@Column(name="FLUSH_TYPE")
	private String flushType;
	
	/**
	 * 可替代料,该物料在实际使用过程中可以被其它物料所替代
	 */
	@Column(name="IS_ALTERNATE")
	private String isAlternate = "N";
	
	/**
	 * 替代策略:全部替代或部分替代
	 * 目前仅使用全部替代
	 */
	@Column(name="ALTERNATE_STRATEGY")
	private String alternateStrategy;
	
	/**
	 * 替代组
	 * 只有同一组类的物料才能相互替代
	 */
	@Column(name="ALTERNATE_GROUP")
	private String alternateGroup;
	
	/**
	 * 替代优先级
	 */
	@Column(name="ALTERNATE_PRIORITY")
	private BigDecimal alternatePriority;
	
	/**
	 * 替代百分百
	 * 只在部分替代时使用(目前未使用)
	 */
	@Column(name="ALTERNATE_PERCENT")
	private BigDecimal alternatePercent;
	
	/**
	 * 用于Co-Product时成本分摊
	 */
	@Column(name="COST_ALLOCATION_PERCENT")
	private BigDecimal costAllocationPercent;
	
	/**
	 * 物料在设备上的位置,支持多个位置,以分号分隔
	 * 在物料校验时支持Position校验
	 */
	@Column(name="POSITION_NAME")
	private String positionName;
	
	@Column(name="COMMENTS")
	private String comments;
	
	@Column(name="RESERVED1")
	private String reserved1;
	
	@Column(name="RESERVED2")
	private String reserved2;

	@Column(name="RESERVED3")
	private String reserved3;

	@Column(name="RESERVED4")
	private String reserved4;

	@Column(name="RESERVED5")
	private String reserved5;

	@Column(name="RESERVED6")
	private String reserved6;

	@Column(name="RESERVED7")
	private String reserved7;

	@Column(name="RESERVED8")
	private String reserved8;

	@Column(name="RESERVED9")
	private String reserved9;

	@Column(name="RESERVED10")
	private String reserved10;	
	
	@Transient
	private Boolean isSelected;
	
	@Transient
	private Material material;

	@Transient
	private BigDecimal transQty;
	
	@Transient
	private Object attribute1;
	
	@Transient
	private Object attribute2;

	@Transient
	private Object attribute3;

	@Transient
	private Object attribute4;

	@Transient
	private Object attribute5;
	
	private Map udf;
	
	@Override
	public Map getUdf() {
		return udf;
	}

	@Override
	public void setUdf(Map dynamicComponent) {
		this.udf = dynamicComponent;
	}
	
	public Long getBomRrn() {
		return bomRrn;
	}

	public void setBomRrn(Long bomRrn) {
		this.bomRrn = bomRrn;
	}

	public Long getSeqNo() {
		return seqNo;
	}

	public void setSeqNo(Long seqNo) {
		this.seqNo = seqNo;
	}

	public String getItemCategory() {
		return itemCategory;
	}

	public void setItemCategory(String itemCategory) {
		this.itemCategory = itemCategory;
	}

	public String getStepName() {
		return stepName;
	}

	public void setStepName(String stepName) {
		this.stepName = stepName;
	}

	public Long getStepVersion() {
		return stepVersion;
	}

	public void setStepVersion(Long stepVersion) {
		this.stepVersion = stepVersion;
	}

	public Long getMaterialRrn() {
		return materialRrn;
	}

	public void setMaterialRrn(Long materialRrn) {
		this.materialRrn = materialRrn;
	}

	public String getMaterialName() {
		return materialName;
	}

	public void setMaterialName(String materialName) {
		this.materialName = materialName;
	}

	public Long getMaterialVersion() {
		return materialVersion;
	}

	public void setMaterialVersion(Long materialVersion) {
		this.materialVersion = materialVersion;
	}

	public String getMaterialDesc() {
		return materialDesc;
	}

	public void setMaterialDesc(String materialDesc) {
		this.materialDesc = materialDesc;
	}

	public String getMaterialType() {
		return materialType;
	}

	public void setMaterialType(String materialType) {
		this.materialType = materialType;
	}

	public BigDecimal getUnitQty() {
		return unitQty;
	}

	public void setUnitQty(BigDecimal unitQty) {
		this.unitQty = unitQty;
	}

	public Boolean getIsQtyPercent() {
		return "Y".equalsIgnoreCase(this.isQtyPercent) ? true : false;
	}

	public void setIsQtyPercent(Boolean isQtyPercent) {
		if(isQtyPercent == null) {
			this.isQtyPercent = "N";
		}else {
			this.isQtyPercent = isQtyPercent ? "Y" : "N";
		}
	}

	public BigDecimal getBatchQty() {
		return batchQty;
	}

	public void setBatchQty(BigDecimal batchQty) {
		this.batchQty = batchQty;
	}

	public String getUomId() {
		return uomId;
	}

	public void setUomId(String uomId) {
		this.uomId = uomId;
	}

	public BigDecimal getFixedQty() {
		return fixedQty;
	}

	public void setFixedQty(BigDecimal fixedQty) {
		this.fixedQty = fixedQty;
	}

	public BigDecimal getLossRate() {
		return lossRate;
	}

	public void setLossRate(BigDecimal lossRate) {
		this.lossRate = lossRate;
	}

	public Date getValidFrom() {
		return validFrom;
	}

	public void setValidFrom(Date validFrom) {
		this.validFrom = validFrom;
	}

	public Date getValidTo() {
		return validTo;
	}

	public void setValidTo(Date validTo) {
		this.validTo = validTo;
	}

	public Boolean getIsMain() {
		return "Y".equalsIgnoreCase(this.isMain) ? true : false;
	}

	public void setIsMain(Boolean isMain) {
		if(isMain == null) {
			this.isMain = "N";
		}else {
			this.isMain = isMain ? "Y" : "N";
		}
	}

	public Boolean getIsCritical() {
		return "Y".equalsIgnoreCase(this.isCritical) ? true : false;
	}

	public void setIsCritical(Boolean isCritical) {
		if(isCritical == null) {
			this.isCritical = "N";
		}else {
			this.isCritical = isCritical ? "Y" : "N";
		}
	}

	public Boolean getIsAssembly() {
		return "Y".equalsIgnoreCase(this.isAssembly) ? true : false;
	}

	public void setIsAssembly(Boolean isAssembly) {
		if(isAssembly == null) {
			this.isAssembly = "N";
		}else {
			this.isAssembly = isAssembly ? "Y" : "N";
		}
	}

	public Boolean getIsOptional() {
		return "Y".equalsIgnoreCase(this.isOptional) ? true : false;
	}

	public void setIsOptional(Boolean isOptional) {
		if(isOptional == null) {
			this.isOptional = "N";
		}else {
			this.isOptional = isOptional ? "Y" : "N";
		}
	}

	public String getFlushType() {
		return flushType;
	}

	public void setFlushType(String flushType) {
		this.flushType = flushType;
	}

	public Boolean getIsAlternate() {
		return "Y".equalsIgnoreCase(this.isAlternate) ? true : false;
	}

	public void setIsAlternate(Boolean isAlternate) {
		if(isAlternate == null) {
			this.isAlternate = "N";
		}else {
			this.isAlternate = isAlternate ? "Y" : "N";
		}
	}

	public String getAlternateStrategy() {
		return alternateStrategy;
	}

	public void setAlternateStrategy(String alternateStrategy) {
		this.alternateStrategy = alternateStrategy;
	}

	public String getAlternateGroup() {
		return alternateGroup;
	}

	public void setAlternateGroup(String alternateGroup) {
		this.alternateGroup = alternateGroup;
	}

	public BigDecimal getAlternatePriority() {
		return alternatePriority;
	}

	public void setAlternatePriority(BigDecimal alternatePriority) {
		this.alternatePriority = alternatePriority;
	}

	public BigDecimal getAlternatePercent() {
		return alternatePercent;
	}

	public void setAlternatePercent(BigDecimal alternatePercent) {
		this.alternatePercent = alternatePercent;
	}

	public BigDecimal getCostAllocationPercent() {
		return costAllocationPercent;
	}

	public void setCostAllocationPercent(BigDecimal costAllocationPercent) {
		this.costAllocationPercent = costAllocationPercent;
	}

	public String getPositionName() {
		return positionName;
	}

	public void setPositionName(String positionName) {
		this.positionName = positionName;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getReserved1() {
		return reserved1;
	}

	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}

	public String getReserved2() {
		return reserved2;
	}

	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}

	public String getReserved3() {
		return reserved3;
	}

	public void setReserved3(String reserved3) {
		this.reserved3 = reserved3;
	}

	public String getReserved4() {
		return reserved4;
	}

	public void setReserved4(String reserved4) {
		this.reserved4 = reserved4;
	}

	public String getReserved5() {
		return reserved5;
	}

	public void setReserved5(String reserved5) {
		this.reserved5 = reserved5;
	}

	public String getReserved6() {
		return reserved6;
	}

	public void setReserved6(String reserved6) {
		this.reserved6 = reserved6;
	}

	public String getReserved7() {
		return reserved7;
	}

	public void setReserved7(String reserved7) {
		this.reserved7 = reserved7;
	}

	public String getReserved8() {
		return reserved8;
	}

	public void setReserved8(String reserved8) {
		this.reserved8 = reserved8;
	}

	public String getReserved9() {
		return reserved9;
	}

	public void setReserved9(String reserved9) {
		this.reserved9 = reserved9;
	}

	public String getReserved10() {
		return reserved10;
	}

	public void setReserved10(String reserved10) {
		this.reserved10 = reserved10;
	}

	public Boolean getIsSelected() {
		return isSelected;
	}

	public void setIsSelected(Boolean isSelected) {
		this.isSelected = isSelected;
	}

	public Material getMaterial() {
		return material;
	}

	public void setMaterial(Material material) {
		this.material = material;
	}

	public BigDecimal getTransQty() {
		return transQty;
	}

	public void setTransQty(BigDecimal transQty) {
		this.transQty = transQty;
	}

	public Object getAttribute1() {
		return attribute1;
	}

	public void setAttribute1(Object attribute1) {
		this.attribute1 = attribute1;
	}

	public Object getAttribute2() {
		return attribute2;
	}

	public void setAttribute2(Object attribute2) {
		this.attribute2 = attribute2;
	}

	public Object getAttribute3() {
		return attribute3;
	}

	public void setAttribute3(Object attribute3) {
		this.attribute3 = attribute3;
	}

	public Object getAttribute4() {
		return attribute4;
	}

	public void setAttribute4(Object attribute4) {
		this.attribute4 = attribute4;
	}

	public Object getAttribute5() {
		return attribute5;
	}

	public void setAttribute5(Object attribute5) {
		this.attribute5 = attribute5;
	}
	
	public Boolean getIsProduction(){
		return "Y".equalsIgnoreCase(this.isProduction) ? true : false; 
	}

	public void setIsProduction(Boolean isProduction) {
		if(isProduction == null) {
			this.isProduction = "N";
		}else {
			this.isProduction = isProduction ? "Y" : "N";
		}
	}

	@Override
	public int compareTo(AbstractBomLine arg0) {
		return this.getSeqNo().compareTo(arg0.getSeqNo());
	}
	
	public static List<BigDecimal> getChildMaterialQty(BigDecimal parentMaterialQty, Long childMaterialRrn, List<AbstractBomLine> bomLines) {
		List<BigDecimal> qtys = new ArrayList<BigDecimal>();
		BigDecimal totalQty = BigDecimal.ZERO;
		BigDecimal totalMaxQty = BigDecimal.ZERO;

		for (AbstractBomLine bomLine : bomLines) {
			if (bomLine.getMaterialRrn().equals(childMaterialRrn)) {
				totalQty = totalQty.add(bomLine.getUnitQty().multiply(parentMaterialQty));
				BigDecimal fixedQty = BigDecimal.ZERO;
	 	        BigDecimal lossRate = BigDecimal.ZERO;
	 	        if (bomLine.getFixedQty() != null) {
	 	        	fixedQty = bomLine.getFixedQty();
	 	        }
	 	        if (bomLine.getLossRate() != null) {
	 	        	lossRate = bomLine.getLossRate();
	 	        } 
	 	        //最大所需数量 = fixedQty + 数量 * unitQty * (1 + lossRate)
	 	        totalMaxQty = totalMaxQty.add(fixedQty.add(
	 	        		  lossRate.add(BigDecimal.ONE).multiply(bomLine.getUnitQty().multiply(parentMaterialQty))));
			}
		}
		qtys.add(totalQty);
		qtys.add(totalMaxQty);
		
		return qtys;
	}
	
	/**
	 * 根据工步名称获得该工步对应的BOM
	 * 
	 * @param bomLines 产品BOM
	 * @param stepName 工步名称
	 */
	public static List<AbstractBomLine> getStepBomLine(List<AbstractBomLine> bomLines, String stepName) {
		List<AbstractBomLine> stepBomLines = new ArrayList<AbstractBomLine>();
		for (AbstractBomLine bomLine : bomLines) {
			if (stepName.equals(bomLine.getStepName())) {
				stepBomLines.add(bomLine);
			}
		}
		return stepBomLines;
	}
	
	/**
	 * 根据替代组获得改组下所有的可替代料BOM
	 * 
	 * @param bomLines 产品BOM
	 * @param alternateGroup 替代组名
	 */
	public static List<AbstractBomLine> getAlternateBomLine(List<AbstractBomLine> bomLines, String alternateGroup) {
		List<AbstractBomLine> alterBomLines = new ArrayList<AbstractBomLine>();
		if (!StringUtil.isEmpty(alternateGroup)) {
			for (AbstractBomLine bomLine : bomLines) {
				if (alternateGroup.equals(bomLine.getAlternateGroup())) {
					alterBomLines.add(bomLine);
				}
			}
		}
		return alterBomLines;
	}
	
	/**
	 * 根据产品BOM下所有的替代组名称
	 * 
	 * @param bomLines 产品BOM
	 */
	public static List<String> getAlternateGroups(List<AbstractBomLine> bomLines) {
		List<String> alterGroups = new ArrayList<String>();
		for (AbstractBomLine bomLine : bomLines) {
			if (!StringUtil.isEmpty(bomLine.getAlternateGroup()) && !alterGroups.contains(bomLine.getAlternateGroup())) {
				alterGroups.add(bomLine.getAlternateGroup());
			}
		}
		return alterGroups;
	}
	 
    /**
     * 检查物料批是否在BOM中(如果BOM中定义了物料位置,则需要检查批次位置)
     * 
     * @param mLot 待检查的物料批次
     * @param bomLines 对应的BOM
     */
    public static boolean checkMLotByBom(MLot mLot, List<AbstractBomLine> bomLines) {   
		AbstractBomLine lotBomLine = null;
		for (AbstractBomLine bomLine : bomLines) {
			if (mLot.getMaterialName().equals(bomLine.getMaterialName())) {
				lotBomLine = bomLine;
				break;
			}
		}
		if (lotBomLine == null) {
			throw new ClientParameterException("mm.mlot_not_in_bom", mLot.getmLotId());
		} else if (!StringUtil.isEmpty(mLot.getTransPosition()) && !StringUtil.isEmpty(lotBomLine.getPositionName())) {
			//如果BOM中定义了物料位置,则需要检查批次位置
			if (!mLot.getTransPosition().equals(lotBomLine.getPositionName())) {
				throw new ClientParameterException("mm.mlot_not_in_bom_position", mLot.getmLotId(), lotBomLine.getPositionName());
			}
		}
    	return true;
    }
    
    /**
     * 检查BOM中物料是否都存在
     * 如果在BOM中定义了可替代料,还需要检查可替代料
     * 
     * @param bomLines 待检查的BOM
     * @param mLots 待检查的物料批
     */
    public static boolean checkBomByMLot(List<AbstractBomLine> bomLines, List<MLot> mLots) throws ClientException {   
		for (AbstractBomLine bomLine : bomLines) {
			//首先只检查没有替代组的物料
			if (StringUtil.isEmpty(bomLine.getAlternateGroup())) {
				boolean flag = false;
				for (MLot mLot : mLots) {
					if (mLot.getMaterialName().equals(bomLine.getMaterialName())) {
						flag = true;
						break;
					}
				}
				if (!flag) {
					throw new ClientParameterException("mm.bom_material_is_not_found", bomLine.getMaterialName());
				}	
			}
		}
		//获得替代组
		List<String> alternateGroups = AbstractBomLine.getAlternateGroups(bomLines);
		for (String alternateGroup : alternateGroups) {
			List<AbstractBomLine> alternateBomLines = AbstractBomLine.getAlternateBomLine(bomLines, alternateGroup);
			//只要替代组的物料有一个存在就检查通过
			boolean flag = false;
			for (MLot mLot : mLots) {
				for (AbstractBomLine alternateBomLine : alternateBomLines) {
					if (mLot.getMaterialName().equals(alternateBomLine.getMaterialName())) {
						flag = true;
						break;
					}
				}
				if (flag) {
					break;
				}
			}
			if (!flag) {
				throw new ClientParameterException("mm.bom_material_is_not_found", alternateBomLines.get(0).getMaterialName());
			}
		}
    	
    	return true;
    }
    
    /**
     * 检查按Bom消耗的物料数量是否满足, 有替代料时，这里替代策略是100%替代。 
     * 有以下二种情况会检查不准，但应该不会出现：
     * 第一种情况：物料是Bom中的替代料，又是非替代料
     * 第二种情况：物料是多种主物料的替代料
     * @param checkBomLines 检查BomLine
     * @param parentMaterialQty 主物料批数量
     * @param mLots 消耗物料批
     * @return
     * @throws ClientException
     */
    public static boolean checkMLotConsumeQtyByBom(List<AbstractBomLine> checkBomLines, BigDecimal parentMaterialQty, List<MLot> mLots) throws ClientException {   
		List<String> existAlternateGroup = Lists.newArrayList();
		for (AbstractBomLine checkBomLine : checkBomLines) {			
			if (existAlternateGroup.contains(checkBomLine.getAlternateGroup())) {
				continue;
			}
			
			if (checkBomLine.getIsAlternate()) {	
				existAlternateGroup.add(checkBomLine.getAlternateGroup());
				//判断替代料数量是否足够
				List<AbstractBomLine> alternateBomLines = AbstractBomLine.getAlternateBomLine(checkBomLines, checkBomLine.getAlternateGroup());
				Map<Long, AbstractBomLine> maps = alternateBomLines.stream().collect(Collectors.toMap(AbstractBomLine::getMaterialRrn, Function.identity()));
				Long alternateMaterialRrn = checkBomLine.getMaterialRrn();
				for (MLot mLot : mLots) {
					if (maps.containsKey(mLot.getMaterialRrn())) {
						//这里默认100%替代，找到用的哪个替代料
						alternateMaterialRrn = mLot.getMaterialRrn();
						break;
					}
				}
				//算出替代料的需求数量
				List<BigDecimal> alternateMaterialQty = AbstractBomLine.getChildMaterialQty(parentMaterialQty, alternateMaterialRrn, checkBomLines);	
				BigDecimal consumeTotalQty = BigDecimal.ZERO;
				for (MLot mLot : mLots) {
					if (alternateMaterialRrn.equals(mLot.getMaterialRrn())) {
						consumeTotalQty = consumeTotalQty.add(mLot.getTransMainQty());
					}
				}
				if (consumeTotalQty.compareTo(alternateMaterialQty.get(0)) < 0) {
					throw new ClientParameterException("mm.consume_material_shortage", checkBomLine.getMaterialName());
				}
			} else {
				//判断非替代料数量是否足够
				BigDecimal consumeTotalQty = BigDecimal.ZERO;
				for (MLot mLot : mLots) {
					if (checkBomLine.getMaterialName().equals(mLot.getMaterialName())) {
						consumeTotalQty = consumeTotalQty.add(mLot.getTransMainQty());
					}
				}
				List<BigDecimal> materialQty = AbstractBomLine.getChildMaterialQty(parentMaterialQty, checkBomLine.getMaterialRrn(), checkBomLines);
				if (consumeTotalQty.compareTo(materialQty.get(0)) < 0) {
					throw new ClientParameterException("mm.consume_material_shortage", checkBomLine.getMaterialName());
				}
			}
		}				
    	
    	return true;
    }
}
