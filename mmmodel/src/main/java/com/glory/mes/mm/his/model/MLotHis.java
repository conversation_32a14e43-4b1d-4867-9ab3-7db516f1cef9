package com.glory.mes.mm.his.model;

import com.glory.framework.activeentity.model.ADDynamicComponent;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.mes.mm.lot.model.MLot;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class MLotHis extends ADDynamicComponent {

	private static final long serialVersionUID = 1L;

	public static final char HISCOMMENT_SEPERATOR = 31;
	
	public static final String TRANS_TYPE_CREATE = "CREATE"; //新建 
	public static final String TRANS_TYPE_RECEIVE = "RECEIVE"; //接收
	public static final String TRANS_TYPE_REJECT = "REJECT"; //拒绝
	
	public static final String TRANS_TYPE_READYTOIN = "READYTOIN"; //请求入库
	public static final String TRANS_TYPE_READYTOOUT = "READYTOOUT"; //请求出库
	public static final String TRANS_TYPE_CANCELREADYTOIN = "CANCELREADYTOIN"; //取消请求入库
	public static final String TRANS_TYPE_CANCELREADYTOOUT = "CANCELREADYTOOUT"; //取消请求出库
	public static final String TRANS_TYPE_IQC = "IQC"; //入库检验
	public static final String TRANS_TYPE_IN = "IN"; //入库
	public static final String TRANS_TYPE_OUT = "OUT"; //出库
	public static final String TRANS_TYPE_RETURN = "RETURN"; //退回
    public static final String TRANS_TYPE_ONSHELF = "ONSHELF"; //上架
    public static final String TRANS_TYPE_OFFSHELF = "OFFSHELF"; //下架
	
	public static final String TRANS_TYPE_TRANSFER = "TRANSFER"; //移库
	public static final String TRANS_TYPE_READYLOT = "READYLOT"; //备货
	
	public static final String TRANS_TYPE_RESERVE = "RESERVE"; //预留
	public static final String TRANS_TYPE_UNRESERVE = "UNRESERVE"; //取消预留
	public static final String TRANS_TYPE_COMSUME = "COMSUME"; //消耗
	public static final String TRANS_TYPE_UNCOMSUME = "UNCOMSUME"; //反消耗
	public static final String TRANS_TYPE_ATTACH = "ATTACH"; //绑定
	public static final String TRANS_TYPE_DETACH = "DETACH"; //解绑
	public static final String TRANS_TYPE_ATTACHIN = "ATTACHIN"; //SubMLot绑定MainMLot历史
	public static final String TRANS_TYPE_ATTACHLOT = "ATTACHLOT"; //MainMLot绑定SubMLot历史
	public static final String TRANS_TYPE_DETACHOUT = "DETACHOUT"; //SubMLot解绑MainMLot历史
	public static final String TRANS_TYPE_DETACHLOT = "DETACHLOT"; //MainMLot解绑SubMLot历史
	public static final String TRANS_TYPE_CONVERTMLOT = "CONVERTMLOT"; //WLot转MLot
	public static final String TRANS_TYPE_CONVERTLOT = "CONVERTLOT"; //MLot转MLot
	
	public static final String TRANS_TYPE_SPLIT = "SPLIT"; //主批次分批历史
	public static final String TRANS_TYPE_SPLITOUT = "SPLITOUT"; //子批次分批历史
	public static final String TRANS_TYPE_MERGE = "MERGE";
	public static final String TRANS_TYPE_MERGEIN = "MERGEIN";
	public static final String TRANS_TYPE_SCRAP = "SCRAP"; //报废
	public static final String TRANS_TYPE_UNSCRAP = "UNSCRAP"; //反报废
	public static final String TRANS_TYPE_HELD = "HOLD"; //暂停
	public static final String TRANS_TYPE_RELEASE = "RELEASE"; //放行
	public static final String TRANS_TYPE_CHANGEQTY = "CHANGEQTY"; //修改数量	
	public static final String TRANS_TYPE_CHANGELOTID = "CHANGELOTID"; //修改批次ID
	
	public static final String TRANS_TYPE_STOCKTAKING = "STOCKTAKING"; //盘点
	
	public static final String TRANS_CHANGELOT = "CHANGELOT";
	public static final String TRANS_CHANGECOMP = "CHANGECOMP";
	public static final String TRANS_EDC = "EDC";
	
	public static final String TRANS_TYPE_PKG = "PKG"; //包装
	public static final String TRANS_TYPE_UNPKG = "UNPKG"; //拆包
	
	public static final String TRANS_TYPE_CHANGEEXPIRE = "CHANGEEXPIRE"; //修改物料有效期

    public static final String TRANS_TYPE_MLOTINDURABLE = "MLOINDURABLE"; // 批次放入载具
    public static final String TRANS_TYPE_MLOTOUTDURABLE = "MLOTOUTDURABLE"; // 批次从载具拿出
    public static final String TRANS_TYPE_MLOTCHANGEDURABLE = "MLOTCHANGEDURABLE"; // 批次从一载具转移至另一载具

	public static final String TRANS_ATTACHLOT = "ATTACHLOT";
	public static final String TRANS_DETACHLOT = "DETACHLOT";

	private String hisSeq;
	
	private Long hisSeqNo;

	private String transType;
	
	private Long mLotRrn;
	
	private String mLotId;
	
	private String mLotType;
	
	private String mainMatType;

	private String subMatType;
	
	private String comClass;
	
	private String state;
	
	private String holdState;
	
	private String transferState;
	
	private String woId;
	
	private BigDecimal mainQty;
	
	private BigDecimal subQty;
	
	private String grade1;
	
	private String grade2;
		
	private String materialClazz;
	
	private Long materialRrn;
	
	private String materialName;

	private Long materialVersion;
	
	private String materialDesc;
	
	private String materialType;

	private String uomId;
	
	private String partnerCode;

	private String partnerOrder;
	
	private String partnerMaterialId;
	
	private String partnerLotId;
	
	private String rootMLotId;
	
	private String singleMLotId;
	
	/**
	 * 父批的ObjectRrn
	 */
	private Long parentMLotRrn;
	
	private String isSubMLot = "N";
	
	private String stepName;
	
	private Long stepVersion;

	private String equipmentId;
	
	private String position;
	
	private String lotId; 
	
	private String componentId; 

	private Date dateCode;
	
	private Date inDate;
	
	private Date outDate;
	
	private String batchType;
	
	/**
	 * 是否时间敏感物料
	 */
	private String isTimeSensitive = "N";

	/**
	 * 保质期失效时间
	 */
	private Date shelfWarningExpire;	

	/**
	 * 保质期失效时间
	 */
	private Date shelfLifeExpire;	

	/**
	 * 使用有效期失效时间
	 */
	private Date floorLifeExpire;
	
	/**
	 * 次数有效期
	 */
	private Long currentCount;
	
	/**
	 * 次数有效期
	 */
	private Long limitWarning;
	
	private Long limitLife;
	
	private String durable;
	
	private String slotId;
	
	private String owner;
	
	private String lotComment;

	private BigDecimal transMainQty;
	
	private BigDecimal transSubQty;
	
	private String transWarehouseId;
	
	private String transStorageType;

	private String transStorageId;
		
	private String transTargetWarehouseId;
	
	private String transTargetStorageType;

	private String transTargetStorageId;
	
	private String refDocType;
		
	private String refDocId;
	
	private String actionCode;
	
	private String actionReason;
	
	private String actionComment;
	
	private String operator;
	
	private String hisComment;
	
	private String reserved1;

	private String reserved2;

	private String reserved3;

	private String reserved4;

	private String reserved5;
	
	private String reserved6;
	
	private String reserved7;
	
	private String reserved8;
		
	public MLotHis() {}
	
	public MLotHis(MLot mLot, SessionContext sc) {
		PropertyUtil.copyProperties(this, mLot);
		this.setObjectRrn(null);
		this.setCreated(new Date());
		this.setCreatedBy(sc.getUserName());
		this.setUpdated(new Date());
		this.setUpdatedBy(sc.getUserName());
		this.setLockVersion(null);
		this.setmLotRrn(mLot.getObjectRrn());

		this.setHisSeq(sc.getTransRrn());
		this.setHisSeqNo((long)sc.getTransSeqNo());
		this.setOperator(sc.getUserName());
		
		if (mLot.getUdf() != null) {
			Map udf = new HashMap();
			udf.putAll(mLot.getUdf());
			this.setUdf(udf);
		}
	}

	public String getHisSeq() {
		return hisSeq;
	}

	public void setHisSeq(String hisSeq) {
		this.hisSeq = hisSeq;
	}

	public Long getHisSeqNo() {
		return hisSeqNo;
	}

	public void setHisSeqNo(Long hisSeqNo) {
		this.hisSeqNo = hisSeqNo;
	}

	public String getTransType() {
		return transType;
	}

	public void setTransType(String transType) {
		this.transType = transType;
	}

    public Long getmLotRrn() {
        return mLotRrn;
    }

    public void setmLotRrn(Long mLotRrn) {
        this.mLotRrn = mLotRrn;
    }

    public String getmLotId() {
        return mLotId;
    }

    public void setmLotId(String mLotId) {
        this.mLotId = mLotId;
    }

    public String getmLotType() {
        return mLotType;
    }

    public void setmLotType(String mLotType) {
        this.mLotType = mLotType;
    }
    
	public String getMainMatType() {
		return mainMatType;
	}

	public void setMainMatType(String mainMatType) {
		this.mainMatType = mainMatType;
	}

	public String getSubMatType() {
		return subMatType;
	}

	public void setSubMatType(String subMatType) {
		this.subMatType = subMatType;
	}

	public String getHoldState() {
		return holdState;
	}

	public void setHoldState(String holdState) {
		this.holdState = holdState;
	}
	
	public String getComClass() {
		return comClass;
	}

	public void setComClass(String comClass) {
		this.comClass = comClass;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getTransferState() {
		return transferState;
	}

	public void setTransferState(String transferState) {
		this.transferState = transferState;
	}
	
	public BigDecimal getMainQty() {
		return mainQty;
	}

	public void setMainQty(BigDecimal mainQty) {
		this.mainQty = mainQty;
	}

	public BigDecimal getSubQty() {
		return subQty;
	}

	public void setSubQty(BigDecimal subQty) {
		this.subQty = subQty;
	}
	
	public String getGrade1() {
		return grade1;
	}

	public void setGrade1(String grade1) {
		this.grade1 = grade1;
	}

	public String getGrade2() {
		return grade2;
	}

	public void setGrade2(String grade2) {
		this.grade2 = grade2;
	}

    public String getRootMLotId() {
        return rootMLotId;
    }

    public void setRootMLotId(String rootMLotId) {
        this.rootMLotId = rootMLotId;
    }
    
	public String getSingleMLotId() {
		return singleMLotId;
	}

	public void setSingleMLotId(String singleMLotId) {
		this.singleMLotId = singleMLotId;
	}

	public Long getParentMLotRrn() {
		return parentMLotRrn;
	}

	public void setParentMLotRrn(Long parentMLotRrn) {
		this.parentMLotRrn = parentMLotRrn;
	}

	public Boolean getIsSubMLot(){
		return "Y".equalsIgnoreCase(this.isSubMLot) ? true : false; 
	}

	public void setIsSubMLot(Boolean isSubMLot) {
		if(isSubMLot != null) {
			this.isSubMLot = isSubMLot ? "Y" : "N";
		}else {
			this.isSubMLot = "N";
		}
	}
	
	public String getWoId() {
		return woId;
	}

	public void setWoId(String woId) {
		this.woId = woId;
	}

	public String getMaterialClazz() {
		return materialClazz;
	}

	public void setMaterialClazz(String materialClazz) {
		this.materialClazz = materialClazz;
	}

	public Long getMaterialRrn() {
		return materialRrn;
	}

	public void setMaterialRrn(Long materialRrn) {
		this.materialRrn = materialRrn;
	}

	public String getMaterialName() {
		return materialName;
	}

	public void setMaterialName(String materialName) {
		this.materialName = materialName;
	}

	public Long getMaterialVersion() {
		return materialVersion;
	}

	public void setMaterialVersion(Long materialVersion) {
		this.materialVersion = materialVersion;
	}

	public String getMaterialDesc() {
		return materialDesc;
	}

	public void setMaterialDesc(String materialDesc) {
		this.materialDesc = materialDesc;
	}

	public String getMaterialType() {
		return materialType;
	}

	public void setMaterialType(String materialType) {
		this.materialType = materialType;
	}

    public String getUomId() {
		return uomId;
	}

	public void setUomId(String uomId) {
		this.uomId = uomId;
	}

	public String getPartnerCode() {
		return partnerCode;
	}

	public void setPartnerCode(String partnerCode) {
		this.partnerCode = partnerCode;
	}

	public String getPartnerOrder() {
		return partnerOrder;
	}

	public void setPartnerOrder(String partnerOrder) {
		this.partnerOrder = partnerOrder;
	}

	public String getPartnerMaterialId() {
		return partnerMaterialId;
	}

	public void setPartnerMaterialId(String partnerMaterialId) {
		this.partnerMaterialId = partnerMaterialId;
	}

	public String getPartnerLotId() {
		return partnerLotId;
	}

	public void setPartnerLotId(String partnerLotId) {
		this.partnerLotId = partnerLotId;
	}
	
	public Date getDateCode() {
		return dateCode;
	}

	public void setDateCode(Date dateCode) {
		this.dateCode = dateCode;
	}
	
	public Date getInDate() {
		return inDate;
	}

	public void setInDate(Date inDate) {
		this.inDate = inDate;
	}
	
	public Date getOutDate() {
		return outDate;
	}

	public void setOutDate(Date outDate) {
		this.outDate = outDate;
	}

	public String getBatchType() {
		return batchType;
	}

	public void setBatchType(String batchType) {
		this.batchType = batchType;
	}
	
	public Boolean getIsTimeSensitive() {
		return "Y".equalsIgnoreCase(this.isTimeSensitive) ? true : false; 
	}

	public void setIsTimeSensitive(Boolean isTimeSensitive) {
		if(isTimeSensitive != null) {
			this.isTimeSensitive = isTimeSensitive ? "Y" : "N";
		}else {
			this.isTimeSensitive = "N";
		}
	}
	
	public Date getShelfWarningExpire() {
		return shelfWarningExpire;
	}

	public void setShelfWarningExpire(Date shelfWarningExpire) {
		this.shelfWarningExpire = shelfWarningExpire;
	}
	
	public Date getShelfLifeExpire() {
		return shelfLifeExpire;
	}

	public void setShelfLifeExpire(Date shelfLifeExpire) {
		this.shelfLifeExpire = shelfLifeExpire;
	}

	public Date getFloorLifeExpire() {
		return floorLifeExpire;
	}

	public void setFloorLifeExpire(Date floorLifeExpire) {
		this.floorLifeExpire = floorLifeExpire;
	}

	public Long getCurrentCount() {
		return currentCount;
	}

	public void setCurrentCount(Long currentCount) {
		this.currentCount = currentCount;
	}

	public Long getLimitWarning() {
		return limitWarning;
	}

	public void setLimitWarning(Long limitWarning) {
		this.limitWarning = limitWarning;
	}

	public Long getLimitLife() {
		return limitLife;
	}

	public void setLimitLife(Long limitLife) {
		this.limitLife = limitLife;
	}
	
	public String getDurable() {
		return durable;
	}

	public void setDurable(String durable) {
		this.durable = durable;
	}
	
	public String getSlotId() {
		return slotId;
	}

	public void setSlotId(String slotId) {
		this.slotId = slotId;
	}

	public String getOwner() {
		return owner;
	}

	public void setOwner(String owner) {
		this.owner = owner;
	}
	
	public String getLotComment() {
		return lotComment;
	}

	public void setLotComment(String lotComment) {
		this.lotComment = lotComment;
	}
	
	public BigDecimal getTransMainQty() {
		return transMainQty;
	}

	public void setTransMainQty(BigDecimal transMainQty) {
		this.transMainQty = transMainQty;
	}

	public BigDecimal getTransSubQty() {
		return transSubQty;
	}

	public void setTransSubQty(BigDecimal transSubQty) {
		this.transSubQty = transSubQty;
	}
	
	public String getTransWarehouseId() {
		return transWarehouseId;
	}

	public void setTransWarehouseId(String transWarehouseId) {
		this.transWarehouseId = transWarehouseId;
	}

	public String getTransStorageType() {
		return transStorageType;
	}

	public void setTransStorageType(String transStorageType) {
		this.transStorageType = transStorageType;
	}

	public String getTransStorageId() {
		return transStorageId;
	}

	public void setTransStorageId(String transStorageId) {
		this.transStorageId = transStorageId;
	}

	public String getTransTargetWarehouseId() {
		return transTargetWarehouseId;
	}

	public void setTransTargetWarehouseId(String transTargetWarehouseId) {
		this.transTargetWarehouseId = transTargetWarehouseId;
	}

	public String getTransTargetStorageType() {
		return transTargetStorageType;
	}

	public void setTransTargetStorageType(String transTargetStorageType) {
		this.transTargetStorageType = transTargetStorageType;
	}

	public String getTransTargetStorageId() {
		return transTargetStorageId;
	}

	public void setTransTargetStorageId(String transTargetStorageId) {
		this.transTargetStorageId = transTargetStorageId;
	}

	public String getRefDocType() {
		return refDocType;
	}

	public void setRefDocType(String refDocType) {
		this.refDocType = refDocType;
	}

	public String getRefDocId() {
		return refDocId;
	}

	public void setRefDocId(String refDocId) {
		this.refDocId = refDocId;
	}

	public String getStepName() {
		return stepName;
	}

	public void setStepName(String stepName) {
		this.stepName = stepName;
	}

	public Long getStepVersion() {
		return stepVersion;
	}

	public void setStepVersion(Long stepVersion) {
		this.stepVersion = stepVersion;
	}

	public String getEquipmentId() {
		return equipmentId;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	public String getLotId() {
		return lotId;
	}

	public void setLotId(String lotId) {
		this.lotId = lotId;
	}

	public String getComponentId() {
		return componentId;
	}

	public void setComponentId(String componentId) {
		this.componentId = componentId;
	}
	
	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}
	
	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}
	
	public String getActionComment() {
		return actionComment;
	}

	public void setActionComment(String actionComment) {
		this.actionComment = actionComment;
	}

	public String getHisComment() {
		return hisComment;
	}

	public void setHisComment(String hisComment) {
		this.hisComment = hisComment;
	}
	
	public String getActionCode() {
		return actionCode;
	}

	public void setActionCode(String actionCode) {
		this.actionCode = actionCode;
	}

	public String getActionReason() {
		return actionReason;
	}

	public void setActionReason(String actionReason) {
		this.actionReason = actionReason;
	}

	public String getReserved1() {
		return reserved1;
	}

	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}

	public String getReserved2() {
		return reserved2;
	}

	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}

	public String getReserved3() {
		return reserved3;
	}

	public void setReserved3(String reserved3) {
		this.reserved3 = reserved3;
	}

	public String getReserved4() {
		return reserved4;
	}

	public void setReserved4(String reserved4) {
		this.reserved4 = reserved4;
	}

	public String getReserved5() {
		return reserved5;
	}

	public void setReserved5(String reserved5) {
		this.reserved5 = reserved5;
	}

	public String getReserved6() {
		return reserved6;
	}

	public void setReserved6(String reserved6) {
		this.reserved6 = reserved6;
	}

	public String getReserved7() {
		return reserved7;
	}

	public void setReserved7(String reserved7) {
		this.reserved7 = reserved7;
	}

	public String getReserved8() {
		return reserved8;
	}

	public void setReserved8(String reserved8) {
		this.reserved8 = reserved8;
	}

}
