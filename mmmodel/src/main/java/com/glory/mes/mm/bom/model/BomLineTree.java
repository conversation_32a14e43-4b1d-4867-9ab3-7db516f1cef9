package com.glory.mes.mm.bom.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Transient;

import com.glory.framework.activeentity.model.ADBase;

/**
 * 用于在需要用树形结构记录bom信息
 */
public class BomLineTree extends AbstractBomLine {

	private static final long serialVersionUID = 1L;
	
	public BomLineTree() {
		
	}
	
	public BomLineTree(AbstractBomLine line) {
		this.setObjectRrn(line.getObjectRrn());
		this.setOrgRrn(line.getOrgRrn());
		this.setBomRrn(line.getBomRrn());
		this.setSeqNo(line.getSeqNo());
		
		this.setStepName(line.getStepName());
		this.setStepVersion(line.getStepVersion());
		
		this.setItemCategory(line.getItemCategory());
		this.setMaterialRrn(line.getMaterialRrn());
		this.setMaterialName(line.getMaterialName());
		this.setMaterialVersion(line.getMaterialVersion());
		this.setMaterialDesc(line.getMaterialDesc());
		this.setMaterialType(line.getMaterialType());
		this.setUomId(line.getUomId());
		
		this.setUnitQty(line.getUnitQty());
		
		this.setIsOptional(line.getIsOptional());	
		this.setIsCritical(line.getIsCritical());
		this.setFlushType(line.getFlushType());	
		this.setIsAssembly(line.getIsAssembly());		
		this.setIsMain(line.getIsMain());		
		this.setIsQtyPercent(line.getIsQtyPercent());
		this.setIsSelected(line.getIsSelected());		
		this.setFixedQty(line.getFixedQty());
		this.setLossRate(line.getLossRate());
				
		this.setIsAlternate(line.getIsAlternate());
		this.setAlternateGroup(line.getAlternateGroup());
		this.setAlternatePercent(line.getAlternatePercent());
		this.setAlternatePriority(line.getAlternatePriority());
		this.setAlternateStrategy(line.getAlternateStrategy());
		this.setBatchQty(line.getBatchQty());
		this.setIsProduction(line.getIsProduction());
		
		this.setComments(line.getComments());
		
		this.setReserved1(line.getReserved1());
		this.setReserved2(line.getReserved2());
		this.setReserved3(line.getReserved3());
		this.setReserved4(line.getReserved4());
		this.setReserved5(line.getReserved5());
		
		this.setReserved6(line.getReserved6());
		this.setReserved7(line.getReserved7());
		this.setReserved8(line.getReserved8());
		this.setReserved9(line.getReserved9());
		this.setReserved10(line.getReserved10());
	}
	
	
	/**
	 * 记录bom路径
	 * 第一层为materialName/
	 * 第二层为materialName/materialName/
	 */
	@Transient
	private String path;
	
	@Transient
	private String stepPath;
	
	/**
	 * 工步描述
	 */
	@Transient
	private String stepDesc;

	/**
	 * 记录bom层次,第一层为1
	 */
	@Transient
	private Long pathLevel;
	
	/**
	 * 实际路径, 与path的区别在于使用虚拟料时
	 * realPath包含了虚拟料时
	 */
	@Transient
	private String realPath;
	
	/**
	 * 与pathLevel的区别在于使用虚拟料时
	 * realPathLevel包含了虚拟料时
	 */
	@Transient
	private Long realPathLevel;

	/**
	 * 对应树的根节点的单位用量
	 */
	@Transient
	private BigDecimal treeUnitQty;
	

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getStepPath() {
		return stepPath;
	}

	public void setStepPath(String stepPath) {
		this.stepPath = stepPath;
	}
	
	public String getStepDesc() {
		return stepDesc;
	}

	public void setStepDesc(String stepDesc) {
		this.stepDesc = stepDesc;
	}
	
	public Long getPathLevel() {
		return pathLevel;
	}

	public void setPathLevel(Long pathLevel) {
		this.pathLevel = pathLevel;
	}
	
	public String getRealPath() {
		return realPath;
	}

	public void setRealPath(String realPath) {
		this.realPath = realPath;
	}

	public Long getRealPathLevel() {
		return realPathLevel;
	}

	public void setRealPathLevel(Long realPathLevel) {
		this.realPathLevel = realPathLevel;
	}
	
	public BigDecimal getTreeUnitQty() {
		return treeUnitQty;
	}

	public void setTreeUnitQty(BigDecimal treeUnitQty) {
		this.treeUnitQty = treeUnitQty;
	}

	public boolean equals(Object obj) {
		if (obj instanceof BomLineTree) {
			BomLineTree objTree = (BomLineTree)obj;
			if (getPath() != null) {
				if (getPath().equals(objTree.getPath())) {
					return super.equals(objTree);
				}
			}
		}
		return false;
	}

}
