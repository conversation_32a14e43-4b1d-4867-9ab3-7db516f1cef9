package com.glory.mes.mm.his.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.glory.framework.activeentity.model.ADDynamicComponent;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.mm.durable.model.Durable;
import com.glory.mes.mm.durable.model.DurableSpec;

public abstract class DurableHis extends ADDynamicComponent {	
	
	private static final long serialVersionUID = 1L;
	
	public static final String TRANSTYPE_USE = "USE";
	public static final String TRANSTYPE_CLEAN = "CLEAN";
	public static final String TRANSTYPE_DIRTY = "DIRTY";
	public static final String TRANSTYPE_HOLD = "HOLD";
	public static final String TRANSTYPE_RELEASE = "RELEASE";
	public static final String TRANSTYPE_SCRAP = "SCRAP";
	public static final String TRANSTYPE_UNSCRAP = "UNSCRAP";	
	public static final String TRANSTYPE_CHANGESTATE = "CHANGESTATE";
	public static final String TRANSTYPE_CHANGETRANSFERSTATE = "CHANGETRANSFERSTATE";
	public static final String TRANSTYPE_CHANGESPEC = "CHANGESPEC";
	public static final String TRANSTYPE_CHANGEINFO = "CHANGEINFO";
	public static final String TRANSTYPE_ATTACH = "ATTACH";
	public static final String TRANSTYPE_DETACH = "DETACH";
	public static final String TRANSTYPE_CLEAN_START = "CLEANSTART";
	public static final String TRANSTYPE_OUT = "OUT";
	public static final String TRANSTYPE_RETURN = "RETURN";
	public static final String TRANSTYPE_CHANGEDISPATCH = "CHANGEDISPATCH";
	public static final String TRANSTYPE_RECEIVE = "RECEIVE";
	
	public DurableHis() {}
	
	public DurableHis(Durable durable, SessionContext sc) {
		this.setOrgRrn(durable.getOrgRrn());
		this.setIsActive(durable.getIsActive());
		this.setUpdatedBy(sc.getUserName());
		
		this.setTransTime(sc.getTransTime());
		this.setHistorySeq(sc.getTransRrn());
		this.setHistorySeqNo(sc.getTransSeqNo());
				
		this.durableRrn = durable.getObjectRrn();
		this.durableId = durable.getDurableId();
		
		this.durableSpecName = durable.getDurableSpecName();
		this.durableSpecVersion = durable.getDurableSpecVersion();
		
		this.category = durable.getCategory();
		this.durableType = durable.getDurableType();
		
		this.mainMatType = durable.getMainMatType();
		this.subMatType = durable.getSubMatType();
		
		this.capacity = durable.getCapacity();
		this.slotDirection = durable.getSlotDirection();
		this.currentQty = durable.getCurrentQty();
		
		this.warningCount = durable.getWarningCount();
		this.limitCount = durable.getLimitCount();
		this.currentCount = durable.getCurrentCount();
		
		this.warningTime = durable.getWarningTime();
		this.limitTime = durable.getLimitTime();
		this.limitTimeUnit = durable.getLimitTimeUnit();
		
		this.cleanState = durable.getCleanState();
		this.cleanDate = durable.getCleanDate();
		
		this.warehouseId = durable.getWarehouseId();
		this.locatorId = durable.getLocatorId();
		this.line = durable.getLine();
		this.location = durable.getLocation();
		this.portId = durable.getPortId();
		this.equipmentId = durable.getEquipmentId();

		this.statusModelRrn = durable.getStatusModelRrn();
		this.comClass = durable.getComClass();
		this.state = durable.getState();
		this.subState = durable.getSubState();
		this.transferState = durable.getTransferState();
		this.holdState = durable.getHoldState();
		
		this.comments = durable.getComments();
		
		this.actionCode = durable.getActionCode();
		this.actionReason = durable.getActionReason();
		this.actionComment = durable.getActionComment();
		
		if (durable.getUdf() != null) {
			Map udf = new HashMap();
			udf.putAll(durable.getUdf());
			this.setUdf(udf);
		}
	}
	
	private String transType;
	
	private Date transTime;
	
	private String historySeq;
	
	private Long historySeqNo;
	
	private String eventId;
	
	private Long durableRrn;
	
	private String durableId;
	
	private String durableType;
	
	private String durableSpecName;
	
	private Long durableSpecVersion;
	
	private String category;
	
	private String mainMatType;
	
	private String subMatType;
	
	/**
	 * 容量
	 */
	private BigDecimal capacity;
	
	/**
	 * Slot号排列方式
	 */
	private String slotDirection;
	
	/**
	 * 当前数量
	 */
	private BigDecimal currentQty = BigDecimal.ZERO;
	
	private Long warningCount;
	
	/**
	 * 最大使用次数
	 */
	private Long limitCount;
	
	private Long warningTime;
	
	/**
	 * 最大使用时间
	 */
	private Long limitTime;
	
	private String limitTimeUnit;
	
	/**
	 * 当前使用次数
	 */
	private Long currentCount;
	
	private String cleanState;
	
	private Date cleanDate;
	
	/**
	 * 当前仓库(当Durable在仓库时使用)
	 */
	private String warehouseId;
	
	/**
	 * 当前库位(当Durable在仓库时使用)
	 */
	private String locatorId;
	
	/**
	 * 位置(当Durable不在仓库时使用)
	 */
	private String location;
	
	/**
	 * 所在线别
	 */
	private String line;
	
	private String equipmentId;

	private String portId;
	
	private String lotId;
	
	private String carrierId;
	
	private String position;

	/**
	 * 设备状态模型
	 */
	private Long statusModelRrn;

	/**
	 * 当前状态
	 */
	private String comClass;

	private String state;
	
	private String subState;
	
	private String transferState;
	
	private String holdState;
	
	private String actionCode;
	
	private String actionReason;
	
	private String actionComment;
		
	private String hisComment;
	
	private String comments;
	
	/**
	 * 是否是要跨厂的载具(在跨厂搬送时使用,Y表示载具要跨厂,在搬送完成后改为N)
	 */
	private String isChangeOrg;
	
	/**
	 * 入库时间(载具到达设备/Buffer的时间)
	 */
	private Date arrivalDate;
	
	private String isDispatch = "N";

	public String getTransType() {
		return transType;
	}

	public void setTransType(String transType) {
		this.transType = transType;
	}
	
	public String getEventId() {
		return eventId;
	}

	public void setEventId(String eventId) {
		this.eventId = eventId;
	}

	public Long getDurableRrn() {
		return durableRrn;
	}

	public void setDurableRrn(Long durableRrn) {
		this.durableRrn = durableRrn;
	}
	
	public String getDurableId() {
		return durableId;
	}

	public void setDurableId(String durableId) {
		this.durableId = durableId;
	}

	public String getDurableType() {
		return durableType;
	}

	public void setDurableType(String durableType) {
		this.durableType = durableType;
	}

	public String getDurableSpecName() {
		return durableSpecName;
	}

	public void setDurableSpecName(String durableSpecName) {
		this.durableSpecName = durableSpecName;
	}

	public Long getDurableSpecVersion() {
		return durableSpecVersion;
	}

	public void setDurableSpecVersion(Long durableSpecVersion) {
		this.durableSpecVersion = durableSpecVersion;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}
	
	public Long getLimitCount() {
		return limitCount;
	}

	public void setLimitCount(Long limitCount) {
		this.limitCount = limitCount;
	}

	public Long getLimitTime() {
		return limitTime;
	}

	public void setLimitTime(Long limitTime) {
		this.limitTime = limitTime;
	}

	public Long getCurrentCount() {
		return currentCount;
	}

	public void setCurrentCount(Long currentCount) {
		this.currentCount = currentCount;
	}
	
	public BigDecimal getCapacity() {
		return capacity;
	}

	public void setCapacity(BigDecimal capacity) {
		this.capacity = capacity;
	}

	public String getSlotDirection() { 
		return slotDirection;
	}

	public void setSlotDirection(String slotDirection) {
		this.slotDirection = slotDirection;
	}

	public BigDecimal getCurrentQty() {
		return currentQty;
	}

	public void setCurrentQty(BigDecimal currentQty) {
		this.currentQty = currentQty;
	}

	public String getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(String warehouseId) {
		this.warehouseId = warehouseId;
	}

	public String getLocatorId() {
		return locatorId;
	}

	public void setLocatorId(String locatorId) {
		this.locatorId = locatorId;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public String getLine() {
		return line;
	}

	public void setLine(String line) {
		this.line = line;
	}

	public Long getStatusModelRrn() {
		return statusModelRrn;
	}

	public void setStatusModelRrn(Long statusModelRrn) {
		this.statusModelRrn = statusModelRrn;
	}

	public String getComClass() {
		return comClass;
	}

	public void setComClass(String comClass) {
		this.comClass = comClass;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getSubState() {
		return subState;
	}

	public void setSubState(String subState) {
		this.subState = subState;
	}

	public String getTransferState() {
		return transferState;
	}

	public void setTransferState(String transferState) {
		this.transferState = transferState;
	}

	public String getHoldState() {
		return holdState;
	}

	public void setHoldState(String holdState) {
		this.holdState = holdState;
	}

	public String getActionCode() {
		return actionCode;
	}

	public void setActionCode(String actionCode) {
		this.actionCode = actionCode;
	}

	public String getActionReason() {
		return actionReason;
	}

	public void setActionReason(String actionReason) {
		this.actionReason = actionReason;
	}

	public String getActionComment() {
		return actionComment;
	}

	public void setActionComment(String actionComment) {
		this.actionComment = actionComment;
	}
	
	public String getHisComment() {
		return hisComment;
	}

	public void setHisComment(String hisComment) {
		this.hisComment = hisComment;
	}

	public String getMainMatType() {
		return mainMatType;
	}

	public void setMainMatType(String mainMatType) {
		this.mainMatType = mainMatType;
	}

	public String getSubMatType() {
		return subMatType;
	}

	public void setSubMatType(String subMatType) {
		this.subMatType = subMatType;
	}

	public Date getTransTime() {
		return transTime;
	}

	public void setTransTime(Date transTime) {
		this.transTime = transTime;
	}

	public String getHistorySeq() {
		return historySeq;
	}

	public void setHistorySeq(String historySeq) {
		this.historySeq = historySeq;
	}

	public Long getHistorySeqNo() {
		return historySeqNo;
	}

	public void setHistorySeqNo(Long historySeqNo) {
		this.historySeqNo = historySeqNo;
	}

	public Long getWarningCount() {
		return warningCount;
	}

	public void setWarningCount(Long warningCount) {
		this.warningCount = warningCount;
	}

	public Long getWarningTime() {
		return warningTime;
	}

	public void setWarningTime(Long warningTime) {
		this.warningTime = warningTime;
	}

	public String getLimitTimeUnit() {
		return limitTimeUnit;
	}

	public void setLimitTimeUnit(String limitTimeUnit) {
		this.limitTimeUnit = limitTimeUnit;
	}

	public String getCleanState() {
		return cleanState;
	}

	public void setCleanState(String cleanState) {
		this.cleanState = cleanState;
	}

	public Date getCleanDate() {
		return cleanDate;
	}

	public void setCleanDate(Date cleanDate) {
		this.cleanDate = cleanDate;
	}

	public String getPortId() {
		return portId;
	}

	public void setPortId(String portId) {
		this.portId = portId;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}
	
	public String getEquipmentId() {
		return equipmentId;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	public String getLotId() {
		return lotId;
	}

	public void setLotId(String lotId) {
		this.lotId = lotId;
	}
	
	public String getCarrierId() {
		return carrierId;
	}

	public void setCarrierId(String carrierId) {
		this.carrierId = carrierId;
	}
	
	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public String getIsChangeOrg() {
		return isChangeOrg;
	}

	public void setIsChangeOrg(String isChangeOrg) {
		this.isChangeOrg = isChangeOrg;
	}
	
	public void setIsChangeOrg(boolean isChangeOrg) {
		this.isChangeOrg = isChangeOrg ? "Y" : "N";
	}

	public Date getArrivalDate() {
		return arrivalDate;
	}

	public void setArrivalDate(Date arrivalDate) {
		this.arrivalDate = arrivalDate;
	}
	
	public Boolean getIsDispatch() {
		return "Y".equalsIgnoreCase(this.isDispatch) ? true : false;
	}

	public void setIsDispatch(Boolean isDispatch) {
		if(isDispatch != null) {
			this.isDispatch = isDispatch ? "Y" : "N";
		}else {
			this.isDispatch = "N";
		}
	}
	
}
