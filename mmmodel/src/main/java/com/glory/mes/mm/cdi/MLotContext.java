package com.glory.mes.mm.cdi;

import java.io.Serializable;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.glory.framework.core.cdi.ICdiAction;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.mm.lot.model.MLot;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

public class MLotContext implements Serializable {
	
	private static final long serialVersionUID = 1L;
	    
    /**
     * 事务类型
     */
	private String transType;

	/**
     * 对应的批次
     */
	private MLot mLot;
	
	private SessionContext sessionContext;
	
	private Map<String, Boolean> actionFlagMap = Maps.newHashMap();

	public Map<String, Boolean> getActionFlagMap() {
		return actionFlagMap;
	}

	public void setActionFlagMap(Map<String, Boolean> actionFlagMap) {
		this.actionFlagMap = actionFlagMap;
	}
	
	public MLotContext() {
	}
	
	public MLotContext(MLot mLot, SessionContext sc) {
		this.mLot = mLot;
		this.sessionContext = sc;
	}

	public String getTransType() {
		return transType;
	}

	public void setTransType(String transType) {
		this.transType = transType;
	}

	public MLot getmLot() {
		return mLot;
	}

	public void setmLot(MLot mLot) {
		this.mLot = mLot;
	}
	
	public SessionContext getSessionContext() {
		return sessionContext;
	}

	public void setSessionContext(SessionContext sessionContext) {
		this.sessionContext = sessionContext;
	}
	
	public boolean isActionEnable(String actionName, boolean defaultValue) {
		if (actionFlagMap.containsKey(actionName)) {
			return actionFlagMap.get(actionName);
		}
		//如果未定义则为默认值
		return defaultValue;
	}

	public static List<IMLotCdiAction> getActions(Iterator<IMLotCdiAction> actionIter, String cdiPoint, String triggerPoint) {
		if (StringUtil.isEmpty(cdiPoint) || StringUtil.isEmpty(triggerPoint)) {
			throw new ClientException("CDI Point or trigger point is null.");
		}
		List<IMLotCdiAction> actions = Lists.newArrayList();
		while (actionIter.hasNext()) {
			IMLotCdiAction action = actionIter.next();
			if (cdiPoint.equalsIgnoreCase(action.getCdiPointName())
					&& triggerPoint.equalsIgnoreCase(action.getTriggerPoint())) {
				actions.add(action);
			}
		}
		actions.sort(Comparator.comparing(ICdiAction::getPriority));
		return actions;
	}

}
