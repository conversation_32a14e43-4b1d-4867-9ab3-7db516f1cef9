package com.glory.mes.mm.bom.model;

import java.util.List;
import java.util.Map;

import com.glory.framework.base.model.VersionControl;
import com.glory.framework.core.util.IDynamicComponent;


public class BomTemplate extends VersionControl  implements IDynamicComponent {
	
	private static final long serialVersionUID = 1L;
	
	private List<BomTemplateLine> bomTemplateLines;
	
	private Map udf;

	@Override
	public Map getUdf() {
		return udf;
	}

	@Override
	public void setUdf(Map dynamicComponent) {
		this.udf = dynamicComponent;
	}
	
	public List<BomTemplateLine> getBomTemplateLines() {
		return bomTemplateLines;
	}

	public void setBomTemplateLines(List<BomTemplateLine> bomTemplateLines) {
		this.bomTemplateLines = bomTemplateLines;
	}
	
}
