package com.glory.mes.mm.his.model;

import com.glory.framework.core.util.SessionContext;
import com.glory.mes.mm.consumable.model.Consumable;

import java.util.Date;

public abstract class ConsumableHis extends MLotHis {	
	
	private static final long serialVersionUID = 1L;
	
	public static final String TRANS_TYPE_USE = "USE";
	public static final String TRANS_TYPE_CLEAN = "CLEAN";
    public static final String TRANS_LOG_EVENT = "LOG_EVENT";
	
	public ConsumableHis() {}
	
	public ConsumableHis(Consumable consumable, SessionContext sc) {
		super(consumable, sc);
			
		this.cleanState = consumable.getCleanState();
		this.cleanDate = consumable.getCleanDate();
		
		this.warehouseId = consumable.getWarehouseId();
		this.locatorId = consumable.getLocatorId();
		this.line = consumable.getLine();
		this.location = consumable.getLocation();
		this.portId = consumable.getPortId();

		this.statusModelRrn = consumable.getStatusModelRrn();
		
	}
	
	private String cleanState;
	
	private Date cleanDate;
	
	/**
	 * 当前仓库(当Durable在仓库时使用)
	 */
	private String warehouseId;
	
	/**
	 * 当前库位(当Durable在仓库时使用)
	 */
	private String locatorId;
	
	/**
	 * 位置(当Durable不在仓库时使用)
	 */
	private String location;
	
	/**
	 * 所在线别
	 */
	private String line;	

	private String portId;
	
	/**
	 * 清洗次数(与limitWarning配和,limitWarning表示最大清洗次数)
	 */
	private Long cleanCount;
	
	/**
	 * 设备状态模型
	 */
	private Long statusModelRrn;
	
	public String getCleanState() {
		return cleanState;
	}

	public void setCleanState(String cleanState) {
		this.cleanState = cleanState;
	}

	public Date getCleanDate() {
		return cleanDate;
	}

	public void setCleanDate(Date cleanDate) {
		this.cleanDate = cleanDate;
	}

	public String getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(String warehouseId) {
		this.warehouseId = warehouseId;
	}

	public String getLocatorId() {
		return locatorId;
	}

	public void setLocatorId(String locatorId) {
		this.locatorId = locatorId;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public String getLine() {
		return line;
	}

	public void setLine(String line) {
		this.line = line;
	}

	public String getPortId() {
		return portId;
	}

	public void setPortId(String portId) {
		this.portId = portId;
	}

	public Long getStatusModelRrn() {
		return statusModelRrn;
	}

	public void setStatusModelRrn(Long statusModelRrn) {
		this.statusModelRrn = statusModelRrn;
	}

	public Long getCleanCount() {
		return cleanCount;
	}

	public void setCleanCount(Long cleanCount) {
		this.cleanCount = cleanCount;
	}
}
