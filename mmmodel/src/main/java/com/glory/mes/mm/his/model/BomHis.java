package com.glory.mes.mm.his.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.glory.framework.activeentity.model.ADUpdatableHisSeq;
import com.glory.framework.core.util.IDynamicComponent;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.mes.mm.bom.model.Bom;
import com.glory.mes.mm.bom.model.BomLine;

public class BomHis extends ADUpdatableHisSeq implements IDynamicComponent {

    private static final long serialVersionUID = 1L;
    
    public BomHis() {
    }
	
    public BomHis(Bom bom) {
    	this.orgRrn = bom.getOrgRrn();
    	this.created = bom.getCreated();
    	this.createdBy = bom.getCreatedBy();
    	this.updated = bom.getUpdated();
    	this.updatedBy = bom.getUpdatedBy();
		this.bomRrn = bom.getObjectRrn();
		this.bomName = bom.getName();
		this.bomDesc = bom.getDescription();
		this.bomStatus = bom.getStatus();
		this.bomVersion = bom.getVersion();
		this.materialName = bom.getMaterialName();
		this.materialVersion = bom.getMaterialVersion();
		this.uomId = bom.getUomId();
		this.bomUse = bom.getBomUse();
		this.validFrom = bom.getValidFrom();
		this.validTo = bom.getValidTo();
		
		this.reserved1 = bom.getReserved1();
		this.reserved2 = bom.getReserved2();
		this.reserved3 = bom.getReserved3();
		this.reserved4 = bom.getReserved4();
		this.reserved5 = bom.getReserved5();
		
		this.reserved6 = bom.getReserved6();
		this.reserved7 = bom.getReserved7();
		this.reserved8 = bom.getReserved8();
		this.reserved9 = bom.getReserved9();
		this.reserved10 = bom.getReserved10();
		
		List<BomLineHis> hisLines = new ArrayList<BomLineHis>();
		if (bom.getBomLines() != null && bom.getBomLines().size() > 0) {
			for (BomLine line : bom.getBomLines()) {
				BomLineHis hisLine = new BomLineHis();
				PropertyUtil.copyProperties(hisLine, line);
				hisLine.setObjectRrn(null);
				hisLines.add(hisLine);
			}
		}
		this.bomHisLines = hisLines;
	}

    private String transType;
    
	private Long bomRrn;
    
	private String bomName;
    
	private String bomDesc;
    
  	private String bomStatus;
    
	private Long bomVersion;
    
	private String materialName;
	
	private Long materialVersion;
	
	private String uomId;
	
	private String bomUse;
	
	private Date validFrom;
	
	private Date validTo;
	
	private String reserved1;
	
	private String reserved2;

	private String reserved3;

	private String reserved4;

	private String reserved5;

	private String reserved6;

	private String reserved7;

	private String reserved8;

	private String reserved9;

	private String reserved10;
	
	private Map udf;
	
	private List<BomLineHis> bomHisLines = new ArrayList<BomLineHis>();

	public String getTransType() {
		return transType;
	}

	public void setTransType(String transType) {
		this.transType = transType;
	}

	public Long getBomRrn() {
		return bomRrn;
	}

	public void setBomRrn(Long bomRrn) {
		this.bomRrn = bomRrn;
	}

	public String getBomName() {
		return bomName;
	}

	public void setBomName(String bomName) {
		this.bomName = bomName;
	}

	public String getBomDesc() {
		return bomDesc;
	}

	public void setBomDesc(String bomDesc) {
		this.bomDesc = bomDesc;
	}

	public String getBomStatus() {
		return bomStatus;
	}

	public void setBomStatus(String bomStatus) {
		this.bomStatus = bomStatus;
	}

	public Long getBomVersion() {
		return bomVersion;
	}

	public void setBomVersion(Long bomVersion) {
		this.bomVersion = bomVersion;
	}

	public String getMaterialName() {
		return materialName;
	}

	public void setMaterialName(String materialName) {
		this.materialName = materialName;
	}

	public Long getMaterialVersion() {
		return materialVersion;
	}

	public void setMaterialVersion(Long materialVersion) {
		this.materialVersion = materialVersion;
	}

	public String getUomId() {
		return uomId;
	}

	public void setUomId(String uomId) {
		this.uomId = uomId;
	}

	public String getBomUse() {
		return bomUse;
	}

	public void setBomUse(String bomUse) {
		this.bomUse = bomUse;
	}

	public Date getValidFrom() {
		return validFrom;
	}

	public void setValidFrom(Date validFrom) {
		this.validFrom = validFrom;
	}

	public Date getValidTo() {
		return validTo;
	}

	public void setValidTo(Date validTo) {
		this.validTo = validTo;
	}

	public String getReserved1() {
		return reserved1;
	}

	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}

	public String getReserved2() {
		return reserved2;
	}

	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}

	public String getReserved3() {
		return reserved3;
	}

	public void setReserved3(String reserved3) {
		this.reserved3 = reserved3;
	}

	public String getReserved4() {
		return reserved4;
	}

	public void setReserved4(String reserved4) {
		this.reserved4 = reserved4;
	}

	public String getReserved5() {
		return reserved5;
	}

	public void setReserved5(String reserved5) {
		this.reserved5 = reserved5;
	}

	public String getReserved6() {
		return reserved6;
	}

	public void setReserved6(String reserved6) {
		this.reserved6 = reserved6;
	}

	public String getReserved7() {
		return reserved7;
	}

	public void setReserved7(String reserved7) {
		this.reserved7 = reserved7;
	}

	public String getReserved8() {
		return reserved8;
	}

	public void setReserved8(String reserved8) {
		this.reserved8 = reserved8;
	}

	public String getReserved9() {
		return reserved9;
	}

	public void setReserved9(String reserved9) {
		this.reserved9 = reserved9;
	}

	public String getReserved10() {
		return reserved10;
	}

	public void setReserved10(String reserved10) {
		this.reserved10 = reserved10;
	}

	public List<BomLineHis> getBomHisLines() {
		return bomHisLines;
	}

	public void setBomHisLines(List<BomLineHis> bomHisLines) {
		this.bomHisLines = bomHisLines;
	}
	
	@Override
	public Map getUdf() {
		return udf;
	}

	@Override
	public void setUdf(Map dynamicComponent) {
		this.udf = dynamicComponent;
	}
}
