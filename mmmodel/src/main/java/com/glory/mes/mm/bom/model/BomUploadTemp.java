package com.glory.mes.mm.bom.model;

import java.math.BigDecimal;
import java.util.Date;

import com.glory.framework.activeentity.model.ADBase;

public class BomUploadTemp extends ADBase {

	private static final long serialVersionUID = 1L;

	private String name;

	private String description;
	
	private Long version;
	
	private String partName;
	
	private Long partVersion;
	
	private String bomUse; 
	
	private String bomUomId;
	
	private Date bomValidFrom;
	
	private Date bomValidTo;
	
	
	/**
	 * BOMLine在BOM中的顺序
	 */
	private Long seqNo;
	
	/**
	 * ITEM的类别
	 */
	private String itemCategory;
	
	/**
	 * BOMLine所在的Step名称
	 */
	private String stepName;
	
	/**
	 * BOMLine所在的Step版本
	 */
	private Long stepVersion;
	
	/**
	 * 对应物料的ObjectRrn
	 */
	private Long materialRrn;
	
	/**
	 * 对应物料的名称
	 */
	private String materialName;
	
	private Long materialVersion;
	
	/**
	 * 对应物料的名称
	 */
	private String materialDesc;

	/**
	 * 对应物料的类型
	 */
	private String materialType;
	
	/**
	 * 对应物料的单位用量
	 */
	private BigDecimal unitQty;
	
	/**
	 * 按照百分批方式定义物料与batchQty配合使用
	 */
	private String isQtyPercent;
	
	/**
	 * 对应物料的单位用量
	 */
	private BigDecimal batchQty;
	
	/**
	 * 对应物料的单位
	 */
	private String uomId;
	
	/**
	 * 对应物料的固定损耗率
	 */
	private BigDecimal fixedQty;
	
	/**
	 * 对应物料的损耗率
	 * 实际所需数量 = fixedQty + 数量 * unitQty * (1 + lossRate)
	 */
	private BigDecimal lossRate;
	
	/**
	 * BOMLine生效时间(未使用)
	 */
	private Date validFrom;
	
	/**
	 * BOMLine失效时间(未使用)
	 */
	private Date validTo;
	
	/**
	 * 主物料
	 * 主物料在投料的时候使用
	 * 在投料时就需要选择对应的主物料
	 */
	private String isMain;
	
	/**
	 * 关键物料
	 * 关键物料在批次过站的时候进行确认
	 * 一般在进站时检查物料是否正确
	 * 出站时记录对应的使用数量
	 * 需要和特殊的TrackIn/Out配合使用
	 */
	private String isCritical;
	
	/**
	 * 键合物料
	 * 需要和特殊的键合TrackIn/Out配合使用
	 */
	private String isAssembly;
	
	/**
	 * 是否需要生产
	 * 如果生产则展开下级BOM，否则将产品作为原材料
	 */
	private String isProduction;
	
	/**
	 * 可选料,该物料在实际使用过程中可使用也可不使用
	 */
	private String isOptional;
	
	/**
	 * 倒扣料方式
	 */
	private String flushType;
	
	/**
	 * 可替代料,该物料在实际使用过程中可以被其它物料所替代
	 */
	private String isAlternate;
	
	/**
	 * 替代策略:全部替代或部分替代
	 * 目前仅使用全部替代
	 */
	private String alternateStrategy;
	
	/**
	 * 替代组
	 * 只有同一组类的物料才能相互替代
	 */
	private String alternateGroup;
	
	/**
	 * 替代优先级
	 */
	private BigDecimal alternatePriority;
	
	/**
	 * 替代百分百
	 * 只在部分替代时使用(目前未使用)
	 */
	private BigDecimal alternatePercent;
	
	/**
	 * 用于Co-Product时成本分摊
	 */
	private BigDecimal costAllocationPercent;
	
	
	private String positionName;
	
	private String comments;
	
	
	public BomUploadTemp() {
		super();
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getPartName() {
		return partName;
	}

	public void setPartName(String partName) {
		this.partName = partName;
	}

	public Long getPartVersion() {
		return partVersion;
	}

	public void setPartVersion(Long partVersion) {
		this.partVersion = partVersion;
	}

	public String getBomUse() {
		return bomUse;
	}

	public void setBomUse(String bomUse) {
		this.bomUse = bomUse;
	}

	public String getBomUomId() {
		return bomUomId;
	}

	public void setBomUomId(String bomUomId) {
		this.bomUomId = bomUomId;
	}

	public Date getBomValidFrom() {
		return bomValidFrom;
	}

	public void setBomValidFrom(Date bomValidFrom) {
		this.bomValidFrom = bomValidFrom;
	}

	public Date getBomValidTo() {
		return bomValidTo;
	}

	public void setBomValidTo(Date bomValidTo) {
		this.bomValidTo = bomValidTo;
	}

	public Long getSeqNo() {
		return seqNo;
	}

	public void setSeqNo(Long seqNo) {
		this.seqNo = seqNo;
	}

	public String getItemCategory() {
		return itemCategory;
	}

	public void setItemCategory(String itemCategory) {
		this.itemCategory = itemCategory;
	}

	public String getStepName() {
		return stepName;
	}

	public void setStepName(String stepName) {
		this.stepName = stepName;
	}

	public Long getStepVersion() {
		return stepVersion;
	}

	public void setStepVersion(Long stepVersion) {
		this.stepVersion = stepVersion;
	}

	public Long getMaterialRrn() {
		return materialRrn;
	}

	public void setMaterialRrn(Long materialRrn) {
		this.materialRrn = materialRrn;
	}

	public String getMaterialName() {
		return materialName;
	}

	public void setMaterialName(String materialName) {
		this.materialName = materialName;
	}

	public Long getMaterialVersion() {
		return materialVersion;
	}

	public void setMaterialVersion(Long materialVersion) {
		this.materialVersion = materialVersion;
	}

	public String getMaterialDesc() {
		return materialDesc;
	}

	public void setMaterialDesc(String materialDesc) {
		this.materialDesc = materialDesc;
	}

	public String getMaterialType() {
		return materialType;
	}

	public void setMaterialType(String materialType) {
		this.materialType = materialType;
	}

	public BigDecimal getUnitQty() {
		return unitQty;
	}

	public void setUnitQty(BigDecimal unitQty) {
		this.unitQty = unitQty;
	}

	public Boolean getIsQtyPercent() {
		return "Y".equalsIgnoreCase(this.isQtyPercent) ? true : false;
	}

	public void setIsQtyPercent(Boolean isQtyPercent) {
		if(isQtyPercent == null) {
			this.isQtyPercent = "N";
		}else {
			this.isQtyPercent = isQtyPercent ? "Y" : "N";
		}
	}

	public BigDecimal getBatchQty() {
		return batchQty;
	}

	public void setBatchQty(BigDecimal batchQty) {
		this.batchQty = batchQty;
	}

	public String getUomId() {
		return uomId;
	}

	public void setUomId(String uomId) {
		this.uomId = uomId;
	}

	public BigDecimal getFixedQty() {
		return fixedQty;
	}

	public void setFixedQty(BigDecimal fixedQty) {
		this.fixedQty = fixedQty;
	}

	public BigDecimal getLossRate() {
		return lossRate;
	}

	public void setLossRate(BigDecimal lossRate) {
		this.lossRate = lossRate;
	}

	public Date getValidFrom() {
		return validFrom;
	}

	public void setValidFrom(Date validFrom) {
		this.validFrom = validFrom;
	}

	public Date getValidTo() {
		return validTo;
	}

	public void setValidTo(Date validTo) {
		this.validTo = validTo;
	}

	public Boolean getIsMain() {
		return "Y".equalsIgnoreCase(this.isMain) ? true : false;
	}

	public void setIsMain(Boolean isMain) {
		if(isMain == null) {
			this.isMain = "N";
		}else {
			this.isMain = isMain ? "Y" : "N";
		}
	}

	public Boolean getIsCritical() {
		return "Y".equalsIgnoreCase(this.isCritical) ? true : false;
	}

	public void setIsCritical(Boolean isCritical) {
		if(isCritical == null) {
			this.isCritical = "N";
		}else {
			this.isCritical = isCritical ? "Y" : "N";
		}
	}

	public Boolean getIsAssembly() {
		return "Y".equalsIgnoreCase(this.isAssembly) ? true : false;
	}

	public void setIsAssembly(Boolean isAssembly) {
		if(isAssembly == null) {
			this.isAssembly = "N";
		}else {
			this.isAssembly = isAssembly ? "Y" : "N";
		}
	}

	public Boolean getIsOptional() {
		return "Y".equalsIgnoreCase(this.isOptional) ? true : false;
	}

	public void setIsOptional(Boolean isOptional) {
		if(isOptional == null) {
			this.isOptional = "N";
		}else {
			this.isOptional = isOptional ? "Y" : "N";
		}
	}
	
	public Boolean getIsProduction(){
		return "Y".equalsIgnoreCase(this.isProduction) ? true : false; 
	}

	public void setIsProduction(Boolean isProduction) {
		if(isProduction == null) {
			this.isProduction = "N";
		}else {
			this.isProduction = isProduction ? "Y" : "N";
		}
	}

	public String getFlushType() {
		return flushType;
	}

	public void setFlushType(String flushType) {
		this.flushType = flushType;
	}

	public Boolean getIsAlternate() {
		return "Y".equalsIgnoreCase(this.isAlternate) ? true : false;
	}

	public void setIsAlternate(Boolean isAlternate) {
		if(isAlternate == null) {
			this.isAlternate = "N";
		}else {
			this.isAlternate = isAlternate ? "Y" : "N";
		}
	}

	public String getAlternateStrategy() {
		return alternateStrategy;
	}

	public void setAlternateStrategy(String alternateStrategy) {
		this.alternateStrategy = alternateStrategy;
	}

	public String getAlternateGroup() {
		return alternateGroup;
	}

	public void setAlternateGroup(String alternateGroup) {
		this.alternateGroup = alternateGroup;
	}

	public BigDecimal getAlternatePriority() {
		return alternatePriority;
	}

	public void setAlternatePriority(BigDecimal alternatePriority) {
		this.alternatePriority = alternatePriority;
	}

	public BigDecimal getAlternatePercent() {
		return alternatePercent;
	}

	public void setAlternatePercent(BigDecimal alternatePercent) {
		this.alternatePercent = alternatePercent;
	}

	public BigDecimal getCostAllocationPercent() {
		return costAllocationPercent;
	}

	public void setCostAllocationPercent(BigDecimal costAllocationPercent) {
		this.costAllocationPercent = costAllocationPercent;
	}

	public String getPositionName() {
		return positionName;
	}

	public void setPositionName(String positionName) {
		this.positionName = positionName;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	@Override
	public Long getObjectRrn() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void setObjectRrn(Long objectRrn) {
		// TODO Auto-generated method stub
		
	}
}
