<?xml version="1.0" encoding="UTF-8"?>
<persistence version="1.0" xmlns="http://java.sun.com/xml/ns/persistence" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://java.sun.com/xml/ns/persistence http://java.sun.com/xml/ns/persistence/persistence_1_0.xsd">
	<persistence-unit name="edc" transaction-type="JTA">
		<provider>org.hibernate.ejb.HibernatePersistence</provider>
		<jta-data-source>java:/OracleDS</jta-data-source>
		<jar-file>edcmodel.jar</jar-file>
		<properties>
			<property name="hibernate.dialect" value="${profiles.active}"/>
        	<property name="hibernate.hbm2ddl.auto" value="none"/>
         	<property name="hibernate.show_sql" value="true"/>
    	</properties>
	</persistence-unit>
</persistence>
