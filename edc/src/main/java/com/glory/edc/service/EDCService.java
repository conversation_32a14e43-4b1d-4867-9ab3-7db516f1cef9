package com.glory.edc.service;

import java.util.List;

import javax.ejb.EJB;
import javax.ejb.Stateless;

import com.glory.edc.ejb.EDCManagerLocal;
import com.glory.edc.model.EdcProcessData;
import com.glory.edc.model.EdcProcessDataLog;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.service.CoreService;
import com.glory.framework.core.util.SessionContext;

@Stateless
@CoreService(serviceGroup = "MESCore")
public class EDCService {
	
	@EJB
	private EDCManagerLocal edcManager;
	
	/**
	 * @see com.glory.edc.ejb.EDCManagerBean#isMatchProcessData(long, EdcProcessData, String)
	 * 匹配ProcessData,如果匹配成功,则按照Item名称对EdcProcessData进行分组
	 * 
	 * @param orgRrn
	 * @param processData 待检查的数据
	 * @param configName 配置名称
	 * 
	 */
	public List<EdcProcessData> matchProcessDataAndGrouping(long orgRrn, EdcProcessData processData, String configName) throws ClientException {
        return edcManager.matchProcessDataAndGrouping(orgRrn, processData, configName);
    }
	
	/**
	 * 保存ProcessData
     *
	 * @param processDataList
	 * @param sc 会话上下文
	 * @throws ClientException 客户端异常
	 */
	public List<EdcProcessData> saveProcessDataList(List<EdcProcessData> processDataList, SessionContext sc) throws ClientException {
		return edcManager.saveProcessDataList(processDataList, sc);
	}
	
	/**
	 * 保存ProcessData处理日志,一般用于记录ProcessData处理异常
	 * 
	 * @param processData 待保存的processData
	 * @param transType 事务类型
	 * @param returnCode 返回码
	 * @param returnMessage 返回消息(异常消息)
	 * @param sc
	 */
	public EdcProcessDataLog saveProcessDataLog(EdcProcessData processData, String transType, String returnCode, String returnMessage, SessionContext sc) throws ClientException {
		return edcManager.saveProcessDataLog(processData, transType, returnCode, returnMessage, sc);
	}
}
