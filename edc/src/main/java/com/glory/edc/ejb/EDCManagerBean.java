package com.glory.edc.ejb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import javax.ejb.EJB;
import javax.ejb.Local;
import javax.ejb.Remote;
import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.log4j.Logger;
import org.lsmp.djep.vectorJep.VectorJep;
import org.nfunk.jep.SymbolTable;

import com.glory.common.context.ejb.ContextManagerLocal;
import com.glory.common.context.model.Context;
import com.glory.common.context.model.ContextValue;
import com.glory.edc.client.EDCManager;
import com.glory.edc.exception.EDCExceptionBundle;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.AbstractEdcSetLine;
import com.glory.edc.model.EdcAQLSet;
import com.glory.edc.model.EdcBinSet;
import com.glory.edc.model.EdcBinSetLine;
import com.glory.edc.model.EdcContext;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcDataHis;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcItemSetLineAttr;
import com.glory.edc.model.EdcProcessData;
import com.glory.edc.model.EdcProcessDataConfig;
import com.glory.edc.model.EdcProcessDataLine;
import com.glory.edc.model.EdcProcessDataLog;
import com.glory.edc.model.EdcResult;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcSpec;
import com.glory.edc.model.EdcSubgroupPlan;
import com.glory.edc.model.EdcTecn;
import com.glory.edc.model.EdcTecnContext;
import com.glory.edc.model.EdcTextSet;
import com.glory.edc.model.EdcTextSetLine;
import com.glory.edc.model.auto.EdcAutoConfig;
import com.glory.edc.model.calculation.FormulaUtil;
import com.glory.edc.model.calculation.FormulaVariable;
import com.glory.edc.model.calculation.ProcessGroup;
import com.glory.edc.model.calculation.ProcessGroupData;
import com.glory.edc.model.sampling.AqlSamplingDetail;
import com.glory.edc.model.sampling.AqlSamplingPlan;
import com.glory.edc.model.sampling.AqlSamplingPlanInstance;
import com.glory.edc.model.sampling.AqlSamplingTable;
import com.glory.edc.model.sampling.ManualSamplingPlan;
import com.glory.edc.model.sampling.SamplingPlan;
import com.glory.edc.model.sampling.SamplingPlanCalc;
import com.glory.edc.model.spc.SpcResultJob;
import com.glory.framework.activeentity.ejb.ADManagerLocal;
import com.glory.framework.activeentity.ejb.BASManagerLocal;
import com.glory.framework.activeentity.ejb.SysParameterManagerLocal;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.model.VersionControl;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ExceptionManager;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.SnowFlake;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.base.model.WorkStation;
import com.glory.mes.base.util.ParameterUtil;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.msg.send.MessageSender;
import com.glory.mes.msg.send.spc.data.SpcDataRequest;
import com.glory.mes.msg.send.trans.SendMesTransContext;
import com.glory.mes.ras.eqp.EquipmentHold;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotHold;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

@Stateless
@Remote(EDCManager.class)
@Local(EDCManagerLocal.class)
public class EDCManagerBean implements EDCManager, EDCManagerLocal {
	
	private static final Logger logger = Logger.getLogger(EDCManagerBean.class);
	
	@PersistenceContext
	private EntityManager em;
	
	@EJB
	private ADManagerLocal adManager;
	
	@EJB
	private BASManagerLocal basManager;
	
	@EJB
	private ContextManagerLocal ctxManager;
	
	@EJB 
	private SysParameterManagerLocal sysParamManager;

	@EJB
	private MessageSender messageSender;
	
	public EntityManager getEntityManager() {
		return em;
	}
	
	/**
	 * 获得数据收集定义
     *
	 * @param orgRrn
	 * @customAnnotate 注解啊
	 * @param edcSetName
	 * @param version 为空时,取Active的edcSet
	 * @param isThrowException
	 */
	public AbstractEdcSet getEdcSet(long orgRrn, String edcSetName, Long version, boolean isThrowException) throws ClientException {
		return getEdcSet(orgRrn, edcSetName, version, false, isThrowException);
	}
	
	public AbstractEdcSet getEdcSet(long orgRrn, String edcSetName, Long version, boolean isLoadLine, boolean isThrowException) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" SELECT AbstractEdcSet FROM AbstractEdcSet AbstractEdcSet ");
			sql.append(" WHERE ");
            sql.append(ADBase.BASE_CONDITION_N);
            sql.append(" AND name = :name");
            if (version == null) {
            	sql.append(" AND status = :status");
            } else {
            	sql.append(" AND version = :version");
            }
            Query query = em.createQuery(sql.toString());
            query.setParameter("name", edcSetName);
            if (version == null) {
                query.setParameter("status", AbstractEdcSet.STATUS_ACTIVE);
            } else {
                query.setParameter("version", version);
            }
            query.setParameter("orgRrn", orgRrn);
            List<AbstractEdcSet> edcSets = query.getResultList();
            if (edcSets.size() == 0) {
            	if (isThrowException) {
                    throw EDCExceptionBundle.bundle.EdcSetNotExistOrNoActive();
            	}
            	return null;
            } else if (isLoadLine) {
            	edcSets.get(0).getEdcSetLine().size();
            	for (AbstractEdcSetLine line : edcSets.get(0).getEdcSetLine()) {
            		if(line instanceof EdcItemSetLine) {
            			EdcItemSetLine itemSetLine = (EdcItemSetLine) line;
            			itemSetLine.getEdcItem().getObjectRrn();
            		}
				}
            }
			return edcSets.get(0);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 获取EDCSet详细数据
	 * 
	 * @param orgRrn
	 * @param edcSetName
	 * @param version
	 * @param isThrowException
	 * @return
	 * @throws ClientException
	 */
	@Override
	public AbstractEdcSet getEdcSetFull(long orgRrn, String edcSetName, Long version, boolean isThrowException)
			throws ClientException {
		try {
			AbstractEdcSet edcSet = getEdcSet(orgRrn, edcSetName, version, false, isThrowException);

			if (edcSet instanceof EdcItemSet) {
				List<EdcItemSetLine> itemSetLines = ((EdcItemSet) edcSet).getItemSetLines();
				if (CollectionUtils.isNotEmpty(itemSetLines)) {
					for (EdcItemSetLine setLine : itemSetLines) {
						List<FormulaVariable> variables = setLine.getFormulaVariables();
						if (CollectionUtils.isNotEmpty(variables)) {
							variables.size();
						}

						List<EdcItemSetLineAttr> lineAttrs = setLine.getAttributes();
						if (CollectionUtils.isNotEmpty(lineAttrs)) {
							lineAttrs.size();
						}
					}
				}
			}

			return edcSet;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 *批量保存数据采集项集信息
     *
	 * @param edcSets 采集项集列表
	 * @param sc 会话上下文
	 * @throws ClientException 客户端异常
	 */
	@Override
	public List<AbstractEdcSet> saveEdcSets(List<AbstractEdcSet> edcSets, SessionContext sc) throws ClientException {
		List<AbstractEdcSet> abstractEdcSets = new ArrayList<>();
		for (AbstractEdcSet edcSet : edcSets) {
			AbstractEdcSet abstractEdcSet = saveEdcSet(edcSet, sc);
			abstractEdcSets.add(abstractEdcSet);
		}
		return abstractEdcSets;
	}
	
	/**
	 *批量保存修改数据采集项信息
	 * @param edcItems 采集项列表
	 * @param sc 会话上下文
	 * @throws ClientException 客户端异常
	 */
	@Override
	public List<EdcItem> saveEdcItems(List<EdcItem> edcItems, SessionContext sc) throws ClientException {
		List<EdcItem> items = new ArrayList<>();
		for (EdcItem edcItem : edcItems) {
			if (edcItem.getObjectRrn() == null) {
				edcItem.setOrgRrn(sc.getOrgRrn());
				edcItem.setCreatedBy(sc.getUserName());
				edcItem.setCreated(new Date());
				edcItem.setUpdatedBy(sc.getUserName());
				em.persist(edcItem);
			} else {
				edcItem.setUpdatedBy(sc.getUserName());
				em.merge(edcItem);
			}
			items.add(edcItem);
		}
		return items;
	}
	
	/**
	 * 保存ProcessData
     *
	 * @param processDataList
	 * @param sc 会话上下文
	 * @throws ClientException 客户端异常
	 */
	@Override
	public List<EdcProcessData> saveProcessDataList(List<EdcProcessData> processDataList, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			List<EdcProcessData> result = Lists.newArrayList();
			for (EdcProcessData processData : processDataList) {
				List<EdcProcessDataLine> lineList = Lists.newArrayList(processData.getEdcProcessDataLines());
				processData.setOrgRrn(sc.getOrgRrn());
				processData.setCreated(sc.getTransTime());
				processData.setCreatedBy(sc.getUserName());
				processData.setUpdatedBy(sc.getUserName());
				em.persist(processData);
				
				long i = 1;
				for (EdcProcessDataLine line : lineList) {
					line.setSeqNo(i);
					line.setIsActive(true);
					line.setOrgRrn(sc.getOrgRrn());
					line.setProcessDateTime(sc.getTransTime());
					line.setEdcProcessDataRrn(processData.getObjectRrn());
					em.persist(line);
					i ++;
				}
				result.add(processData);
			}
			
			return result;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据config配置,检查processData是否满足配置要求
	 * Match条件1,eqpType; 2,stepName
	 * 条件可用为空,当两个都设置时需要同时满足
	 * 
	 * @param orgRrn
	 * @param processData 待检查的数据
	 * @param configName 配置名称
	 * 
	 */
	public boolean isMatchProcessData(long orgRrn, EdcProcessData processData, String configName) throws ClientException {
		try {
			List<EdcProcessDataConfig> configs = getEdcProcessDataConfig(orgRrn, configName, true);
			for (EdcProcessDataConfig config : configs) {
				if (processData.isMatch(config)) {
					return true;
				}
			}
			return false;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * @see com.glory.edc.ejb.EDCManagerBean#isMatchProcessData(long, EdcProcessData, String)
	 * 匹配ProcessData,如果匹配成功,则按照Item名称对EdcProcessData进行分组
	 * 
	 * @param orgRrn
	 * @param processData 待检查的数据
	 * @param configName 配置名称
	 * 
	 */
	public List<EdcProcessData> matchProcessDataAndGrouping(long orgRrn, EdcProcessData processData, String configName) throws ClientException {
		try {
			List<EdcProcessDataConfig> configs = getEdcProcessDataConfig(orgRrn, configName, true);
			List<EdcProcessData> groupProcessDatas = Lists.newArrayList();
			if (CollectionUtils.isNotEmpty(configs)) {
				for (EdcProcessDataConfig config : configs) {
					if (processData.isMatch(config)) {
						for (String itemName : config.getItemNameList()) {
							List<EdcProcessDataLine> itemDatas = processData.getEdcProcessDataLines().stream().filter(p -> itemName.equals(p.getDvName())).collect(Collectors.toList());
							if (CollectionUtils.isNotEmpty(itemDatas)) {
								EdcProcessData itemProcessData = new EdcProcessData();
								int i = 1;
								for (EdcProcessDataLine itemData : itemDatas) {
									itemData.setSeqNo(Long.valueOf(i));
									itemData.setComponentId(processData.getComponentId());
									i++;
								}
								itemProcessData.setEdcProcessDataLines(itemDatas);
								itemProcessData.buildSpcData();
								groupProcessDatas.add(itemProcessData);
							}
						}
						break;
					}
				}
			}
			
			return groupProcessDatas;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 获得Config配置,按SeqNo排序
	 * @param orgRrn
	 * @param configName 配置名称
	 * @param isThrowException 未找到时,抛出异常
	 */
	public List<EdcProcessDataConfig> getEdcProcessDataConfig(long orgRrn, String configName, boolean isThrowException) throws ClientException {
		try {
			List<EdcProcessDataConfig> processDataConfigs = Lists.newArrayList();
			StringBuffer sql = new StringBuffer(" SELECT EdcProcessDataConfig FROM EdcProcessDataConfig EdcProcessDataConfig ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND configName = :configName");
			sql.append(" ORDER BY seqNo DESC ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("configName", configName);
			processDataConfigs = query.getResultList();
			if (CollectionUtils.isEmpty(processDataConfigs) && isThrowException) {
				throw EDCExceptionBundle.bundle.ProcessDataConfigNotFound();
			}
			return processDataConfigs;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 保存ProcessData处理日志,一般用于记录ProcessData处理异常
	 * 
	 * @param processData 待保存的processData
	 * @param transType 事务类型
	 * @param returnCode 返回码
	 * @param returnMessage 返回消息(异常消息)
	 * @param sc
	 */
	public EdcProcessDataLog saveProcessDataLog(EdcProcessData processData, String transType, String returnCode, String returnMessage, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			if (processData.getObjectRrn() == null) {
				//如果没有保存,则先执行保存逻辑
				processData = saveProcessDataList(Lists.newArrayList(processData), sc).get(0);
			}
			EdcProcessDataLog log = new EdcProcessDataLog();
			log.setProcessDataRrn(processData.getObjectRrn());
			log.setIsActive(true);
			log.setUpdatedBy(sc.getUserName());
			log.setTransTime(new Date());
			log.setLotId(processData.getLotId());
			log.setEquipmentId(processData.getEquipmentId());
			log.setStepName(processData.getStepName());
			log.setTransType(transType);
			log.setReturnCode(returnCode);
			log.setReturnMessage(returnMessage);
			em.persist(log);
			
			return log;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 *保存数据采集项集EDC_SET信息,新建如名称重复自动升版本，objectRrn已存在则修改
     *
	 * @param edcSet 采集项集
	 * @param sc 会话上下文
	 * @throws ClientException 客户端异常
	 */
	public AbstractEdcSet saveEdcSet(AbstractEdcSet edcSet, SessionContext sc) throws ClientException {
		try {
			if (edcSet.getObjectRrn() == null) {
				edcSet.setIsActive(true);
				edcSet.setCreatedBy(sc.getUserName());
				edcSet.setCreated(new Date());
				edcSet.setStatus(EdcItemSet.STATUS_UNFROZNE);
				AbstractEdcSet lastEdcSet = basManager.getLastVersionControl(edcSet.getOrgRrn(), AbstractEdcSet.class, edcSet.getName());
				if (lastEdcSet != null) {
					//检查Name是否重复(EdcItemSet和EdcBinSet名称不能相同)
					if ((edcSet instanceof EdcItemSet && lastEdcSet instanceof EdcBinSet)
							|| (edcSet instanceof EdcBinSet && lastEdcSet instanceof EdcItemSet)) {
						throw EDCExceptionBundle.bundle.EdcSetNameIsExist();
					}
					edcSet.setVersion(lastEdcSet.getVersion() + 1);
				} else {
					edcSet.setVersion(1L);
				}
				if (edcSet instanceof EdcItemSet) {
					for (EdcItemSetLine line : ((EdcItemSet)edcSet).getItemSetLines()) {
						//处理样本计划
						if (line.getSubgroupPlans() != null && line.getSubgroupPlans().size() > 0) {
							line.setIsSubgroupPlan(true);
						} else {
							line.setIsSubgroupPlan(false);
						}
					}
					edcSet.setUpdatedBy(sc.getUserName());
					em.persist(edcSet);
					for (EdcItemSetLine line : ((EdcItemSet)edcSet).getItemSetLines()) {
						//处理样本计划
						if (line.getIsSubgroupPlan()) {
							for (EdcSubgroupPlan subgroupPlan : line.getSubgroupPlans()) {
								subgroupPlan.setItemSetLineRrn(line.getObjectRrn());
								em.merge(subgroupPlan);
							}
						}
					}
				} else {
					edcSet.setUpdatedBy(sc.getUserName());
					em.persist(edcSet);
				}
			} else {
				if (edcSet instanceof EdcItemSet) {
					//处理样本计划
					for (EdcItemSetLine line : ((EdcItemSet)edcSet).getItemSetLines()) {
						if (line.getObjectRrn() != null) {
							String sql = " DELETE FROM EdcSubgroupPlan WHERE itemSetLineRrn = " + line.getObjectRrn();
							Query query = em.createQuery(sql.toString());
							query.executeUpdate();
						} 
						if (line.getSubgroupPlans() != null && line.getSubgroupPlans().size() > 0) {
							line.setIsSubgroupPlan(true);
							if (line.getObjectRrn() == null) {
								em.persist(line);
							} 
							for (EdcSubgroupPlan subgroupPlan : line.getSubgroupPlans()) {
								subgroupPlan.setItemSetLineRrn(line.getObjectRrn());
								em.merge(subgroupPlan);
							}
						} else {
							line.setIsSubgroupPlan(false);
						}
					}
				}
				edcSet.setUpdatedBy(sc.getUserName());
				em.merge(edcSet);
			}
			return edcSet;
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 *保存并，冻结数据采集项信息
     *
	 * @param edcSet 采集项集
	 * @param sc 会话上下文
	 * @throws ClientException 客户端异常
	 */
	public AbstractEdcSet frozen(AbstractEdcSet edcSet, SessionContext sc) throws ClientException {
		try {
			edcSet = saveEdcSet(edcSet, sc);
			em.flush();
			
			edcSet = em.find(AbstractEdcSet.class, edcSet.getObjectRrn());
			edcSet = (AbstractEdcSet)basManager.frozen(edcSet, sc);
			return edcSet;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	

	/**
	 * 保存物料批次在当前状态需要做的ItemSet信息
     *
	 * @param mLots 批次列表
	 * @param sc 会话上下文
	 * @return 处理结果
	 * @throws ClientException 客户端异常
	 */
	public boolean saveItemSetCurrents(List<MLot> mLots, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			return saveItemSetCurrents(mLots, null, sc);			
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 保存物料批次在当前状态需要做的数据采集信息
	 * 
	 * @param mLots 批次列表
	 * @param edcConditionMap 扩展条件
	 * @param sc 会话上下文
	 * @return 处理结果
	 * @throws ClientException 客户端异常
	 */
	public boolean saveItemSetCurrents(List<MLot> mLots, Map<String, String> edcConditionMap, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			for (MLot mLot : mLots) {
				Map<String, String> map = new HashMap<String, String>();
				if (edcConditionMap != null && !edcConditionMap.isEmpty()) {
					map.putAll(edcConditionMap);
				}
				map.put(EdcContext.CONTEXT_FIELD_MATERIAL_NAME, mLot.getMaterialName());
				map.put(EdcContext.CONTEXT_FIELD_MATERIAL_TYPE, mLot.getMainMatType());
				map.put(EdcContext.CONTEXT_FIELD_STATE, mLot.getState());
				map.put(EdcContext.CONTEXT_FIELD_PARTNER_CODE, mLot.getPartnerCode());
				
				Context context = ctxManager.getContextByName(sc.getOrgRrn(), EdcContext.CONTEXT_NAME_MLOT, false);
				if (context == null) {
					return false;
				}
				map = Context.generateContextMap(context, mLot, map);
				
				//获得对应的数据收集
				List<AbstractEdcSet> edcSets = new ArrayList<AbstractEdcSet>();
				
				List<Map<String, String>> contextValuesMap = ctxManager.getContextValuesMap(sc.getOrgRrn(), EdcContext.CONTEXT_NAME_MLOT, map);
				for (Map<String, String> contextValueMap : contextValuesMap) {
					String edcSetName = contextValueMap.get(EdcContext.CONTEXT_RESULT_EDC_SET_NAME);
					if (edcSetName != null && edcSetName.trim().length() > 0) {
						AbstractEdcSet edcSet = basManager.getActiveVersionControl(sc.getOrgRrn(), AbstractEdcSet.class, edcSetName);
						if (edcSet != null) {
							edcSets.add(edcSet);
						}
					}
				}
				if (edcSets.size() == 0) {
					continue;
				}
				
				int i = 1;
				//为每个Lot生产数据采集
				for (AbstractEdcSet lotEdcSet : edcSets) {
					if (lotEdcSet.getIsByComponentEdc()) {
						if (mLot.getSubMComponentUnit() != null && mLot.getSubMComponentUnit().size() > 0) {
							for (MComponentUnit unit : mLot.getSubMComponentUnit()) { //为每个MComponentUnit生产数据采集
								MComponentUnit componentUnit =  (MComponentUnit)unit;
								EdcSetCurrent edcCurrent = new EdcSetCurrent();
								edcCurrent.setOrgRrn(sc.getOrgRrn());
								edcCurrent.setIsActive(true);
								edcCurrent.setCreatedBy(sc.getUserName());
								edcCurrent.setCreated(new Date());
								edcCurrent.setUpdatedBy(sc.getUserName());
								edcCurrent.setLotRrn(mLot.getObjectRrn());
								edcCurrent.setComponentUnitId(componentUnit.getmComponentId());
								edcCurrent.setItemSetRrn(lotEdcSet.getObjectRrn());
								edcCurrent.setSeqNo(i * 10L);
								em.persist(edcCurrent);
								i++;
							}	
						}
					} else { //为每个Lot生产数据采集
						EdcSetCurrent edcCurrent = new EdcSetCurrent();
						edcCurrent.setOrgRrn(sc.getOrgRrn());
						edcCurrent.setIsActive(true);
						edcCurrent.setCreatedBy(sc.getUserName());
						edcCurrent.setCreated(new Date());
						edcCurrent.setUpdatedBy(sc.getUserName());
						edcCurrent.setLotRrn(mLot.getObjectRrn());
						edcCurrent.setItemSetRrn(lotEdcSet.getObjectRrn());
						edcCurrent.setSeqNo(i * 10L);
						em.persist(edcCurrent);
						i++;
					}	
				}
			}
			return true;
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
		}	
	}
	
	/**
	* 根据Batch或者Lot删除当前的EDC数据采集,该方法在TrackOut和Abort时被调用
     *
	* @param batchId 批次batchId
	* @param lotRrn 批次主键
	*/
	public void removeItemSetCurrents(String batchId, Long lotRrn) throws ClientException {
		try {
			String sql;
			if (!StringUtil.isEmpty(batchId)) {
				sql = " DELETE FROM EdcSetCurrent WHERE batchId = :batchId";
			} else {
				sql = " DELETE FROM EdcSetCurrent WHERE lotRrn = :lotRrn";
			}
			Query query = em.createQuery(sql.toString());
			if (!StringUtil.isEmpty(batchId)) {
				query.setParameter("batchId", batchId);
			} else {
				query.setParameter("lotRrn", lotRrn);
			}
			query.executeUpdate();
			
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	
	/**
	 * 根据产品,工艺,流程,工步获得当前工步所对应的数据采集名称
	 * 采用并发方式获取
	 * 
	 * @param orgRrn
	 * @param partName 产品名称
	 * @param processName 工艺名称
	 * @param procedureNames 流程名称列表,大小必须与stepNames一致
	 * @param stepNames 工步名称列表
	 * @param otherContext 其他Context条件
	 */
	public List<String> getEdcSetNames(long orgRrn, String partName, String processName, 
			List<String> procedureNames, List<String> stepNames, Map<String, String> otherContext) throws ClientException {
		ExecutorService service = null;
		try {	
			List<String> edcSetNames = new ArrayList<String>();
			
			Map<String, String> conditionMap = new HashMap<String, String>();
			if (otherContext != null && !otherContext.isEmpty()) {
				conditionMap.putAll(otherContext);
			}
			conditionMap.put(EdcContext.CONTEXT_FIELD_PART_NAME, partName);
			conditionMap.put(EdcContext.CONTEXT_FIELD_PROCESS_NAME, processName);
						
			int threadCount = 10;
			if (stepNames.size() >= 100) {
				threadCount = 20;
			}
			service = Executors.newFixedThreadPool(threadCount, new ThreadFactoryBuilder().setNameFormat("EDC-%d").build());
			
			CompletableFuture<?>[] edcQuerys = new CompletableFuture[stepNames.size()];
			for (int i = 0; i < stepNames.size(); i++) {
				Map<String, String> context = new HashMap<String, String>();
				context.putAll(conditionMap);
				
				context.put(EdcContext.CONTEXT_FIELD_PROCEDURE_NAME, procedureNames.get(i));
				context.put(EdcContext.CONTEXT_FIELD_STEP_NAME, stepNames.get(i));
				
				CompletableFuture<?> componentQuery = CompletableFuture.supplyAsync(() -> getEdcSetName(orgRrn, context), service);
				edcQuerys[i] = componentQuery;
			}
			CompletableFuture<Void> futures = CompletableFuture.allOf(edcQuerys);

			List<?> objects = futures.thenApply(e -> Lists.newArrayList(edcQuerys).stream()
					.map(CompletableFuture::join).collect(Collectors.toList())).join();
			edcSetNames = objects.stream().map(s -> ((String)s)).collect(Collectors.toList());
			
			
			return edcSetNames;
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
        } finally {
        	//关闭线程池
			if (service != null) {
				service.shutdown(); 
			}
        }
	}
	
	/**
	 * 根据Context条件获得数据采集名称
     *
	 * @param orgRrn
	 * @param contextMaps context条件
	 */
	public String getEdcSetName(long orgRrn, Map<String, String> contextMaps) throws ClientException {
		try {
			List<Map<String, String>> contextValuesMap = ctxManager.getContextValuesMap(orgRrn, EdcContext.CONTEXT_NAME, contextMaps);
			String edcSetNames = "";
			for (Map<String, String> contextValueMap : contextValuesMap) {
				String edcSetName = contextValueMap.get(EdcContext.CONTEXT_RESULT_EDC_SET_NAME);
				if (edcSetName == null) {
					edcSetName = "";
				}
				if (StringUtil.isEmpty(edcSetNames)) {
					edcSetNames = edcSetName;
				} else {
					edcSetNames = edcSetNames + ";" + edcSetName;
				}
			}
			return edcSetNames;
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
        }
	}

	
	/**
	* 获得当前的数据采集
	 * @param orgRrn 厂区Id
	* @param batchId 批次BatchId
	* @param lotRrn 批次主键
	* @param componentUnitId 组件Id
     */
	public List<EdcSetCurrent> getItemSetCurrents(Long orgRrn, String batchId, Long lotRrn, String componentUnitId) throws ClientException {
		try {
			String sql;
			List<EdcSetCurrent> currents = new ArrayList<EdcSetCurrent>();
			if (!StringUtil.isEmpty(batchId)) {
				sql = " SELECT EdcSetCurrent FROM EdcSetCurrent EdcSetCurrent WHERE (orgRrn=:orgRrn or orgRrn=0) AND batchId = :batchId ORDER BY seqNo ASC";
				Query query = em.createQuery(sql.toString());
				query.setParameter("orgRrn", orgRrn);
				query.setParameter("batchId", batchId);
				currents = (List<EdcSetCurrent>)query.getResultList();
			}
			if (currents.isEmpty() && !StringUtil.isEmpty(componentUnitId)) {
				sql = " SELECT EdcSetCurrent FROM EdcSetCurrent EdcSetCurrent WHERE lotRrn = :lotRrn and componentUnitId = :componentUnitId ORDER BY seqNo ASC";
				Query query = em.createQuery(sql.toString());
				query.setParameter("lotRrn", lotRrn);
				query.setParameter("componentUnitId", componentUnitId);
				currents = (List<EdcSetCurrent>)query.getResultList();
			}
			
			if (currents.isEmpty() ) {
				sql = " SELECT EdcSetCurrent FROM EdcSetCurrent EdcSetCurrent WHERE lotRrn = :lotRrn ORDER BY seqNo ASC";
				Query query = em.createQuery(sql.toString());
				query.setParameter("lotRrn", lotRrn);
				currents = (List<EdcSetCurrent>)query.getResultList();
			}
			return currents;
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 获得ItemSet,如果Line的Spec是参数型,则将其解析为真实值
	* <p><br>
     *
	* @param edcSetRrn 数据采集项集
	* @param specMap 产品规格
	* @param paraMap 产品参数
	* @return AbstractEdcSet
	*/ 
	public AbstractEdcSet getActualEdcSet(Long edcSetRrn, Map<String, EdcSpec> specMap, Map<String, Object> paramMap) throws ClientException {
		try {
			AbstractEdcSet edcSet = em.find(AbstractEdcSet.class, edcSetRrn);
			if (edcSet instanceof EdcItemSet) {
				EdcItemSet itemSet = (EdcItemSet)edcSet;
				for (EdcItemSetLine line : itemSet.getItemSetLines()) {
					if (line.getIsUsePartSpec() && (specMap != null)
							&& !StringUtil.isEmpty(line.getSlString())
							&& specMap.containsKey((line.getSlString()))) {
						line.setSpec(specMap.get(line.getSlString()));
					} else if (line.getIsUseParameter()) { 
						line.setSpec(paramMap);
					} else {
						line.setSpec();
					}
					if (!Objects.isNull(line.getEdcItem())) {
						line.getEdcItem().getObjectRrn();
					}
				}
			} else if (edcSet instanceof EdcBinSet) {
				EdcBinSet binSet = (EdcBinSet)edcSet;
				binSet.setSpec(paramMap);
				for (EdcBinSetLine line : binSet.getBinSetLines()) {
					if (line.getIsUsePartSpec() && (specMap != null)
							&& !StringUtil.isEmpty(line.getLslString())
							&& specMap.containsKey((line.getLslString()))) {
						line.setSpec(specMap.get(line.getLslString()));
					} else if (line.getIsUseParameter()) { 
						line.setSpec(paramMap);
					} else {
						line.setSpec();
					}
				}
			} else if (edcSet instanceof EdcTextSet) {
				EdcTextSet textSet = (EdcTextSet)edcSet;
				for (EdcTextSetLine line : textSet.getTextSetLines()) {
				}
			}
			return edcSet;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 保存EDC数据采集的数据
	* <p><br>
     *
	* @param List<EdcData> 采集到的数据,每一个采集Item对应一个EdcData
	* @param edcFlag 为T、D分别对应临时保存和保存
	* @param edcFrom 为LOT、OFFLINELOT或GENERAL对应Lot数据采集、离线Lot数据采集或一般数据采集
	* @param edcSetcurrent 当前采集数据对应的EdcSetCurrent
	* @param SessionContext
	* @return List<EdcData> 保存后的数据
	*/ 
	public EdcResult saveData(List<EdcData> dcDatas, String edcFlag,
			String edcFrom, boolean isReCalSubgroupPlan, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			EdcData currentData = dcDatas.get(0);
            AbstractEdcSet edcSet = em.find(AbstractEdcSet.class, currentData.getEdcSetRrn());
            
			//在线Lot数据采集,删除上次可能的临时数据采集的数据
			if (EdcData.EDCFROM_LOT.equalsIgnoreCase(edcFrom) || EdcData.EDCFROM_MLOT.equalsIgnoreCase(edcFrom)) {
				EdcData edcData = dcDatas.get(0);
				
				String whereClause;
				
				boolean isLot = false;
				boolean isComponent = false;
				if (edcSet.getIsByLotEdc()) {
					isLot = true;
					whereClause = " lotRrn = :lotRrn ";
				} else if (edcData.getBatchId() != null) {
					isLot = false;
					whereClause = " batchId = :batchId ";
				} else if (edcSet.getIsByComponentEdc()) {
					isLot = false;
					isComponent = true;
					whereClause = " lotRrn = :lotRrn AND componentList = :componentUnitId";
				} else {
					isLot = true;
					whereClause = " lotRrn = :lotRrn ";
				}
				whereClause = whereClause + " AND edcSetRrn = :edcSetRrn AND isTemp = 'Y' ";
				String deleteSql = " DELETE FROM EdcData WHERE " + whereClause;
				Query query = em.createQuery(deleteSql.toString());
				if (isLot) {
					query.setParameter("lotRrn", edcData.getLotRrn());
				} else if (isComponent) {
					query.setParameter("lotRrn", edcData.getLotRrn());
					query.setParameter("componentUnitId", edcData.getComponentList());
				} else {
					query.setParameter("batchId", edcData.getBatchId());
				}
				query.setParameter("edcSetRrn", edcData.getEdcSetRrn());
				query.executeUpdate();				
			}
			
			EdcData judgeData = null;
			if (edcSet instanceof EdcTextSet) {
				//文本采集时,人工判断结果保存在特殊的DCData中
				for (EdcData dcData : dcDatas) {
					if (EdcTextSet.ITEM_JUDGE_BY_MANUAL.equals(dcData.getItemName())) {
						judgeData = dcData;
						break;
					}
				}
				if (judgeData != null) {
					dcDatas.remove(judgeData);
				}
			}
			
			// Formula处理
			if (edcSet instanceof EdcItemSet) {
				// 拿出EDC Set中所有的Formula Line，并根据SEQ排序
				List<EdcItemSetLine> formulaLines = ((EdcItemSet) edcSet).getItemSetLines().stream()
						.filter(l -> EdcItemSetLine.DATA_TYPE_FORMULA.equals(l.getDataType()))
						.sorted(Comparator.comparing(EdcItemSetLine::getSeqNo))
						.collect(Collectors.toList());					
				
				Lot lot = null;
				List<Lot> lots = dcDatas.get(0).getLots();
				if (CollectionUtils.isNotEmpty(lots)) {
					lot = lots.get(0);
				}
				
				for (EdcItemSetLine formulaLine : formulaLines) {
					if (formulaLine.getIsProcessFormula()) {
						List<EdcData> processHisDatas = Lists.newArrayList();
						// 使用Map是为了避免过多的重复查询数据
						Map<String, FormulaVariable> selectDataVariableMap = Maps.newHashMap();
						
						// 检查公式中是否有本次数据作为变量，没有直接退出流程公式逻辑
						// 此检查是为了排除一些不必要的计算，并提升计算结果的准确性
						for (FormulaVariable variable : formulaLine.getFormulaVariables()) {
							String edcSetName = variable.getProcessName();
							String itemName = variable.getItemName();
							
							Optional<EdcData> f = dcDatas.stream().filter(
									d -> d.getEdcSetName().equals(edcSetName) && d.getItemName().equals(itemName))
									.findFirst();
							if (f.isPresent()) {
								EdcData cloneData = (EdcData) f.get().clone();
								processHisDatas.add(cloneData);
							} else {
								selectDataVariableMap.put(edcSetName + ":" + itemName, variable);
							}
						}
						
						if (CollectionUtils.isNotEmpty(processHisDatas)) {
							
							boolean notMatch = false;
							// 查询数据
							for (FormulaVariable variable : selectDataVariableMap.values()) {
								String edcSetName = variable.getProcessName();
								String itemName = variable.getItemName();
								
								List<EdcData> processHisData = getLastData(sc.getOrgRrn(), null, lot.getObjectRrn(),
										null, edcSetName, itemName);
								
								if (CollectionUtils.isNotEmpty(processHisData)) {
									EdcData cloneData = (EdcData) processHisData.get(0).clone();
									processHisDatas.add(cloneData);
								} else {
									notMatch = true;
									break;
								}
							}
							
							// 计算流程结果
							if (!notMatch) {
								EdcData formulaData = FormulaUtil.calculateEdcData(
										lot, (EdcItemSet) edcSet, formulaLine, processHisDatas, formulaLine.getFormulaVariables(), null, sc);
								
								if (formulaData != null) {
									dcDatas.add(formulaData);
								} else {
									logger.error("EDC formula calculate failed! EDC set name<" + edcSet.getName()
											+ "> Set line name<" + formulaLine.getName() + ">.");
								}
							}
						}
						
					} else {
						// 使用克隆数据进行计算，以免对原有数据造成错误修改
						List<EdcData> varialbleDatas = Lists.newArrayList();
						for (EdcData edcData : dcDatas) {
							EdcData cloneData = (EdcData) edcData.clone();
							varialbleDatas.add(cloneData);
						}
						
						EdcData formulaData = FormulaUtil.calculateEdcData(
								lot, (EdcItemSet) edcSet, formulaLine, varialbleDatas, formulaLine.getFormulaVariables(), null, sc);
						if (formulaData != null) {
							dcDatas.add(formulaData);
						} else {
							logger.error("EDC formula calculate failed! EDC set name<" + edcSet.getName()
							+ "> Set line name<" + formulaLine.getName() + ">.");
						}
					}
				}
			}
			
			//保存EdcData,包括已完成数据和临时数据
			for (EdcData dcData : dcDatas) {
				dcData.setEdcFrom(edcFrom);
				dcData.setIsActive(true);
				dcData.setCreatedBy(sc.getUserName());
				dcData.setCreated(new Date()); 
				dcData.setUpdatedBy(sc.getUserName());
				dcData.setHisSeq(sc.getTransRrn());
				dcData.setMeasureTime(sc.getTransTime());
				if (!(edcSet instanceof EdcTextSet)) {
					dcData.setDcDataAvg(DBUtil.toDouble(dcData.buildDcDataAvg()));
				}
				
				em.persist(dcData);
			}
			
			//不是临时数据采集,需要对采集结果进行处理
			if (EdcSetCurrent.FLAG_DONE.equals(edcFlag)) {
                Boolean isHoldLot = false;
                Boolean isOos = false;
                String judge = "";
 				//对EDC检查进行处理(即使要进行SPC检查,也需要进行EDC检查)
				//只检查OOS
				List<LotHold> lotHolds = new ArrayList<LotHold>();
				
				if ((edcSet instanceof EdcTextSet) && (judgeData != null)) {
					judge = judgeData.getJudge1();
					if (EdcData.JUDGE_NG.equals(judgeData.getJudge1())) {
						EdcTextSet textSet = (EdcTextSet)edcSet;
						if (textSet.getIsHoldLot()) {
							LotHold lotHold = new LotHold();
							lotHold.setHoldCode(textSet.getHoldCode());
							lotHold.setHoldReason(textSet.getHoldReason());
							lotHold.setHoldOwner(textSet.getHoldOwner());
							lotHolds.add(lotHold);
							isHoldLot = true;
						}
					} 
				} else {
					for (EdcData dcData : dcDatas) {
						if (edcSet instanceof EdcItemSet) {
							//根据EDC结果计算Attribute
							EdcItemSetLine line = ((EdcItemSet)edcSet).getLine(dcData.getItemName());
							dcData.calculateAttributeValues(line);
						}

						if (edcSet instanceof EdcItemSet) {
							EdcItemSetLine line = ((EdcItemSet)edcSet).getLine(dcData.getItemName());
							//如果非人工判定
							if (EdcData.JUDGE_OK.equals(dcData.getJudge1())) {
								//如果人工判断为OK,则不再检查是否OOS
								continue;
							} else if (EdcData.JUDGE_NG.equals(dcData.getJudge1())) {
								//如果人工判断为NG
								//只要一个NG就是NG
								judge = dcData.getJudge1();
								if (edcSet instanceof EdcItemSet) {
									if (line != null && line.getIsHoldLot()) {
										LotHold lotHold = new LotHold();
										lotHold.setHoldCode(line.getHoldCode());
										lotHold.setHoldReason(line.getHoldReason());
										lotHold.setHoldOwner(line.getHoldOwner());
										lotHolds.add(lotHold);
										isHoldLot = true;
										dcData.setIsHoldLot(true);
									}
								}
							} else {
								//Item类型的数据采集
								Boolean lineOos = dcData.getIsOos();  
								if (line != null && line.getIsHoldLot() && lineOos) {
									LotHold lotHold = new LotHold();
									lotHold.setHoldCode(line.getHoldCode());
									lotHold.setHoldReason(line.getHoldReason());
									lotHold.setHoldOwner(line.getHoldOwner());
									lotHolds.add(lotHold);
									isHoldLot = true;
									isOos = true;
									dcData.setIsHoldLot(true);
								}
							}
						} else if (edcSet instanceof EdcBinSet) {
							EdcBinSet binSet = (EdcBinSet)edcSet;
							if (EdcData.JUDGE_NG.equals(dcData.getJudge1())) {
								//如果人工判断为NG
								judge = dcData.getJudge1();
								if (edcSet instanceof EdcBinSet) {
									if (binSet != null && binSet.getIsHoldLot()) {
										LotHold lotHold = new LotHold();
										lotHold.setHoldCode(binSet.getHoldCode());
										lotHold.setHoldReason(binSet.getHoldReason());
										lotHold.setHoldOwner(binSet.getHoldOwner());
										lotHold.setHoldComment("Type:MANUAL; Judge1:NG;");
										lotHolds.add(lotHold);
										isHoldLot = true;
										dcData.setIsHoldLot(true);
									}
								}
							} 

							//BIN类型的数据采集(会将数据组合成一笔EDCData)
							String[] oosList = null;
							if (dcData.getOosList() != null && !"".equals(dcData.getOosList())) {
								oosList = dcData.getOosList().split(";");
								if (oosList.length > 0) {
									isOos = true;
								}
							}
							
							if (oosList != null  && !edcSet.getIsMultiInput()) {
								//非多次采集时才进行检查
								for (String oos : oosList) {
									int oosIndex = Integer.parseInt(oos.trim());
									if (oosIndex == 1 && !edcSet.getIsByComponent()) {
										//整体良率超标,且按组件收集不检查
										if (binSet.getIsHoldLot()) {
											LotHold lotHold = new LotHold();
											lotHold.setHoldCode(binSet.getHoldCode());
											lotHold.setHoldReason(binSet.getHoldReason());
											lotHold.setHoldOwner(binSet.getHoldOwner());
											String lsl = binSet.getLslString() == null ? "" : binSet.getLslString();
											String usl = binSet.getUslString() == null ? "" : binSet.getUslString();
											BigDecimal goodQty = dcData.getTotalQty().subtract(dcData.getBadQty());
											BigDecimal totalQty = dcData.getTotalQty();
											BigDecimal yield = goodQty.divide(totalQty, 5, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
											lotHold.setHoldComment("Type:PERCENT; Name:TOTAL"+ "; LSL/USL:" + lsl + "/" +  usl + "; "
													+ "Total/Bad:" + dcData.getTotalQty() + "/" + dcData.getBadQty() + "; Yield:" + yield);
											lotHolds.add(lotHold);
											isHoldLot = true;
											dcData.setIsHoldLot(true);
										}
									} else if (oosIndex != 1) {
										//单个BIN良率超标,按组件收集也检查
										List<EdcBinSetLine> lines = binSet.getBinSetLines();
										//OOS记录从1开始,而且第一条记录为Total记录
										//因此对应line的记录应减去2
										EdcBinSetLine line = lines.get(oosIndex - 2);
										if (line.getIsHoldLot()) {
											LotHold lotHold = new LotHold();
											lotHold.setHoldCode(line.getHoldCode());
											lotHold.setHoldReason(line.getHoldReason());
											lotHold.setHoldOwner(line.getHoldOwner());
											String lsl = line.getLslString() == null ? "" : line.getLslString();
											String usl = line.getUslString() == null ? "" : line.getUslString();
											String dataArr[] =  dcData.getDcData().split(";");
											BigDecimal checkQty =  new BigDecimal(dataArr[oosIndex-1]);
											BigDecimal totalQty = dcData.getTotalQty();
											BigDecimal yield = checkQty.divide(totalQty, 5, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
											lotHold.setHoldComment("Type:" + line.getSpecType()
													+ "; Name:"+ line.getName() + "; LSL/USL:" + lsl + "/" +  usl
													+ "; Total/Quantity:" + dcData.getTotalQty() + "/" + checkQty + "; Yield:" + yield);
											lotHolds.add(lotHold);
											isHoldLot = true;
											dcData.setIsHoldLot(true);
										}
									} 
								}
							} 
						} else if (edcSet instanceof EdcAQLSet) {
							EdcAQLSet edcAQLSet = (EdcAQLSet) edcSet;
							//如果非人工判定
							if (EdcData.JUDGE_OK.equals(dcData.getJudge1())) {
								//如果人工判断为OK,则不再检查是否OOS
								continue;
							} else if (EdcData.JUDGE_NG.equals(dcData.getJudge1())) {
								//如果人工判断为NG
								//只要一个NG就是NG
								judge = dcData.getJudge1();
								if (edcSet instanceof EdcAQLSet) {
									if (edcAQLSet != null && edcAQLSet.getIsHoldLot()) {
										LotHold lotHold = new LotHold();
										lotHold.setHoldCode(edcAQLSet.getHoldCode());
										lotHold.setHoldReason(edcAQLSet.getHoldReason());
										lotHold.setHoldOwner(edcAQLSet.getHoldOwner());
										lotHolds.add(lotHold);
										isHoldLot = true;
										dcData.setIsHoldLot(true);
									}
								}
							}  else {
								//处理是否Hold Lot等
							    if (dcData.getIsOos() && edcAQLSet.getIsHoldLot()) {
									LotHold lotHold = new LotHold();
									lotHold.setHoldCode(edcAQLSet.getHoldCode());
									lotHold.setHoldReason(edcAQLSet.getHoldReason());
									lotHold.setHoldOwner(edcAQLSet.getHoldOwner());
									lotHolds.add(lotHold);
									isHoldLot = true;
									dcData.setIsHoldLot(true);
								}
							}
						}
					}
				}
				
				if (edcSet instanceof EdcAQLSet) {
//				     EdcAQLSet edcAQLSet = (EdcAQLSet) edcSet;
//				     AqlSamplingPlanInstance aqlSamplingPlanInstance = getAqlSamplingPlanInstance(sc.getOrgRrn(), edcAQLSet, dcDatas.get(0).getPartName());
//                     if (aqlSamplingPlanInstance== null) {
//                         throw new ClientException("edc.aql_sample_instance_null");
//                     }
//                     convertSamplingPlanInstanceState(aqlSamplingPlanInstance.getObjectRrn(), AqlSamplingPlan.SAMPLE_PLAN_TYPE, Boolean.TRUE, Boolean.FALSE, sc);
                 }
				
				EdcResult spcResult = null;
				if (!(edcSet instanceof EdcTextSet) && MesCfMod.isSendSpcEdcData(sc.getOrgRrn(), sysParamManager)) {
					//执行SPC检查
				    SendMesTransContext sendMesTransContext = new SendMesTransContext();
				    Map<String, Object> requestMap = new HashMap<String, Object>();
				    requestMap.put(SendMesTransContext.KEY_EDCDATAS, dcDatas);
				    sendMesTransContext.setRequestMap(requestMap);
				    sendMesTransContext.setAdManager(adManager);
				    sendMesTransContext.setSessionContext(sc);
				    spcResult = (EdcResult) messageSender.sendRequest(sendMesTransContext, 
							SendMesTransContext.MESSAGE_MES + SpcDataRequest.MESSAGE_NAME, MessageSender.SENDER_SPC_DATA, MessageSender.SENDER_TIMEOUT, sc);
							
					//edcResult不为空说明进行了SPC检查, edcResult为SPC检查结果
					if (spcResult != null && (spcResult.getIsHoldLot() || spcResult.getIsHoldEqp())) {
						List<EquipmentHold> equipmentHolds = Lists.newArrayList();
						for (SpcResultJob job : spcResult.getResultJobs()) {
							if (job.getIsHoldLot()) {
								LotHold lotHold = new LotHold();
								lotHold.setHoldOcapId(job.getOcapId());
								lotHold.setHoldCode(job.getHoldCode());
								lotHold.setHoldReason(job.getJobName() + ";" + job.getHoldReason());
								lotHold.setHoldOwner(job.getHoldOwner());
								lotHolds.add(lotHold);
							}
							if (job.getIsHoldEqp()) {
								EquipmentHold equipmentHold = new EquipmentHold();
								equipmentHold.setHoldOcapId(job.getOcapId());
							    equipmentHold.setHoldCode(job.getHoldCode());
							    equipmentHold.setHoldReason(job.getJobName() + ";" + job.getHoldReason());
							    equipmentHold.setHoldOwner(job.getHoldOwner());
							    equipmentHolds.add(equipmentHold);
							}
						}
						spcResult.setLotHolds(lotHolds);
						spcResult.setEquipmentHolds(equipmentHolds);
					}
				}
				EdcResult edcResult;
				if (spcResult == null) {
					edcResult = new EdcResult();
				} else {
					edcResult = spcResult;
				}
				
				//合并处理结果
				edcResult.setIsHoldLot(isHoldLot || edcResult.getIsHoldLot());
				edcResult.setIsOos(isOos || edcResult.getIsOos());
				edcResult.setJudge1(judge);
				edcResult.setLotHolds(lotHolds);

				edcResult.setOrgRrn(sc.getOrgRrn());
				edcResult.setIsActive(true);
				edcResult.setCreatedBy(sc.getUserName());
				edcResult.setCreated(new Date());
				edcResult.setHisSeq(sc.getTransRrn());
				edcResult.setDcDatas(dcDatas);
				em.persist(edcResult);
				
				//保存EDC数据
				for (EdcData dcData : dcDatas) {
					dcData.setHisSeq(sc.getTransRrn());
					em.merge(dcData);
				}
				
				if (isReCalSubgroupPlan) {
					//重新计算样本计划
					for (EdcData dcData : dcDatas) {
						if (edcSet instanceof EdcItemSet) {
							EdcItemSet itemSet = (EdcItemSet)edcSet;
							for (EdcItemSetLine line : itemSet.getItemSetLines()) {
								if (line.getName().equalsIgnoreCase(dcData.getItemName())) {
									reCalculateSubgroupPlan(line, dcData, false);
									break;
								}
							}
						}
					}
				}
				return edcResult;
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
		return null;
	}
	
	/**
	 * 修改Edc数据,不进行任何相关检查,也不传递给SPC
     *
	 * @param edcData 修改后的edc数据
	 * @param sc
	 */
	@Override
	public EdcData correctEdcData(EdcData edcData, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();

			EdcData oldEdcData = em.find(EdcData.class, edcData.getObjectRrn());
			EdcDataHis his = new EdcDataHis(oldEdcData);
			his.setTransType(EdcDataHis.TRANS_TYPE_CORRECT);
			his.setTransTime(sc.getTransTime());
			em.persist(his);
			
			edcData.setUpdatedBy(sc.getUserName());
			edcData = em.merge(edcData);
			
			return edcData;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 重新计算样本计划
	* @param line 样本line列表
	* @param dcData edc采集信息
	* @param 规则是否为 Ooc
	*/ 
	public void reCalculateSubgroupPlan(EdcItemSetLine line, EdcData dcData, boolean isOoc) throws ClientException {
		try {
			if (line.getIsSubgroupPlan()) {
				EdcSubgroupPlan defaultPlan = null;
				EdcSubgroupPlan violatePlan = null;
				if (EdcItem.DATATYPE_VARIABLE.equals(line.getDataType())) {
					String whereClause = " itemSetLineRrn = " + line.getObjectRrn();
					List<EdcSubgroupPlan> subgroupPlans = adManager.getEntityList(line.getOrgRrn(), 
							EdcSubgroupPlan.class, Integer.MAX_VALUE, whereClause, "");
					for (EdcSubgroupPlan subgroupPlan : subgroupPlans) {
						if (EdcSubgroupPlan.RULELEVEL_DEFALUT.equals(subgroupPlan.getRuleLevel())) {
							defaultPlan = subgroupPlan;
						} else if (EdcSubgroupPlan.RULELEVEL_OOS_COUNT.equals(subgroupPlan.getRuleLevel())) {
							//如果规则为OOSCount
							if (dcData.getIsOos()) {
								String[] oos = dcData.getOosList().split(";");
								if (EdcSubgroupPlan.RULETYPE_NUMBER.equals(subgroupPlan.getRuleType())) {
									//如果Rule为计算所有OOS点的数量
									if (oos.length > subgroupPlan.getCount().intValue()) {
										violatePlan = subgroupPlan;
									}
								} else {
									//如果Rule为计算所有OOS点的百分比数
									String[] datas = dcData.getDcData().split(";");
									if ((oos.length / datas.length) * 100 > subgroupPlan.getCount().doubleValue()) {
										violatePlan = subgroupPlan;
									}
								}
							}
						} else if (EdcSubgroupPlan.RULELEVEL_OOS.equals(subgroupPlan.getRuleLevel())) {
							//如果规则为OOS
							if (dcData.getIsOos()) {
								violatePlan = subgroupPlan;
							}
						} else if (EdcSubgroupPlan.RULELEVEL_OOC.equals(subgroupPlan.getRuleLevel())) {
							//如果规则为OOC
							if (isOoc) {
								violatePlan = subgroupPlan;
							}
						} else if (EdcSubgroupPlan.RULELEVEL_OOS_OOC.equals(subgroupPlan.getRuleLevel())) {
							//如果规则为OOS、如果规则为OOC
							if (dcData.getIsOos() || isOoc) {
								violatePlan = subgroupPlan;
							}
						}
					}
					if (violatePlan != null) {
						//重新设置样本计划,进入SamplePlan阶段
						line.setItem(violatePlan.getItem());
						line.setItemDesc(violatePlan.getItemDesc());
						line.setComp(violatePlan.getComp());
						line.setCompDesc(violatePlan.getCompDesc());
						line.setSite(violatePlan.getSite());
						line.setSiteDesc(violatePlan.getSiteDesc());
						line.setBump(violatePlan.getBump());
						line.setBumpDesc(violatePlan.getBumpDesc());
						
						line.setSampleSize(violatePlan.getSampleSize());
						line.setSubgroupSize(violatePlan.getSubgroupSize());//重置样本数
						line.setSubgroupQualifyCount(line.getSubgroupQualifyRate());//重置采集次数
						em.merge(line);
					}
				} else if (EdcItem.DATATYPE_ATTRIBUTE.equals(line.getDataType())) {
					String whereClause = " itemSetLineRrn = " + line.getObjectRrn();
					List<EdcSubgroupPlan> subgroupPlans = adManager.getEntityList(line.getOrgRrn(), 
							EdcSubgroupPlan.class, Integer.MAX_VALUE, whereClause, "");
					for (EdcSubgroupPlan subgroupPlan : subgroupPlans) {
						if (EdcSubgroupPlan.RULELEVEL_DEFALUT.equals(subgroupPlan.getRuleLevel())) {
							defaultPlan = subgroupPlan;
						} else {
							//如果Rule为计算所有Bad的数量
							//如果Rule为计算所有Bad的百分比数
							if (dcData.getBadQty().divide(dcData.getTotalQty(),5,BigDecimal.ROUND_HALF_EVEN).
									compareTo(subgroupPlan.getCount().divide(new BigDecimal(100))) > 0) {
								violatePlan = subgroupPlan;
							}
						}
					}
					if (violatePlan != null) {
						//如果违反SubgroupPlan
						line.setSubgroupQualifyCount(line.getSubgroupQualifyRate());//重置采集次数
						line.setSubgroupSize(violatePlan.getSubgroupSize());//重置样本数
						line.setSampleSize(violatePlan.getSampleSize());
						line.setSampleLevel(violatePlan.getRuleType());
						em.merge(line);
					}
				}
				if (violatePlan == null) {
					//如果没有违反SubgroupPlan
					if (line.getSubgroupQualifyCount() <= 1) {
						line.setSubgroupQualifyCount(0L);
						//重新设置样本计划
						line.setItem(defaultPlan.getItem());
						line.setItemDesc(defaultPlan.getItemDesc());
						line.setComp(defaultPlan.getComp());
						line.setCompDesc(defaultPlan.getCompDesc());
						line.setSite(defaultPlan.getSite());
						line.setSiteDesc(defaultPlan.getSiteDesc());
						line.setBump(defaultPlan.getBump());
						line.setBumpDesc(defaultPlan.getBumpDesc());
						//当SubgroupQualifyCount等于0
						line.setSampleSize(defaultPlan.getSampleSize());
						line.setSubgroupSize(defaultPlan.getSubgroupSize());
						line.setSampleLevel(defaultPlan.getRuleType());
					} else {
						line.setSubgroupQualifyCount(line.getSubgroupQualifyCount() - 1);
					}
					em.merge(line);
				}
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 获得最后一次采集数据
     *
	 * @param orgRrn
	 * @param batchId 如果有batchId则根据batchId查询,否则根据lotRrn查询
	 * @param lotRrn  如果有batchId则根据batchId查询,否则根据lotRrn查询
	 * @param stepName 工步名称,可选栏位
	 * @param edcSetName 数据采集项集名称,可选栏位
	 * @param itemName 数据采集项名称,可选栏位
	 */
	public List<EdcData> getLastData(Long orgRrn, String batchId, Long lotRrn, String stepName, String edcSetName, String itemName) throws ClientException {
		List<EdcData> lastDatas = new ArrayList<EdcData>();
		try {
			StringBuffer sql = new StringBuffer(" SELECT EdcData FROM EdcData EdcData ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			
			if (!StringUtil.isEmpty(stepName)) {
				sql.append(" AND stepName = :stepName");
			}
			
			if (!StringUtil.isEmpty(batchId)) {
				sql.append(" AND batchId = :batchId");
			} else {
				sql.append(" AND lotRrn = :lotRrn");
			}
			if (!StringUtil.isEmpty(edcSetName)) {
				sql.append(" AND edcSetName = :edcSetName");
			}
			if (!StringUtil.isEmpty(itemName)) {
				sql.append(" AND itemName = :itemName");
			}
			sql.append(" ORDER BY measureTime DESC ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			
			if (!StringUtil.isEmpty(stepName)) {
				query.setParameter("stepName", stepName);
			}
			if (!StringUtil.isEmpty(batchId)) {
				query.setParameter("batchId", batchId);
			} else {
				query.setParameter("lotRrn", lotRrn);
			}
			if (!StringUtil.isEmpty(edcSetName)) {
				query.setParameter("edcSetName", edcSetName);
			}
			if (!StringUtil.isEmpty(itemName)) {
				query.setParameter("itemName", itemName);
			}
			List<EdcData> datas = query.getResultList();
			if (datas.size() > 0) {
				//获得第一笔记录
				if (!StringUtil.isEmpty(itemName)) {
					lastDatas.add(datas.get(0));
				} else if (!StringUtil.isEmpty(edcSetName)) {
					String historySeq = datas.get(0).getHisSeq();
					for (EdcData data : datas) {
						if (historySeq.equals(data.getHisSeq())) {
							lastDatas.add(data);
						}
					}
				} else {
					//可能返回多组edcSet
					Set<String> edcSetNames = new HashSet<String>();
					for (EdcData data : datas) {
						edcSetNames.add(data.getEdcSetName());
					}
					for (String name : edcSetNames) {
						String historySeq = "";
						for (EdcData data : datas) {
							if (name.equals(data.getEdcSetName())) {
								historySeq = data.getHisSeq();
								break;
							}
						}
						for (EdcData data : datas) {
							if (historySeq.equals(data.getHisSeq())) {
								lastDatas.add(data);
							}
						}
					}
				}
			}
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
		}
		return lastDatas;
	}
	
	/**
	 * 获得最后一次数据采集的结果
     *
	 * @param orgRrn
	 * @param batchId 如果有batchId则根据batchId查询,否则根据lotRrn查询
	 * @param lotRrn  如果有batchId则根据batchId查询,否则根据lotRrn查询
	 * @param stepName 工步名称,必须栏位
	 * @param edcSetName 数据采集项集名称,可选栏位
	 * @param itemName 数据采集项名称,可选栏位
	 */
	public List<EdcResult> getLastResult(Long orgRrn, String batchId, Long lotRrn, String stepName, String edcSetName, String itemName) throws ClientException {
		List<EdcResult> lastResults = new ArrayList<EdcResult>();
		try {
			List<EdcData> lastDatas = getLastData(orgRrn, batchId, lotRrn, stepName, edcSetName, itemName);
			if (lastDatas != null && !lastDatas.isEmpty()) {
				Set<String> historySeqs = new HashSet<String>();
				for (EdcData data : lastDatas) {
					historySeqs.add(data.getHisSeq());
				}
				StringBuffer sql = new StringBuffer(" SELECT EdcResult FROM EdcResult EdcResult ");
				sql.append(" WHERE ");
				sql.append(ADBase.BASE_CONDITION_N);
				sql.append(" AND hisSeq IN (:hisSeq)");
				
				Query query = em.createQuery(sql.toString());
				query.setParameter("orgRrn", orgRrn);
				query.setParameter("hisSeq", historySeqs);
				lastResults = query.getResultList();
			}
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
		}
		return lastResults;
	}
	
	/**
	 * 获得数据采集数据，有batchid则按batch获取，无则按lotRrn
     *
	 * @param orgRrn 区域好
	 * @param batchId 如果有batchId则根据batchId查询,否则根据lotRrn查询
	 * @param lotRrn  如果有batchId则根据batchId查询,否则根据lotRrn查询
	 * @param edcSetRrn 采集项集主键,必须栏位
	 */
	public List<EdcData> getData(Long orgRrn, String batchId, Long lotRrn, 
			Long edcSetRrn) throws ClientException {
		try {
			String whereClause;
			String orderBy = " seqNo ASC";
			if (batchId != null) {
				whereClause = " batchId = '" + batchId + "'";
			} else {
				whereClause = " lotRrn = '" + lotRrn + "'";
			}
			whereClause = whereClause + " AND edcSetRrn = '" + edcSetRrn + "' ";
			
			List<EdcData> datas = adManager.getEntityList(orgRrn, EdcData.class, Integer.MAX_VALUE, whereClause, orderBy);
			return datas;
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	
	/**
	 * 获得样本计划
	 * @param orgRrn 区域号
	 * @param lineRrn 采集项主键
	 */
	public List<EdcSubgroupPlan> getSubgroupPlanList(Long orgRrn, Long lineRrn) throws ClientException {
		try {
			String whereClause = " itemSetLineRrn = " + lineRrn;
			List<EdcSubgroupPlan> datas = adManager.getEntityList(
					orgRrn, EdcSubgroupPlan.class, Integer.MAX_VALUE, whereClause, "");
			return datas;
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据Lot获得改批次在当前工步对应的EdcTecn
     *
	 * @param orgRrn
	 * @param lotId 批次
	 */
	public List<EdcTecn> getTecnByLotCurrentStep(long orgRrn, String lotId, String stepName) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" SELECT EdcTecn FROM EdcTecn EdcTecn ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND lotId = :lotId ");
			sql.append(" AND stepName = :stepName ");
			sql.append(" AND actionType = '");
			sql.append(EdcTecn.ACTION_TYPE_CURRENT_STEP);
			sql.append("' ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("lotId", lotId);
			query.setParameter("stepName", stepName);
			List<EdcTecn> tecns = query.getResultList();
			return tecns;
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 移除批次当前工步的EdcTecn,在TrackOut时调用(Abort时会保留EdcTecn)
     *
	 * @param orgRrn
	 * @param lotId 批号
	 */
	public void removeTecnByLotCurrentStep(String lotId, String stepName, SessionContext sc) throws ClientException {
		try {
			List<EdcTecn> existTecns = getTecnByLotCurrentStep(sc.getOrgRrn(), lotId, stepName);
			List<ContextValue> contextValues = Lists.newArrayList();
			for (EdcTecn existTecn : existTecns) {
				em.remove(existTecn);
				if (existTecn.getContextValueRrn() != null) {
					ContextValue contextValue = ctxManager.getEntityManager().find(ContextValue.class, existTecn.getContextValueRrn());
					contextValues.add(contextValue);
				}
			}
			if (CollectionUtils.isNotEmpty(contextValues)) {
				ctxManager.cudContextValues(null, contextValues, sc);
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据Context值获得对应的EdcTecn
     *
	 * @param lot 批次
	 * @param contextMap context参数
	 */
	public List<EdcTecn> getTecnByContext(Lot lot, Map<String, String> contextMap) throws ClientException {
		try {
			Map<String, String> map = new HashMap<String, String>();
			if (contextMap != null && !contextMap.isEmpty()) {
				map.putAll(contextMap);
			}
			
			map.put(EdcTecnContext.CONTEXT_FIELD_LOTID, lot.getLotId());
			map.put(EdcTecnContext.CONTEXT_FIELD_STEPNAME, lot.getStepName());
			map.put(EdcTecnContext.CONTEXT_FIELD_PARTNAME, lot.getPartName());
			map.put(EdcTecnContext.CONTEXT_FIELD_LOTTYPE, lot.getLotType());
			map.put(EdcTecnContext.CONTEXT_FIELD_WORKORDER, lot.getWoId());
			map.put(EdcTecnContext.CONTEXT_FIELD_EQUIPMENT, lot.getEquipmentId());
			map.put(EdcTecnContext.CONTEXT_FIELD_CUSTOMER, lot.getCustomerCode());

			List<EdcTecn> tecns = new ArrayList<EdcTecn>();
			List<ContextValue> values = ctxManager.getContextValues(lot.getOrgRrn(), EdcTecnContext.CONTEXT_NAME, map);
			for (ContextValue value : values) {
				EdcTecn tecn = getEdcTecnByContext(value.getObjectRrn(), true);
				tecns.add(tecn);
			}
			return tecns;
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据Context值获得对应的EdcTecn
     *
	 * @param contextRrn contextValue的objectRrn
	 * @param isThrowException EdcTecn不存在时是否抛出异常
	 */
	public EdcTecn getEdcTecnByContext(long contextRrn, boolean isThrowException) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" SELECT EdcTecn FROM EdcTecn EdcTecn ");
			sql.append(" WHERE ");
			sql.append(" contextValueRrn = :contextValueRrn ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("contextValueRrn", contextRrn);
			List<EdcTecn> tecns = query.getResultList();
			if (tecns == null || tecns.size() == 0) {
				if (isThrowException) {
					throw EDCExceptionBundle.bundle.EdcTecnNotExist();
				}
				return null;
			}
			EdcTecn tecn = tecns.get(0);
			return tecn;
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
     * 初始化对应的SamplingPlanCalc
     *
     * @param samplePlanType 抽样类型(AQL/INTERVAL)
     * @param samplePlanInstanceRrn
     * @param isQualified 当前样本是否合格
     * @param isRetest 本次样本是否为重测(目前重测与正常数据一样处理)
     * @param groupSize1 当样本状态的S时,表示由S到T的批量大小;当样本状态的T时,表示由T到S的批量大小;当样本状态的L时,表示由L到S的批量大小
     * @param groupSize2 只有当样本状态的S时有值,表示由S到L的批量大小
     */
	public SamplingPlanCalc initSamplingPlanCalc(long orgRrn, String samplePlanType, 
			long samplePlanInstanceRrn, boolean isQualified, boolean isRetest, Integer groupSize1, Integer groupSize2) {
		try {
			SamplingPlanCalc planCalc = new SamplingPlanCalc();
			planCalc.setOrgRrn(orgRrn);
			planCalc.setIsActive(true);
			planCalc.setSamplePlanType(samplePlanType);
			planCalc.setSamplePlanInstanceRrn(samplePlanInstanceRrn);
			if (isQualified) {
				planCalc.setQualifiedCount(1);
				planCalc.setUnqualifiedCount(0);
			} else {
				planCalc.setQualifiedCount(0);
				planCalc.setUnqualifiedCount(1);
			}
			if (groupSize1 != null) {
				planCalc.setGroupSize1(groupSize1);
				planCalc.setGroupQualifiedString1(SamplingPlanCalc.addGroupIsQualified(
						planCalc.getGroupQualifiedString1(), isQualified, planCalc.getGroupSize1()));
			}
			if (groupSize2 != null) {
				planCalc.setGroupSize2(groupSize2);
				planCalc.setGroupQualifiedString2(SamplingPlanCalc.addGroupIsQualified(
						planCalc.getGroupQualifiedString2(), isQualified, planCalc.getGroupSize2()));

			} 
			em.persist(planCalc);
			
			return planCalc;
         } catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
        }
	}
	
	/**
     * 获取对应的SamplingPlanCalc
     *
     * @param samplePlanType 抽样类型(AQL/INTERVAL)
     * @param samplePlanInstanceRrn 抽样计划Rrn
     */
	public SamplingPlanCalc getSamplingPlanCalc(long orgRrn, String samplePlanType, long samplePlanInstanceRrn) {
		try {
			StringBuffer sql = new StringBuffer(" SELECT SamplingPlanCalc FROM SamplingPlanCalc SamplingPlanCalc ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND ");
			sql.append(" samplePlanType = :samplePlanType");
			sql.append(" AND samplePlanInstanceRrn = :samplePlanInstanceRrn");
			Query query = em.createQuery(sql.toString());

			query.setParameter("orgRrn", orgRrn);
			query.setParameter("samplePlanType", samplePlanType);
			query.setParameter("samplePlanInstanceRrn", samplePlanInstanceRrn);
			
			List<SamplingPlanCalc> planCalcs = query.getResultList();
			if (planCalcs != null && planCalcs.size() > 0) {
				return planCalcs.get(0);
			}
			return null;
        } catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
        }
	}
	
	/**
     * 重新计算对应的SamplingPlanCalc
     *
     * @param samplePlanType 抽样类型(AQL/INTERVAL)
     * @param samplePlanInstanceRrn
     * @param isQualified 当前样本是否合格
     * @param isRetest 本次样本是否为重测(目前重测与正常数据一样处理)
     */
	public SamplingPlanCalc calculateSamplingPlanCalc(long orgRrn, String samplePlanType, 
			long samplePlanInstanceRrn, boolean isQualified, boolean isRetest) {
		try {
			SamplingPlanCalc planCalc = getSamplingPlanCalc(orgRrn, samplePlanType, samplePlanInstanceRrn);
			if (planCalc == null) {
				return null;
			}
			if (isQualified) {
				planCalc.setQualifiedCount(planCalc.getQualifiedCount() + 1);
				//连续不合格数清0
				planCalc.setUnqualifiedCount(0);
			} else {
				planCalc.setUnqualifiedCount(planCalc.getUnqualifiedCount() + 1);
				//连续合格数清0
				planCalc.setQualifiedCount(0);
			}
			if (planCalc.getGroupSize1() != null) {		
				planCalc.setGroupQualifiedString1(SamplingPlanCalc.addGroupIsQualified(
						planCalc.getGroupQualifiedString1(), isQualified, planCalc.getGroupSize1()));
			}
			if (planCalc.getGroupSize2() != null) {
				planCalc.setGroupQualifiedString2(SamplingPlanCalc.addGroupIsQualified(
						planCalc.getGroupQualifiedString2(), isQualified, planCalc.getGroupSize2()));
			}
			planCalc = em.merge(planCalc);
			
			return planCalc;
         } catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
        }
	}
	
	/**
     * 当SamplingPlan状态变化时,需要对SamplingPlanCalc进行重置
     *
     * @param samplePlanType 抽样类型(AQL/INTERVAL)
     * @param samplePlanInstanceRrn
     * @param groupSize1 向下转换类型
     * @param groupSize2 向上转换类型
     */
	public SamplingPlanCalc resetSamplingPlanCalc(long orgRrn, String samplePlanType, long samplePlanInstanceRrn, Integer groupSize1, Integer groupSize2) {
		try {
			SamplingPlanCalc planCalc = getSamplingPlanCalc(orgRrn, samplePlanType, samplePlanInstanceRrn);
			if (planCalc == null) {
				return null;
			}
			
			planCalc.setQualifiedCount(0);
			planCalc.setUnqualifiedCount(0);
			
			planCalc.setGroupSize1(groupSize1);
			planCalc.setGroupQualifiedString1(null);

			planCalc.setGroupSize2(groupSize2);
			planCalc.setGroupQualifiedString2(null);
			
			planCalc = em.merge(planCalc);
			
			return planCalc;
         } catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
        }
	}
		
	/**
	 * 判断对抽样计划实例是否要进行状态切换，并对一些属性进行重置，如果实例状态已切换，也要对SamplingPlanCalc进行重置
     *
	 * @param samplingPlanInstance 实例
	 * @param samplePlanType 抽样类型
	 * @param isQualified 是否合格
	 * @param isRetest 本次样本是否为重测(目前重测与正常数据一样处理)
	 * @param sc
	 */
	public void convertAqlSamplingPlanInstanceState(Long samplingPlanInstanceRrn, boolean isQualified, boolean isRetest, SessionContext sc) throws ClientException {
		try {	
			AqlSamplingPlanInstance samplingPlanInstance = em.find(AqlSamplingPlanInstance.class, samplingPlanInstanceRrn);		
			if (samplingPlanInstance != null) {	
				AqlSamplingPlan samplingPlan =
						(AqlSamplingPlan)getAqlSamplingPlan(sc.getOrgRrn(), samplingPlanInstance.getSampleName(), null);
				if (samplingPlan == null) {
					throw EDCExceptionBundle.bundle.SamplingNotExistOrInactive();
				}		
				SamplingPlanCalc samplingPlanCalc = calculateSamplingPlanCalc(sc.getOrgRrn(), AqlSamplingPlan.SAMPLE_PLAN_TYPE, 
						samplingPlanInstance.getObjectRrn(), isQualified, isRetest);
				if (samplingPlanCalc == null) {//不存在就初始一个
					Integer groupSize1 = samplingPlan.getTargetGroupSize1(samplingPlanInstance.getInspectionLevel());
					Integer groupSize2 = samplingPlan.getTargetGroupSize2(samplingPlanInstance.getInspectionLevel());
					samplingPlanCalc = initSamplingPlanCalc(sc.getOrgRrn(), AqlSamplingPlan.SAMPLE_PLAN_TYPE, 
							samplingPlanInstance.getObjectRrn(), isQualified, isRetest, groupSize1, groupSize2);
				}
				//是否需要切换状态
				if (samplingPlan.isNeedCalculate()) { 							
					String targetLevel = samplingPlan.getTargetLevel(samplingPlanInstance.getInspectionLevel(), samplingPlanCalc);
					 //如果换成了新的目标状态，实例的一些属性要重置
					if (!samplingPlanInstance.getInspectionLevel().equals(targetLevel)) {							
						samplingPlanInstance.setInspectionLevel(targetLevel);
						em.merge(samplingPlanInstance);
						
						//对SamplingPlanCalc进行重置
						Integer groupSize1 = samplingPlan.getTargetGroupSize1(samplingPlanInstance.getInspectionLevel());
						Integer groupSize2 = samplingPlan.getTargetGroupSize2(samplingPlanInstance.getInspectionLevel());
						resetSamplingPlanCalc(sc.getOrgRrn(), AqlSamplingPlan.SAMPLE_PLAN_TYPE, samplingPlanInstance.getObjectRrn(), groupSize1, groupSize2);
					}		
				}
			}		
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
     * 保存或更新EdcSamplingPlan
     * @param samplingPlan 抽样计划
	 * @param samplePlanType 抽样类型
	 * @param status 状态
	 * @param sc
     */
    @Override
    public SamplingPlan saveEdcSamplingPlan(SamplingPlan samplingPlan, String status, SessionContext sc) throws ClientException {
        try {
            if (samplingPlan.getObjectRrn() == null) {
            	samplingPlan.setIsActive(true);
            	samplingPlan.setOrgRrn(sc.getOrgRrn());
            	samplingPlan.setCreated(new Date());
            	samplingPlan.setCreatedBy(sc.getUserName());
            	samplingPlan.setUpdated(new Date());
            	samplingPlan.setUpdatedBy(sc.getUserName());
            	samplingPlan.setStatus(status);
            	if (VersionControl.STATUS_ACTIVE.equals(status)) {
            		samplingPlan.setActiveTime(new Date());
            		samplingPlan.setActiveUser(sc.getUserName());
            	}
                List<? extends SamplingPlan> allPlan = adManager.getEntityList(sc.getOrgRrn(), 
                		samplingPlan.getClass(), 1, " name = '" + samplingPlan.getName() + "'", " version desc");
                if (allPlan != null && allPlan.size() > 0) {
                	SamplingPlan lastPlan = allPlan.get(0);
                	samplingPlan.setVersion(lastPlan.getVersion() + 1);
                } else { 
                	samplingPlan.setVersion(1L);
                } 
                em.persist(samplingPlan);   
            } else {            	
            	samplingPlan.setUpdated(new Date());
            	samplingPlan.setUpdatedBy(sc.getUserName());
            	samplingPlan.setStatus(status);
                if (VersionControl.STATUS_ACTIVE.equals(status)) {                  
                    List<? extends SamplingPlan> activeEdcSamplingPlans = adManager.getEntityList(sc.getOrgRrn(), samplingPlan.getClass(), Integer.MAX_VALUE, 
                            " name = '" + samplingPlan.getName() + "' and status = 'Active'", "");
                    for (SamplingPlan activeEdcSamplingPlan : activeEdcSamplingPlans) {
                        activeEdcSamplingPlan.setStatus(VersionControl.STATUS_FROZNE);
                        em.merge(activeEdcSamplingPlan);
                    }
                    
                    samplingPlan.setActiveTime(new Date());
                    samplingPlan.setActiveUser(sc.getUserName());
                }
                em.merge(samplingPlan);
            }
            
            return samplingPlan;
        } catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
        }
    }

    /**
     * 查询SamplingPlan
     *
     * @param orgRrn
     * @param samplingPlanName 抽样计划名称
     * @param samplingPlanVersion 抽样计划版本,为null时取得Active版本
     * @param samplePlanType 抽样计划类型ITV或AQL
     */
    public SamplingPlan getAqlSamplingPlan(long orgRrn, String samplingPlanName, Long samplingPlanVersion) throws ClientException {
        try {
        	StringBuffer sql = new StringBuffer();
        	sql.append(" SELECT AqlSamplingPlan FROM AqlSamplingPlan AqlSamplingPlan ");
    		sql.append(" WHERE ");
    		sql.append(ADBase.BASE_CONDITION_N);
    		sql.append(" AND name = :name");
    		if (samplingPlanVersion == null) {
    			sql.append(" AND status = 'Active'");
    		} else {
        		sql.append(" AND version = :version");
    		}
    		Query query = em.createQuery(sql.toString());
    		query.setParameter("orgRrn", orgRrn);
    		query.setParameter("name", samplingPlanName);
    		if (samplingPlanVersion != null) {
    			query.setParameter("version", samplingPlanVersion);
    		}
    		List<SamplingPlan> samplingPlans = query.getResultList();
    		if (samplingPlans != null && samplingPlans.size() > 0) {
    			return samplingPlans.get(0);
    		}
    		return null;
        } catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
        }
    }

    /**
     * 保存或更新EdcAQLSet
     * @param edcAQLSet
     * @param status 状态
     */
    @Override
    public EdcAQLSet saveEdcAQLSet(EdcAQLSet edcAQLSet, String status, SessionContext sc)
            throws ClientException {
        try {
            if (edcAQLSet.getObjectRrn() == null) {
                edcAQLSet.setIsActive(true);
                edcAQLSet.setOrgRrn(sc.getOrgRrn());
                edcAQLSet.setCreated(new Date());
                edcAQLSet.setCreatedBy(sc.getUserName());
                edcAQLSet.setUpdated(new Date());
                edcAQLSet.setUpdatedBy(sc.getUserName());
                edcAQLSet.setStatus(status);
                List<AbstractEdcSet> allEdcAQLSet = adManager.getEntityList(sc.getOrgRrn(), AbstractEdcSet.class, 1, " name = '" + edcAQLSet.getName() + "'", " version desc");
                if (allEdcAQLSet != null && allEdcAQLSet.size() > 0) {
                    AbstractEdcSet lastEdcAQLSet = allEdcAQLSet.get(0);
                    edcAQLSet.setVersion(lastEdcAQLSet.getVersion() + 1);
                } else { 
                    edcAQLSet.setVersion(1L);
                } 
                em.persist(edcAQLSet);   
            } else {
                edcAQLSet.setUpdated(new Date());
                edcAQLSet.setUpdatedBy(sc.getUserName());
                edcAQLSet.setStatus(status);
                if (VersionControl.STATUS_ACTIVE.equals(status)) {                  
                    List<EdcAQLSet> activeEdcAQLSets = adManager.getEntityList(sc.getOrgRrn(), EdcAQLSet.class, Integer.MAX_VALUE, 
                            " name = '" + edcAQLSet.getName() + "' and status = 'Active'", "");
                    for (EdcAQLSet activeEdcAQLSet : activeEdcAQLSets) {
                        activeEdcAQLSet.setStatus(VersionControl.STATUS_FROZNE);
                        em.merge(activeEdcAQLSet);
                    }

                    edcAQLSet.setActiveTime(new Date());
                    edcAQLSet.setActiveUser(sc.getUserName());
                }
                em.merge(edcAQLSet);
            }
            
            return edcAQLSet;
        } catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
        }
    }

    /**
     * 创建AqlSamplingPlanInstance
     *
     * @param sampleName 抽样计划名称
     * @param edcSetName EDC Set名称
     * @param itemName ITEM 名称
     * @param partName 产品名称
     * @param orgRrn 区域号
     * @return
     * @throws ClientException
     */
    public void createAqlSamplingPlanInstance(EdcAQLSet edcAQLSet, Lot lot) throws ClientException {
        try{
            //获取AqlSamplingPlan
            AqlSamplingPlan aqlSamplingPlan = (AqlSamplingPlan)getAqlSamplingPlan(lot.getOrgRrn(), edcAQLSet.getSamplePlanName(), null);
            if (aqlSamplingPlan == null) {
                throw EDCExceptionBundle.bundle.SamplingPlanNotExistOrInactive();
            }
            //判断AqlSamplingPlanInstance是否存在

            String itemName = null;
            if (AqlSamplingPlan.SAMPLE_TYPE_BYITEM.equals(aqlSamplingPlan.getSampleType())) {
                 itemName = edcAQLSet.getEdcAQLSetLines().get(0).getName();
            }
            AqlSamplingPlanInstance aqlSamplingPlanInstance = getAqlSamplingPlanInstance(lot.getOrgRrn(), edcAQLSet, lot.getPartName());

            //如果AqlSamplingPlanInstance不存在，则需要从新创建一个AqlSamplingPlanInstance
            if (aqlSamplingPlanInstance == null) {
                aqlSamplingPlanInstance = new AqlSamplingPlanInstance();
                aqlSamplingPlanInstance.setIsActive(true);
                aqlSamplingPlanInstance.setOrgRrn(lot.getOrgRrn());
                aqlSamplingPlanInstance.setSampleName(aqlSamplingPlan.getName());
                aqlSamplingPlanInstance.setEdcSetName(edcAQLSet.getName());
                aqlSamplingPlanInstance.setItemName(itemName);
                aqlSamplingPlanInstance.setPartName(lot.getPartName());
                aqlSamplingPlanInstance.setInspectionLevel(aqlSamplingPlan.getInitInspectionLevel());
                em.persist(aqlSamplingPlanInstance);
            }
        } catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
        }
    }

    /**
     * 获取AqlSamplingPlanInstance
     *
     * @param orgRrn
     * @param sampleName 抽样计划名称
     * @param edcSetName EDC Set 名称
     * @param partName 产品名称
     * @param itemName Item 名称
     * @return
     */
    @SuppressWarnings("unchecked")
    public AqlSamplingPlanInstance getAqlSamplingPlanInstance(Long orgRrn, EdcAQLSet edcAQLSet, String partName) {
        try{
            AqlSamplingPlan aqlSamplingPlan = (AqlSamplingPlan)getAqlSamplingPlan(orgRrn, edcAQLSet.getSamplePlanName(), null);
            String itemName = null;
            if (AqlSamplingPlan.SAMPLE_TYPE_BYITEM.equals(aqlSamplingPlan.getSampleType())) {
                itemName = edcAQLSet.getEdcAQLSetLines().get(0).getName();
            }

            StringBuffer sql = new StringBuffer(" SELECT AqlSamplingPlanInstance FROM AqlSamplingPlanInstance AqlSamplingPlanInstance ");
            sql.append(" WHERE ");
            sql.append(ADBase.BASE_CONDITION_N);
            sql.append(" AND sampleName = :sampleName ");
            sql.append(" AND edcSetName = :edcSetName ");
            sql.append(" AND partName = :partName ");
            if (itemName != null && !"".equals(itemName)) {
                sql.append(" AND itemName = :itemName ");
            } else {
                sql.append(" AND (itemName IS NULL OR itemName = '')");
            }
            Query query = em.createQuery(sql.toString());
            query.setParameter("orgRrn", orgRrn);
            query.setParameter("sampleName", edcAQLSet.getSamplePlanName());
            query.setParameter("edcSetName", edcAQLSet.getName());
            query.setParameter("partName", partName);
            if (itemName != null && !"".equals(itemName)) {
                query.setParameter("itemName", itemName);
            }
            List<AqlSamplingPlanInstance> aqlSamplingPlanInstances = query.getResultList();
            if (aqlSamplingPlanInstances != null && aqlSamplingPlanInstances.size() > 0) {    
                return aqlSamplingPlanInstances.get(0);
            }
            return null;
        } catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
        }
    }

    /**
     * 获取 AqlSamplingDetail，用于检测是否超出规格
     *
     * @param tableId 抽样表号
     * @param inspectionLevel T加严/S标准/L放宽
     * @param mainQty 数量
     * @param aql AQL
     * @param orgRrn 区域号
     * @return AqlSamplingDetail
     * @throws ClientException
     */
    @SuppressWarnings("unchecked")
    public AqlSamplingDetail getAqlSamplingDetail(String tableId, String inspectionLevel, BigDecimal mainQty, BigDecimal aql,
            Long orgRrn) throws ClientException {
        try{
            //1：通过tableId + inspectionLevel查找AqlSamplingTable
            StringBuffer sqlAqlSamplingTable = new StringBuffer(" SELECT AqlSamplingTable FROM AqlSamplingTable AqlSamplingTable ");
            sqlAqlSamplingTable.append(" WHERE ");
            sqlAqlSamplingTable.append(ADBase.BASE_CONDITION_N);
            sqlAqlSamplingTable.append(" AND tableId = :tableId");
            sqlAqlSamplingTable.append(" AND inspectionLevel = :inspectionLevel");
            Query queryAqlSamplingTable = em.createQuery(sqlAqlSamplingTable.toString());
            queryAqlSamplingTable.setParameter("orgRrn", orgRrn);
            queryAqlSamplingTable.setParameter("tableId", tableId);
            queryAqlSamplingTable.setParameter("inspectionLevel", inspectionLevel);
            List<AqlSamplingTable> samplingPlans = queryAqlSamplingTable.getResultList();
            if (samplingPlans == null || samplingPlans.size() == 0) {
                return null;
            }

            //2：通过 mainQty 计算出抽样代码
            String codeLetter = null;
            for (AqlSamplingTable aqlSamplingTable : samplingPlans) {
                if (aqlSamplingTable.getGroupSizeMax() != null && !"".equals(aqlSamplingTable.getGroupSizeMax())) {
                    if (mainQty.longValue() >= aqlSamplingTable.getGroupSizeMin() && mainQty.longValue() <= aqlSamplingTable.getGroupSizeMax()) {
                        codeLetter = aqlSamplingTable.getCodeLetter();
                        break;
                    }
                } else {
                    if (mainQty.longValue() > aqlSamplingTable.getGroupSizeMin()) {
                        codeLetter = aqlSamplingTable.getCodeLetter();
                        break;
                    }
                }
            }
            if (codeLetter == null) {
                return null;
            }

            //3：通过codeLetter和aql计算出AqlSamplingDetail
            StringBuffer sqlAqlSamplingDetail = new StringBuffer(" SELECT AqlSamplingDetail FROM AqlSamplingDetail AqlSamplingDetail ");
            sqlAqlSamplingDetail.append(" WHERE ");
            sqlAqlSamplingDetail.append(ADBase.BASE_CONDITION_N);
            sqlAqlSamplingDetail.append(" AND codeLetter = :codeLetter");
            sqlAqlSamplingDetail.append(" AND aql = :aql");
            Query queryAqlSamplingDetail = em.createQuery(sqlAqlSamplingDetail.toString());
            queryAqlSamplingDetail.setParameter("orgRrn", orgRrn);
            queryAqlSamplingDetail.setParameter("codeLetter", codeLetter);
            queryAqlSamplingDetail.setParameter("aql", aql);
            List<AqlSamplingDetail> aqlSamplingDetails = queryAqlSamplingDetail.getResultList();
            if (aqlSamplingDetails != null && aqlSamplingDetails.size() > 0) {
                return aqlSamplingDetails.get(0);
            }
            return null;
        } catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
        }
    }

    /**
     * 通过批次的数量获取AQL Sample的大小
     *
     * @param orgRrn 区域号
     * @param edcAQLSetRrn EdcAQLSet主键
     * @param mainQty 批次总数量
     * @param partName 产品名称
     * @return
     * @throws ClientException
     */
    public AqlSamplingDetail getAqlSamplingSize(Long orgRrn, EdcAQLSet edcAQLSet, BigDecimal mainQty, String partName) throws ClientException {
        try{
            AqlSamplingPlan aqlSamplingPlan = (AqlSamplingPlan) getAqlSamplingPlan(orgRrn, edcAQLSet.getSamplePlanName(), null);

            AqlSamplingPlanInstance aqlSamplingPlanInstance = getAqlSamplingPlanInstance(orgRrn, edcAQLSet, partName);

            AqlSamplingDetail aqlSamplingDetail = getAqlSamplingDetail(aqlSamplingPlan.getSampleTableId(), aqlSamplingPlanInstance.getInspectionLevel(), mainQty, edcAQLSet.getAql(), orgRrn);

            return aqlSamplingDetail;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw ExceptionManager.handleException(logger, e);
        }
    }
    
    
    /**
     * 获得EDC自动数据采集配置
     *
     * @param orgRrn;
     * @param ipAdress 当前电脑IP地址;
     * @param edcItem 数据采集项名称;
     * @param equipmentId 采集设备(在配置中如果定义了设备ID,则以此为准,不检查设备类型);
     * @param equipmentType 采集设备类型;
     */
    public List<EdcAutoConfig> getEdcAutoConfig(long orgRrn, String ipAdress, String edcItem, String equipmentId, String equipmentType) throws ClientException {
		try {
			List<EdcAutoConfig> results = new ArrayList<EdcAutoConfig>();
			List<WorkStation> workStations = adManager.getEntityList(orgRrn, WorkStation.class, 1, " ipAddress = '" + ipAdress + "'", "");
			if (workStations.size() == 0) {
				return null;
			}
			List<EdcAutoConfig> configs; 
			if (StringUtil.isEmpty(edcItem)) {
				configs = adManager.getEntityList(orgRrn, EdcAutoConfig.class, Integer.MAX_VALUE, 
						" stationRrn = " + workStations.get(0).getObjectRrn(), "");
			} else {
				configs = adManager.getEntityList(orgRrn, EdcAutoConfig.class, Integer.MAX_VALUE, 
						" stationRrn = " + workStations.get(0).getObjectRrn() + " AND edcItem = '" + edcItem + "' ", "");
			}
			if (!StringUtil.isEmpty(equipmentId)) {
				//如果指定了设备
				for (EdcAutoConfig config : configs) {
					if (equipmentId.equals(config.getEquipmentId())) {
						results.add(config);
					}
				}
				return results;
			}
			if (!StringUtil.isEmpty(equipmentType)) {
				//如果指定了设备类型
				for (EdcAutoConfig config : configs) {
					if (equipmentType.equals(config.getEquipmentType())) {
						results.add(config);
					}
				}
				return results;
			}
			return configs;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
    }
    

    /**
     * 保存手动抽样计划
     * @param samplingPlan 抽样计划
     * @param sc 上下文;
     */
    @Override
    public ManualSamplingPlan saveManualSamplingPlan(ManualSamplingPlan samplingPlan, SessionContext sc) throws ClientException {
        try {
        	if (samplingPlan.getObjectRrn() == null) {
        		samplingPlan.setOrgRrn(sc.getOrgRrn());
        		samplingPlan.setUpdatedBy(sc.getUserName());
        		samplingPlan.setCreatedBy(sc.getUserName());
        		samplingPlan.setStatus(VersionControl.STATUS_UNFROZNE);
        		samplingPlan.setVersion(1L);
        		
        		// 检查有没有老版本
            	ManualSamplingPlan old = basManager.getLastVersionControl(
            			sc.getOrgRrn(), samplingPlan.getClass(), samplingPlan.getName());
            	if (old != null) {
            		samplingPlan.setVersion(old.getVersion() + 1);
    			}
            	
            	em.persist(samplingPlan);
        	} else {
        		if (!VersionControl.STATUS_UNFROZNE.equals(samplingPlan.getStatus())) {
            		EDCExceptionBundle.bundle.SamplingPlanStateNotAllow();
            	}
        		samplingPlan.setUpdatedBy(sc.getUserName());
        		samplingPlan = em.merge(samplingPlan);
        	}
			return samplingPlan;
        } catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
        }
    }
    
    /**
     * 保存autoconfig数据
     * @param config autoconfig信息
     * @param sc 上下文;
     */
	public EdcAutoConfig saveEdcAutoConfig(EdcAutoConfig config, SessionContext sc) throws ClientException {
		String equipmentId = config.getEquipmentId();
		String edcItem = config.getEdcItem();
		if (config.getObjectRrn() == null) {
			List<EdcAutoConfig> configs = adManager.getEntityList(sc.getOrgRrn(), EdcAutoConfig.class, Integer.MAX_VALUE,
					" equipmentId = '" + equipmentId + "'" +" and  edcItem =  '" + edcItem + "'", "");
			if (configs.size() > 0) {
				throw EDCExceptionBundle.bundle.EdcItemNameOnly(equipmentId);
			} else {
				config.setUpdated(new Date());
				config.setUpdatedBy(sc.getUserName());
				config.setCreated(new Date());
				config.setCreatedBy(sc.getUserName());
				em.persist(config);
			}
		} else {
			config.setUpdated(new Date());
			config.setUpdatedBy(sc.getUserName());
			em.merge(config);
		}
		return config;
	}
	
	/**
	 * 保存 EdcTecn
     *
	 * @param edcTecn EdcTecn信息
	 * @param status 状态
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public EdcTecn saveEdcTecn(EdcTecn edcTecn, String status, SessionContext sc) throws ClientException {
		try {
			if (edcTecn.getObjectRrn() == null) {
				List<EdcTecn> tecns = adManager.getEntityList(sc.getOrgRrn(), EdcTecn.class, Integer.MAX_VALUE, 
						" status <> 'Delete' and name = '" + edcTecn.getName() + "'", "");
				if (tecns != null && tecns.size() > 0) {
					throw EDCExceptionBundle.bundle.EdcTecnNameIsExist();
				}
				
				edcTecn.setOrgRrn(sc.getOrgRrn());
				edcTecn.setCreatedBy(sc.getUserName());
				edcTecn.setUpdatedBy(sc.getUserName());
				if (StringUtil.isEmpty(status)) {
					edcTecn.setStatus(VersionControl.STATUS_UNFROZNE);
				} else {
					edcTecn.setStatus(status);
				}
				EdcTecn lastEdcTecn = basManager.getLastVersionControl(sc.getOrgRrn(), EdcTecn.class, edcTecn.getName());
				if (lastEdcTecn != null) {
					edcTecn.setVersion(lastEdcTecn.getVersion() + 1);
				} else {
					edcTecn.setVersion(1L);
				}
				em.persist(edcTecn);
			} else {
				edcTecn.setUpdatedBy(sc.getUserName());
				if (!StringUtil.isEmpty(status)) {
					edcTecn.setStatus(status);
				}
				em.merge(edcTecn);
			}
			return edcTecn;
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
	    }
	}
	
	/**
	 * 保存EdcTecn与Context关联信息
     *
	 * @param edcTecn
	 * @param contextValue Context关联信息
	 * @param sc
	 * @throws ClientException
	 */
	public EdcTecn saveEdcTecn(EdcTecn edcTecn, ContextValue contextValue, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
				
			ContextValue saveContextValue = contextValue;
			if (contextValue.getObjectRrn() == null) {
				contextValue.setResultValue1(edcTecn.getName());
				saveContextValue = ctxManager.saveContextValue(contextValue, sc);
			} else {
				Context context = ctxManager.getEntityManager().find(Context.class, contextValue.getContextRrn());
				List<ContextValue> repeateValues = ctxManager.getActiveContextValues(sc.getOrgRrn(), context, contextValue);
				if (repeateValues == null || repeateValues.size() == 0) {
					ContextValue cloneContextValue = (ContextValue) contextValue.clone();
					cloneContextValue.setObjectRrn(null);
					cloneContextValue.setResultValue1(edcTecn.getName());
					saveContextValue = ctxManager.saveContextValue(cloneContextValue, sc);
					
					sc.setTransRrn(null);
					sc.buildTransInfo();
					ContextValue oldContextValue = ctxManager.getEntityManager().find(ContextValue.class, contextValue.getObjectRrn());
					List<ContextValue> oldContextValues = new ArrayList<ContextValue>();
					oldContextValues.add(oldContextValue);
					ctxManager.deleteContextValue(oldContextValues, sc);
				}			
			}
			
			edcTecn.setContextRrn(saveContextValue.getContextRrn());
			edcTecn.setContextValueRrn(saveContextValue.getObjectRrn());
			edcTecn.setActiveTime(new Date());
			edcTecn.setActiveUser(sc.getUserName());
			return saveEdcTecn(edcTecn, VersionControl.STATUS_ACTIVE, sc);		
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
	    }
	}
	
	/**
	 * 对已经InActive的EdcTecn进行Active
     *
	 * @param contextValues
	 * @param sc
	 * @throws ClientException
	 */
	public void activeEdcTecn(List<ContextValue> contextValues, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			for (ContextValue contextValue : contextValues) {
				List<EdcTecn> tecns = adManager.getEntityList(sc.getOrgRrn(), EdcTecn.class, Integer.MAX_VALUE, 
						" status = 'InActive' and contextValueRrn = " + contextValue.getObjectRrn() + " and name = '" + contextValue.getResultValue1() + "'", "");
				if (tecns != null && tecns.size() > 0) {
					for (EdcTecn tecn : tecns) {
						EdcTecn edcTecn = em.find(EdcTecn.class, tecn.getObjectRrn());
						edcTecn.setActiveTime(new Date());
						edcTecn.setActiveUser(sc.getUserName());
						saveEdcTecn(edcTecn, VersionControl.STATUS_ACTIVE, sc);
					}
				}
			}
	
			ctxManager.activeContextValue(contextValues, sc);		
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
	    }
	}
	
	/**
	 * 对已经Active的EdcTecn进行InActive
     *
	 * @param contextValues
	 * @param sc
	 * @throws ClientException
	 */
	public void inActiveEdcTecn(List<ContextValue> contextValues, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			for (ContextValue contextValue : contextValues) {
				List<EdcTecn> tecns = adManager.getEntityList(sc.getOrgRrn(), EdcTecn.class, Integer.MAX_VALUE, 
						" status = 'Active' and contextValueRrn = " + contextValue.getObjectRrn() + " and name = '" + contextValue.getResultValue1() + "'", "");
				if (tecns != null && tecns.size() > 0) {
					for (EdcTecn tecn : tecns) {
						EdcTecn edcTecn = em.find(EdcTecn.class, tecn.getObjectRrn());
						saveEdcTecn(edcTecn, VersionControl.STATUS_INACTIVE, sc);
					}
				}
			}
	
			ctxManager.activeContextValue(contextValues, sc);		
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
	    }
	}
	
	/**
	 * 通过ContextValue找到EdcTecn，进行删除
     *
	 * @param contextValue
	 * @param sc
	 * @throws ClientException
	 */
	public void deleteEdcTecn(List<ContextValue> contextValues, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			for (ContextValue contextValue : contextValues) {
				List<EdcTecn> tecns = adManager.getEntityList(sc.getOrgRrn(), EdcTecn.class, Integer.MAX_VALUE, 
						" status = 'Active' and contextValueRrn = " + contextValue.getObjectRrn() + " and name = '" + contextValue.getResultValue1() + "'", "");
				if (tecns != null && tecns.size() > 0) {
					for (EdcTecn tecn : tecns) {
						EdcTecn edcTecn = em.find(EdcTecn.class, tecn.getObjectRrn());
						saveEdcTecn(edcTecn, VersionControl.STATUS_DELETE, sc);
					}
				}
			}
			ctxManager.deleteContextValue(contextValues, sc);
			
		} catch (Exception e) { 
			throw ExceptionManager.handleException(logger, e);
	    }	
	}
	
	/**
	* 批量冻结versionControls
     *
	* @param versionControl 要Frozen的VersionControl
	* @param SessionContext
	* @return
	*/ 
	@Override
	public List<VersionControl> batchFrozen(List<VersionControl> versionControls, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			for(VersionControl versionControl : versionControls) {
				basManager.frozen(versionControl, sc);
			}
			return versionControls;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 批量解冻versionControls
     *
	* @param versionControl 要UnFrozen的VersionControl
	* @param SessionContext
	* @return
	*/ 
	public List<VersionControl> batchUnFrozen(List<VersionControl> versionControls, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			for(VersionControl versionControl : versionControls) {
				basManager.unFrozen(versionControl, sc);
			}
			return versionControls;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 批量激活versionControls
     *
	* @param versionControls 要激活的VersionControl
	* @param SessionContext
	* @return
	*/ 
	public List<VersionControl> batchActive(List<VersionControl> versionControls, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			for(VersionControl versionControl : versionControls) {
				basManager.active(versionControl, sc);
			}
			return versionControls;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 批量失效versionControls
     *
	* @param versionControls 要InActive的VersionControl
	* @param SessionContext
	* @return
	*/ 
	public List<VersionControl> batchInActive(List<VersionControl> versionControls, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			for(VersionControl versionControl : versionControls) {
				basManager.inActive(versionControl, sc);
			}
			return versionControls;
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	* 批量删除versionControls
     *
	* @param versionControls 要Delete的VersionControl
	* @param SessionContext
	* @return
	*/ 
	public void batchDelete(List<VersionControl> versionControls, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			for(VersionControl versionControl : versionControls) {
				basManager.delete(versionControl, sc);
			}
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据批次获得对应ProcessGroup信息
	 * 
	 * @param orgRrn 区域号
	 * @param category procssGroup类别(PM/MC/BJ)
	 * @param processType procssGroup对应的工步对应的Process类型(可为空)
	 * @param stepName 工步名称
	 * @param lot 批次(匹配批次对应的partName/processName/procedureName)
	 * 
	 * @param isThrowException 如果找到多个ProcessGroup是否抛出异常
	 */
	public ProcessGroup getProcessGroup(long orgRrn, String category, String processType, String stepName, Lot lot, boolean isThrowException) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT ProcessGroup FROM ProcessGroup ProcessGroup ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND stepName = :stepName ");
			sql.append(" AND category = :category ");
			if (!StringUtil.isEmpty(processType)) {
				sql.append(" AND processType = :processType ");
			}
			
	        sql.append(" AND (procedureName = :procedureName OR (procedureName IS NULL OR procedureName = '')) ");
	        sql.append(" AND (processName = :processName OR (processName IS NULL OR processName = '')) ");
	        sql.append(" AND (partName = :partName OR (partName IS NULL OR partName = '')) ");

	        Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", lot.getOrgRrn());
			query.setParameter("stepName", stepName);
			query.setParameter("category", category);
			if (!StringUtil.isEmpty(processType)) {
				query.setParameter("processType", processType);
			}

			query.setParameter("procedureName", lot.getProcedureName());
			query.setParameter("processName", lot.getProcessName());
			query.setParameter("partName", lot.getPartName());

			List<ProcessGroup> processGroups = query.getResultList();
			if (CollectionUtils.isEmpty(processGroups)) {
				return null;
			}
			if (processGroups.size() > 1) {
				if (isThrowException) {
					throw EDCExceptionBundle.bundle.MatchMultiProcessGroup();
				} 
				//返回匹配条件最符合的结果
				List<ProcessGroup> filterGroups = processGroups;
				filterGroups = processGroups.stream().filter(p -> !StringUtil.isEmpty(p.getProcedureName())).collect(Collectors.toList());
				if (CollectionUtils.isEmpty(filterGroups)) {
					filterGroups = processGroups;
				}
				
				processGroups = filterGroups;
				filterGroups = processGroups.stream().filter(p -> !StringUtil.isEmpty(p.getProcessName())).collect(Collectors.toList());
				if (CollectionUtils.isEmpty(filterGroups)) {
					filterGroups = processGroups;
				}
				
				processGroups = filterGroups;
				filterGroups = processGroups.stream().filter(p -> !StringUtil.isEmpty(p.getPartName())).collect(Collectors.toList());
				if (CollectionUtils.isEmpty(filterGroups)) {
					filterGroups = processGroups;
				}
				return filterGroups.get(0);
			}
			return processGroups.get(0);
		} catch (Exception e){
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * 根据条件获得ProcessGroup
	 * 
	 * @param orgRrn 区域号
	 * @param category procssGroup类别(PM/MC/BJ)
	 * @param processType procssGroup对应的工步对应的Process类型(可为空)
	 * @param procedureName Procedure名称
	 */
	public List<ProcessGroup> getProcessGroup(long orgRrn, String category, String processType, String procedureName, String stepName) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT ProcessGroup FROM ProcessGroup ProcessGroup ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND category = :category ");
			sql.append(" AND stepName = :stepName ");
			if (!StringUtil.isEmpty(processType)) {
				sql.append(" AND processType = :processType ");
			}
			sql.append(" AND (procedureName = :procedureName) ");

			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("category", category);
			if (!StringUtil.isEmpty(processType)) {
				query.setParameter("processType", processType);
			}
			query.setParameter("procedureName", procedureName);
			query.setParameter("stepName", stepName);

			return query.getResultList();
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 保存ProcessGroup数据
	 * 
	 * @param stepName 当前作业的工步名称
	 * @param lots
	 * @param sc
	 */
	public void saveProcessGroupData(String stepName, List<Lot> lots, SessionContext sc) throws ClientException {
		StringBuffer sqlData = new StringBuffer();
		sqlData.append(" SELECT ProcessGroupData FROM ProcessGroupData ProcessGroupData ");
		sqlData.append(" WHERE ");
		sqlData.append(ADBase.BASE_CONDITION_N);
		sqlData.append(" AND groupId = :groupId ");
		sqlData.append(" AND lotRrn = :lotRrn ");
		sqlData.append(" AND stepName = :stepName ");
		
		try {
			Query queryData = em.createQuery(sqlData.toString());
			for (Lot lot : lots) {
				ProcessGroup processGroup = getProcessGroup(sc.getOrgRrn(), ProcessGroup.GROUPCATEGORY_PM, ProcessGroup.PROCESSTYPE_PROCESS, stepName, lot, false);
				if (processGroup != null) {
					queryData.setParameter("orgRrn", sc.getOrgRrn());
					queryData.setParameter("groupId", processGroup.getGroupId());
					queryData.setParameter("lotRrn", lot.getObjectRrn());
					queryData.setParameter("stepName", stepName);
					
                    List<ProcessGroupData> processGroupDatas = queryData.getResultList();
                    if (CollectionUtils.isNotEmpty(processGroupDatas)) {
						ProcessGroupData data = processGroupDatas.get(0);
						data.setBatchId(lot.getBatchId());
						data.setProcessTime(sc.getTransTime());
                        data.setEquipmentId(lot.getLastEquipmentId());
						data.setOperator(lot.getOperator1());
                        data.setLogicRecipeName(lot.getRecipeName());
                        data.setEquipmentRecipeName(lot.getEquipmentRecipe());
						data.setReticleId(lot.getEquipmentMask());
						data = em.merge(data);
					} else {
						ProcessGroupData data = ProcessGroupData.buildData(processGroup, lot, sc);
						em.persist(data);
					}
				}
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 获取批次的ProcessGroup数据
	 * 
	 * @param stepName
	 * @param lot 
	 */
	public List<ProcessGroupData> getProcessGroupDataByProcessMeasure(String stepName, Lot lot) throws ClientException {	
		StringBuffer sqlData = new StringBuffer();
		sqlData.append(" SELECT ProcessGroupData FROM ProcessGroupData ProcessGroupData ");
		sqlData.append(" WHERE ");
		sqlData.append(ADBase.BASE_CONDITION_N);
		sqlData.append(" AND groupId = :groupId ");
		sqlData.append(" AND lotRrn = :lotRrn ");
		sqlData.append(" ORDER BY seqNo DESC ");

		try {
			ProcessGroup processGroup = getProcessGroup(lot.getOrgRrn(), ProcessGroup.GROUPCATEGORY_PM, ProcessGroup.PROCESSTYPE_MEASURE, stepName, lot, false);
			if (processGroup != null) {
				Query queryData = em.createQuery(sqlData.toString());
				queryData.setParameter("orgRrn", lot.getOrgRrn());
				queryData.setParameter("groupId", processGroup.getGroupId());
				queryData.setParameter("lotRrn", lot.getObjectRrn());

				List<ProcessGroupData> processGroupDatas = queryData.getResultList();
				return processGroupDatas;
			}
			return Lists.newArrayList();
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * 获取Monitor批次的ProcessGroup数据
	 *
	 * @param lot Monitor Lot
	 */
	public List<ProcessGroup> getCreateProcessGroupByBatchJob(String stepName, Lot lot) throws ClientException {
		StringBuffer sqlQuery = new StringBuffer();
		sqlQuery.append(" SELECT ProcessGroup FROM ProcessGroup ProcessGroup ");
		sqlQuery.append(" WHERE ");
		sqlQuery.append(ADBase.BASE_CONDITION_N);
		sqlQuery.append(" AND groupId = :groupId ");
		sqlQuery.append(" ORDER BY seqNo DESC ");

		try {
			List<ProcessGroup> processGroupList = Lists.newArrayList();
			List<ProcessGroup> processGroups = getProcessGroup(lot.getOrgRrn(), ProcessGroup.GROUPCATEGORY_BJ, ProcessGroup.PROCESSTYPE_CREATE_BATCH, lot.getProcedureName(), stepName);
			processGroups.forEach(processGroup -> {
				Query queryData = em.createQuery(sqlQuery.toString());
				queryData.setParameter("orgRrn", lot.getOrgRrn());
				queryData.setParameter("groupId", processGroup.getGroupId());

				processGroupList.addAll(queryData.getResultList());
			});

			return processGroupList;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * 获取cleanStep
	 *
	 * @param stepName
	 * @param lot
	 */
	public List<ProcessGroup> getMeasureProcessGroupByMeasureClean(String stepName, Lot lot) throws ClientException {
		StringBuffer queryByGroupSql = new StringBuffer();
		queryByGroupSql.append(" SELECT ProcessGroup FROM ProcessGroup ProcessGroup ");
		queryByGroupSql.append(" WHERE ");
		queryByGroupSql.append(ADBase.BASE_CONDITION_N);
		queryByGroupSql.append(" AND groupId = :groupId ");
		queryByGroupSql.append(" AND processType = :processType ");

		try {
			ProcessGroup processGroup = getProcessGroup(lot.getOrgRrn(), ProcessGroup.GROUPCATEGORY_MC, ProcessGroup.PROCESSTYPE_MEASURE, stepName, lot, false);
			if (processGroup == null) {
				return Lists.newArrayList();
			}

			Query groupQuery = em.createQuery(queryByGroupSql.toString());
			groupQuery.setParameter("orgRrn", lot.getOrgRrn());
			groupQuery.setParameter("groupId", processGroup.getGroupId());
			groupQuery.setParameter("processType", ProcessGroup.PROCESSTYPE_CLEAN);

			List<ProcessGroup> processSteps = groupQuery.getResultList();
			return  processSteps;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * 获取ProcessStep
	 *
	 * @param stepName
	 * @param lot
	 */
	public List<ProcessGroup> getMeasureProcessGroupByProcessMeasure(String stepName, Lot lot) throws ClientException {
		StringBuffer queryByGroupSql = new StringBuffer();
		queryByGroupSql.append(" SELECT ProcessGroup FROM ProcessGroup ProcessGroup ");
		queryByGroupSql.append(" WHERE ");
		queryByGroupSql.append(ADBase.BASE_CONDITION_N);
		queryByGroupSql.append(" AND groupId = :groupId ");
		queryByGroupSql.append(" AND processType = :processType ");

		try {
			ProcessGroup processGroup = getProcessGroup(lot.getOrgRrn(), ProcessGroup.GROUPCATEGORY_PM, ProcessGroup.PROCESSTYPE_MEASURE, stepName, lot, false);
			if (processGroup == null) {
				return Lists.newArrayList();
			}

			Query groupQuery = em.createQuery(queryByGroupSql.toString());
			groupQuery.setParameter("orgRrn", lot.getOrgRrn());
			groupQuery.setParameter("groupId", processGroup.getGroupId());
			groupQuery.setParameter("processType", ProcessGroup.PROCESSTYPE_PROCESS);

			List<ProcessGroup> processSteps = groupQuery.getResultList();
			return  processSteps;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
     * 批量保存ProcessGroup
     *
     * @param processGroups ProcessGroup列表
     * @param sc
     * @return
     * @throws ClientException
     */
	public void saveProcessGroups(List<ProcessGroup> processGroups, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			for (ProcessGroup processGroup : processGroups) {
				saveProcessGroup(processGroup, sc);
			}
		} catch (Exception e){ 
			throw ExceptionManager.handleException(logger, e);
		}
	}

    /**
     * 保存 ProcessGroup
     *
     * @param processGroup ProcessGroup
     * @param sc
     * @return
     * @throws ClientException
     */
    public ProcessGroup saveProcessGroup(ProcessGroup processGroup, SessionContext sc) throws ClientException {
        sc.buildTransInfo();
        try {
            if (processGroup.getGroupId() == null) {
                Map<String, Object> paramMap = new HashMap<String, Object>();
                paramMap.put("orgRrn", sc.getOrgRrn());
                StringBuffer where = new StringBuffer(" ");
                where.append(" PROCESSSTEP = '" + processGroup.getProcessStepName() + "' ");
                where.append(" AND MEASURESTEP = '" + processGroup.getMeasureStepName() + "'");
				if (ProcessGroup.GROUPCATEGORY_PM.equals(processGroup.getCategory())) {
					where.append(" AND \"PROCEDURE\" = '" + processGroup.getProcedureName() + "'");
					long processGroupCount = adManager.getEntityMapCountByQueryName("ProcessMeasureGroup", where.toString(), paramMap);
					if (processGroupCount > 0) {
						throw EDCExceptionBundle.bundle.ProcessMeasureGroupIsExist();
					}
				} else if (ProcessGroup.GROUPCATEGORY_MC.equals(processGroup.getCategory())) {
					long processGroupCount = adManager.getEntityMapCountByQueryName("MeasureCleanGroup", where.toString(), paramMap);
					if (processGroupCount > 0) {
						throw EDCExceptionBundle.bundle.MeasureCleanGroupIsExist();
					}
				} else if (ProcessGroup.GROUPCATEGORY_BJ.equals(processGroup.getCategory())) {
					StringBuffer attachWhere = new StringBuffer(" ");
					attachWhere.append(" PARTNAME = '" + processGroup.getPartName() + "' ");
					attachWhere.append(" AND NPWSTEPNAME = '" + processGroup.getNpwStepName() + "'");
					attachWhere.append(" AND NPWPROCEDURENAME = '" + processGroup.getNpwProcedureName() + "'");

					long processGroupCount = adManager.getEntityMapCountByQueryName("BatchJobMappingGroup", attachWhere.toString(), paramMap);
					if (processGroupCount > 0) {
						throw EDCExceptionBundle.bundle.BatchJobMappingIsExist();
					}
				}

                processGroup.setOrgRrn(sc.getOrgRrn());
                processGroup.setGroupId(String.valueOf(SnowFlake.generateId()));
                generateAndSaveProcessGroup(processGroup);
            } else {
                removeProcessGroup(processGroup.getGroupId(), sc);
                generateAndSaveProcessGroup(processGroup);
            }
            return processGroup;
        } catch (Exception e) {
            throw ExceptionManager.handleException(logger, e);
        }
    }
    
    
    /**
     * 将 ProcessGroup按类型PM、MC、EDCATTACH 拆分两条数据进行保存
     * @param processGroup processGroup
     * @throws ClientException
     */
    private void generateAndSaveProcessGroup(ProcessGroup processGroup) throws ClientException {
        try {
            ProcessGroup processGroup1 = (ProcessGroup) ObjectUtils.clone(processGroup);
			ProcessGroup processGroup2 = (ProcessGroup) ObjectUtils.clone(processGroup);
			processGroup1.setSeqNo(1l);
			processGroup2.setSeqNo(2l);

			if (ProcessGroup.GROUPCATEGORY_PM.equals(processGroup1.getCategory())) {
				processGroup1.setProcessType(ProcessGroup.PROCESSTYPE_PROCESS);
				processGroup1.setStepName(processGroup.getProcessStepName());
				em.persist(processGroup1);

				processGroup2.setProcessType(ProcessGroup.PROCESSTYPE_MEASURE);
				processGroup2.setStepName(processGroup.getMeasureStepName());
				em.persist(processGroup2);
			} else if(ProcessGroup.GROUPCATEGORY_MC.equals(processGroup1.getCategory())) {
				processGroup1.setProcessType(ProcessGroup.PROCESSTYPE_CLEAN);
				processGroup1.setStepName(processGroup.getProcessStepName());
				em.persist(processGroup1);

				processGroup2.setProcessType(ProcessGroup.PROCESSTYPE_MEASURE);
				processGroup2.setStepName(processGroup.getMeasureStepName());
				em.persist(processGroup2);
			} else if (ProcessGroup.GROUPCATEGORY_BJ.equals(processGroup.getCategory())) {
				processGroup1.setProcessType(ProcessGroup.PROCESSTYPE_RESUME_BATCH);
				processGroup1.setStepName(processGroup.getNpwStepName());
				processGroup1.setProcedureName(processGroup.getNpwProcedureName());
				em.persist(processGroup1);

				processGroup2.setProcessType(ProcessGroup.PROCESSTYPE_CREATE_BATCH);
				em.persist(processGroup2);
			}
        } catch (Exception e) {
            throw ExceptionManager.handleException(logger, e);
        }
    }


    /**
     * 移除 ProcessGroup
     *
     * @param processGroup ProcessGroup
     * @param sc
     * @return
     * @throws ClientException
     */
    public void removeProcessGroup(String groupId, SessionContext sc) throws ClientException {
        try {
            StringBuffer sql = new StringBuffer("DELETE FROM ProcessGroup ");
            sql.append(" WHERE ");
            sql.append(ADBase.BASE_CONDITION_N);
            sql.append(" AND groupId = :groupId ");
            
            Query query = em.createQuery(sql.toString());
            query.setParameter("orgRrn", sc.getOrgRrn());
            query.setParameter("groupId", groupId);
            query.executeUpdate();
        } catch (ClientException e){
            throw ExceptionManager.handleException(logger, e);
        }
    }
    
    /**
	 * 校验公式是否合法
	 * @param setLine
	 * @throws ClientException
	 */
	@Override
	public void checkFormula(EdcItemSetLine setLine) throws ClientException {
		try {
			if (!setLine.getIsFormula()) {
	            return;
	        }

	        if (StringUtil.isEmpty(setLine.getFormula())) {
	            throw EDCExceptionBundle.bundle.ItemSetLineFormulaMust();
	        }
	        Set<String> variableNameSet = new HashSet<String>();
	        for (FormulaVariable variable : setLine.getFormulaVariables()) {
	            variableNameSet.add(variable.getVariableName());
	        }
	        
	        VectorJep jep = new VectorJep();
	        jep.addStandardFunctions();
	        jep.setElementMultiply(true);
	        jep.setAllowUndeclared(true);

	        jep.parseExpression(setLine.getFormula());
	        // 检查公式是否正确
	        if (jep.getErrorInfo() != null) {
	            throw EDCExceptionBundle.bundle.ItemSetLineFormulaTextIsError();
	        }
	        SymbolTable table = jep.getSymbolTable();
	        for (Object nameObj : table.keySet()) {
	            String name = (String) nameObj;
	            // 可以允许定义的变量
	            if (variableNameSet.contains(name)) {
	                continue;
	            }
	            // 可以允许参数做为变量
	            if (ParameterUtil.hasParameter(name)) {
	                continue;
	            }
	            throw EDCExceptionBundle.bundle.ItemSetLineFormulaVariableIsNotFound();
	        }
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

}