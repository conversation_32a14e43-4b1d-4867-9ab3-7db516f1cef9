package com.glory.edc.client;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import com.glory.common.context.model.ContextValue;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcAQLSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcProcessData;
import com.glory.edc.model.EdcProcessDataConfig;
import com.glory.edc.model.EdcProcessDataLog;
import com.glory.edc.model.EdcResult;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcSpec;
import com.glory.edc.model.EdcSubgroupPlan;
import com.glory.edc.model.EdcTecn;
import com.glory.edc.model.auto.EdcAutoConfig;
import com.glory.edc.model.calculation.ProcessGroup;
import com.glory.edc.model.calculation.ProcessGroupData;
import com.glory.edc.model.sampling.AqlSamplingDetail;
import com.glory.edc.model.sampling.ManualSamplingPlan;
import com.glory.edc.model.sampling.SamplingPlan;
import com.glory.edc.model.sampling.SamplingPlanCalc;
import com.glory.framework.base.model.VersionControl;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.SessionContext;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.wip.model.Lot;

public interface EDCManager {
	 
	public EntityManager getEntityManager();
	
	AbstractEdcSet getEdcSet(long orgRrn, String edcSetName, Long version, boolean isThrowException) throws ClientException;
	AbstractEdcSet getEdcSet(long orgRrn, String edcSetName, Long version, boolean isLoadLine, boolean isThrowException) throws ClientException;
	AbstractEdcSet getEdcSetFull(long orgRrn, String edcSetName, Long version, boolean isThrowException)
			throws ClientException;
	
	AbstractEdcSet saveEdcSet(AbstractEdcSet edcSet, SessionContext sc) throws ClientException;
	
	AbstractEdcSet frozen(AbstractEdcSet edcSet, SessionContext sc) throws ClientException;
		
	boolean saveItemSetCurrents(List<MLot> mLots, SessionContext sc) throws ClientException;

	void removeItemSetCurrents(String batchId, Long lotRrn) throws ClientException;
	
	List<EdcSetCurrent> getItemSetCurrents(Long orgRrn, String batchId, Long lotRrn, String componentUnitId) throws ClientException;
	
	AbstractEdcSet getActualEdcSet(Long edcSetRrn, Map<String, EdcSpec> specMap, Map<String, Object> paramMap) throws ClientException;
	List<String> getEdcSetNames(long orgRrn, String partName, String processName, 
			List<String> procedureNames, List<String> stepNames, Map<String, String> otherContext) throws ClientException;
	
	EdcResult saveData(List<EdcData> dcDatas, String edcFlag,
			String edcFrom, boolean isReCalSubgroupPlan, SessionContext sc) throws ClientException;
	
	List<EdcData> getData(Long orgRrn, String batchId, Long lotRrn, Long edcSetRrn) throws ClientException;
	List<EdcData> getLastData(Long orgRrn, String batchId, Long lotRrn, String stepName, String edcSetName, String itemName) throws ClientException;
	List<EdcResult> getLastResult(Long orgRrn, String batchId, Long lotRrn, String stepName, String edcSetName, String itemName) throws ClientException;
	
	void reCalculateSubgroupPlan(EdcItemSetLine line, EdcData dcData, boolean isOoc) throws ClientException;
	
	List<EdcSubgroupPlan> getSubgroupPlanList(Long orgRrn ,Long lineRrn)throws ClientException;
		
	public SamplingPlan saveEdcSamplingPlan(SamplingPlan samplingPlan, String status, SessionContext sc) throws ClientException;
	public SamplingPlan getAqlSamplingPlan(long orgRrn, String samplingPlanName, Long samplingPlanVersion) throws ClientException;
	public EdcAQLSet saveEdcAQLSet(EdcAQLSet edcAQLSet, String status, SessionContext sc) throws ClientException;
	
	public SamplingPlanCalc initSamplingPlanCalc(long orgRrn, String samplePlanType, 
			long samplePlanInstanceRrn, boolean isQualified, boolean isRetest, Integer groupSize1, Integer groupSize2);
	public SamplingPlanCalc calculateSamplingPlanCalc(long orgRrn, String samplePlanType, 
			long samplePlanInstanceRrn, boolean isQualified, boolean isRetest);
	public SamplingPlanCalc resetSamplingPlanCalc(long orgRrn, String samplePlanType, long samplePlanInstanceRrn, Integer groupSize1, Integer groupSize2);
	public SamplingPlanCalc getSamplingPlanCalc(long orgRrn, String samplePlanType, long samplePlanInstanceRrn);
	public void convertAqlSamplingPlanInstanceState(Long samplingPlanInstanceRrn, boolean isQualified, boolean isRetest, SessionContext sc) throws ClientException;

	public AqlSamplingDetail getAqlSamplingSize(Long orgRrn, EdcAQLSet edcAQLSet, BigDecimal mainQty, String partName) throws ClientException;
	public void createAqlSamplingPlanInstance(EdcAQLSet edcAQLSet, Lot lot) throws ClientException;
	
    public List<EdcAutoConfig> getEdcAutoConfig(long orgRrn, String ipAdress, String edcItem, String equipmentId, String equipmentType) throws ClientException;

	EdcData correctEdcData(EdcData edcData, SessionContext sc) throws ClientException;

	List<AbstractEdcSet> saveEdcSets(List<AbstractEdcSet> edcSets, SessionContext sc) throws ClientException;
	
	public List<EdcItem> saveEdcItems(List<EdcItem> edcItems, SessionContext sc) throws ClientException;

	List<EdcProcessData> saveProcessDataList(List<EdcProcessData> processDataList, SessionContext sc) throws ClientException;
	
	public List<EdcProcessData> matchProcessDataAndGrouping(long orgRrn, EdcProcessData processData, String configName) throws ClientException;

	public List<EdcProcessDataConfig> getEdcProcessDataConfig(long orgRrn, String configName, boolean isThrowException) throws ClientException;
	
	public EdcProcessDataLog saveProcessDataLog(EdcProcessData processData, String transType, String returnCode, String returnMessage, SessionContext sc) throws ClientException;
	
	ManualSamplingPlan saveManualSamplingPlan(ManualSamplingPlan samplingPlan, SessionContext sc)
			throws ClientException;
	
	public void removeTecnByLotCurrentStep(String lotId, String stepName, SessionContext sc) throws ClientException;
	public List<EdcTecn> getTecnByContext(Lot lot, Map<String, String> contextMap) throws ClientException;
	public List<EdcTecn> getTecnByLotCurrentStep(long orgRrn, String lotId, String stepName) throws ClientException;
	
	public EdcAutoConfig saveEdcAutoConfig(EdcAutoConfig config, SessionContext sc) throws ClientException;
	
	public EdcTecn saveEdcTecn(EdcTecn edcTecn, String status, SessionContext sc) throws ClientException;
	public EdcTecn saveEdcTecn(EdcTecn edcTecn, ContextValue contextValue, SessionContext sc) throws ClientException;
	public void activeEdcTecn(List<ContextValue> contextValues, SessionContext sc) throws ClientException;
	public void inActiveEdcTecn(List<ContextValue> contextValues, SessionContext sc) throws ClientException;
	public void deleteEdcTecn(List<ContextValue> contextValues, SessionContext sc) throws ClientException;

	public List<VersionControl> batchFrozen(List<VersionControl> versionControls, SessionContext sc) throws ClientException;
	public List<VersionControl> batchUnFrozen(List<VersionControl> versionControls, SessionContext sc) throws ClientException;
	public List<VersionControl> batchActive(List<VersionControl> versionControls, SessionContext sc) throws ClientException;
	public List<VersionControl> batchInActive(List<VersionControl> versionControls, SessionContext sc) throws ClientException;
	public void batchDelete(List<VersionControl> versionControls, SessionContext sc) throws ClientException;
	void checkFormula(EdcItemSetLine setLine) throws ClientException;

	
	public ProcessGroup getProcessGroup(long orgRrn, String category, String processType, String stepName, Lot lot, boolean isThrowException) throws ClientException;
	public List<ProcessGroup> getProcessGroup(long orgRrn, String category, String processType, String procedureName, String stepName) throws ClientException;
	public List<ProcessGroupData> getProcessGroupDataByProcessMeasure(String stepName, Lot lot) throws ClientException;
	public List<ProcessGroup> getCreateProcessGroupByBatchJob(String stepName,Lot lot) throws ClientException;
	public List<ProcessGroup> getMeasureProcessGroupByMeasureClean(String stepName, Lot lot) throws ClientException;
	public List<ProcessGroup> getMeasureProcessGroupByProcessMeasure(String stepName, Lot lot) throws ClientException;
	public void saveProcessGroupData(String stepName, List<Lot> lots, SessionContext sc) throws ClientException;
	public void saveProcessGroups(List<ProcessGroup> processGroups, SessionContext sc) throws ClientException;
	public ProcessGroup saveProcessGroup(ProcessGroup processGroup, SessionContext sc) throws ClientException;
    public void removeProcessGroup(String groupId, SessionContext sc) throws ClientException;
}
