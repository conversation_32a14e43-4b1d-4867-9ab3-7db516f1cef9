package com.glory.edc.cdi.impl;

import javax.annotation.Priority;
import javax.ejb.EJB;
import javax.ejb.Stateless;

import com.glory.edc.ejb.EDCManagerLocal;
import com.glory.mes.mm.cdi.IMLotCdiAction;
import com.glory.mes.mm.cdi.MLotContext;

@Stateless
@Priority(10)
public class RemoveEdcSetCurrentMLotAction implements IMLotCdiAction {

	public static final String ACTION_NAME = "RemoveEdcSetCurrentMLotAction";
	
	@EJB
	private EDCManagerLocal edcManager;
	
	@Override
	public int getPriority() {
		return 10;
	}

	@Override
	public String getActionName() {
		return ACTION_NAME;
	}

	@Override
	public String getActionDesc() {
		return "Material lot remove current edcset action.";
	}

	@Override
	public String getCdiPointName() {
		return IMLotCdiAction.CDI_POINT_IQC;
	}

	@Override
	public String getTriggerPoint() {
		return IMLotCdiAction.TRIGGER_POINT_POSTEXECUTE;
	}

	@Override
	public MLotContext invoke(MLotContext context) throws Exception {
		
		edcManager.removeItemSetCurrents(null, context.getmLot().getObjectRrn());
		
		return context;
	}

}
