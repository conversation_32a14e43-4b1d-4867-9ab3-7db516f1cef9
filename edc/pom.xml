<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.glory.mes</groupId>
		<artifactId>mes-pom</artifactId>
		<version>8.4.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>edc</artifactId>
	<name>EDC</name>
	<description>数据收集</description>
	<packaging>ejb</packaging>

	<dependencies>
		<dependency>
			<groupId>com.glory</groupId>
			<artifactId>jep</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>edcmodel</artifactId>
			<version>${mes.version}</version>
		</dependency>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>mesmsgsend</artifactId>
			<version>${mes.version}</version>
		</dependency>
		<dependency>
			<groupId>com.glory.common</groupId>
			<artifactId>context</artifactId>
			<version>${context.version}</version>
			
			<exclusions>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentity</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>activeentitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>security</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>securitymodel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.glory.framework</groupId>
					<artifactId>core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>
	
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-ejb-plugin</artifactId>
				<configuration>
					<ejbVersion>3.1</ejbVersion>
					<generateClient>true</generateClient>
					<clientIncludes>
						<clientInclude>com/glory/edc/client/**</clientInclude>
					</clientIncludes>
                    <classifier>${my.classifier}</classifier>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>