<?xml version="1.0" encoding="UTF-8"?>
<?pde version="3.5"?>

<product name="MESwell V8.0" uid="MESwell" id="com.glory.framework.base.product" application="com.glory.framework.base.application.Application" version="8.4.0" useFeatures="false" includeLaunchers="true" autoIncludeRequirements="true">

   <configIni use="default">
      <win32>/com.glory.framework.lib/config.ini</win32>
   </configIni>

   <launcherArgs>
      <programArgs>-clearPersistedState
      </programArgs>
      <vmArgsMac>-XstartOnFirstThread -Dorg.eclipse.swt.internal.carbon.smallFonts
      </vmArgsMac>
   </launcherArgs>

   <windowImages/>

   <splash
      location="com.glory.framework.base" />
   <launcher name="MESwell">
      <win useIco="true">
         <ico path="icons/glory.ico"/>
         <bmp/>
      </win>
   </launcher>

   <vm>
   </vm>

   <plugins>
      <plugin id="ca.odell.glazedlists"/>
      <plugin id="com.glory.common.context" version="8.4.0"/>
      <plugin id="com.glory.common.ecn" version="8.4.0"/>
      <plugin id="com.glory.common.excel" version="3.3.0"/>
      <plugin id="com.glory.common.intro" version="3.1.0"/>
      <plugin id="com.glory.common.state" version="8.4.0"/>
      <plugin id="com.glory.edc" version="8.4.0"/>
      <plugin id="com.glory.framework.base" version="8.4.0"/>
      <plugin id="com.glory.framework.lib" version="8.4.0"/>
      <plugin id="com.glory.framework.runtime" version="8.4.0"/>
      <plugin id="com.glory.mes.base" version="8.4.0"/>
      <plugin id="com.glory.mes.mm" version="8.4.0"/>
      <plugin id="com.glory.mes.prd" version="8.4.0"/>
      <plugin id="com.glory.mes.prd.designer" version="8.4.0"/>
      <plugin id="com.glory.mes.prd.designer.common" version="8.4.0"/>
      <plugin id="com.glory.mes.ras" version="8.4.0"/>
      <plugin id="com.glory.mes.wip" version="8.4.0"/>
      <plugin id="com.glory.mes.wip.advance" version="8.4.0"/>
      <plugin id="com.google.gson"/>
      <plugin id="com.google.guava"/>
      <plugin id="com.ibm.icu"/>
      <plugin id="com.sun.jna"/>
      <plugin id="com.sun.jna.platform"/>
      <plugin id="javax.annotation"/>
      <plugin id="javax.inject"/>
      <plugin id="org.apache.batik.constants"/>
      <plugin id="org.apache.batik.css"/>
      <plugin id="org.apache.batik.i18n"/>
      <plugin id="org.apache.batik.util"/>
      <plugin id="org.apache.commons.codec"/>
      <plugin id="org.apache.commons.collections4"/>
      <plugin id="org.apache.commons.io"/>
      <plugin id="org.apache.commons.jxpath"/>
      <plugin id="org.apache.commons.lang"/>
      <plugin id="org.apache.commons.logging"/>
      <plugin id="org.apache.commons.math3"/>
      <plugin id="org.apache.felix.scr"/>
      <plugin id="org.apache.log4j"/>
      <plugin id="org.apache.oro"/>
      <plugin id="org.apache.poi"/>
      <plugin id="org.apache.xerces"/>
      <plugin id="org.apache.xml.resolver"/>
      <plugin id="org.apache.xml.serializer"/>
      <plugin id="org.apache.xmlgraphics"/>
      <plugin id="org.dom4j"/>
      <plugin id="org.eclipse.collections"/>
      <plugin id="org.eclipse.compare"/>
      <plugin id="org.eclipse.compare.core"/>
      <plugin id="org.eclipse.core.commands"/>
      <plugin id="org.eclipse.core.contenttype"/>
      <plugin id="org.eclipse.core.databinding"/>
      <plugin id="org.eclipse.core.databinding.observable"/>
      <plugin id="org.eclipse.core.databinding.property"/>
      <plugin id="org.eclipse.core.expressions"/>
      <plugin id="org.eclipse.core.filebuffers"/>
      <plugin id="org.eclipse.core.filesystem"/>
      <plugin id="org.eclipse.core.filesystem.win32.x86_64" fragment="true"/>
      <plugin id="org.eclipse.core.jobs"/>
      <plugin id="org.eclipse.core.resources"/>
      <plugin id="org.eclipse.core.resources.win32.x86_64" fragment="true"/>
      <plugin id="org.eclipse.core.runtime"/>
      <plugin id="org.eclipse.core.variables"/>
      <plugin id="org.eclipse.debug.core"/>
      <plugin id="org.eclipse.draw2d"/>
      <plugin id="org.eclipse.e4.core.commands"/>
      <plugin id="org.eclipse.e4.core.contexts"/>
      <plugin id="org.eclipse.e4.core.di"/>
      <plugin id="org.eclipse.e4.core.di.annotations"/>
      <plugin id="org.eclipse.e4.core.di.extensions"/>
      <plugin id="org.eclipse.e4.core.di.extensions.supplier"/>
      <plugin id="org.eclipse.e4.core.services"/>
      <plugin id="org.eclipse.e4.emf.xpath"/>
      <plugin id="org.eclipse.e4.ui.bindings"/>
      <plugin id="org.eclipse.e4.ui.css.core"/>
      <plugin id="org.eclipse.e4.ui.css.swt"/>
      <plugin id="org.eclipse.e4.ui.css.swt.theme"/>
      <plugin id="org.eclipse.e4.ui.di"/>
      <plugin id="org.eclipse.e4.ui.dialogs"/>
      <plugin id="org.eclipse.e4.ui.ide"/>
      <plugin id="org.eclipse.e4.ui.model.workbench"/>
      <plugin id="org.eclipse.e4.ui.services"/>
      <plugin id="org.eclipse.e4.ui.swt.win32" fragment="true"/>
      <plugin id="org.eclipse.e4.ui.widgets"/>
      <plugin id="org.eclipse.e4.ui.workbench"/>
      <plugin id="org.eclipse.e4.ui.workbench.addons.swt"/>
      <plugin id="org.eclipse.e4.ui.workbench.renderers.swt"/>
      <plugin id="org.eclipse.e4.ui.workbench.swt"/>
      <plugin id="org.eclipse.e4.ui.workbench3"/>
      <plugin id="org.eclipse.emf.common"/>
      <plugin id="org.eclipse.emf.ecore"/>
      <plugin id="org.eclipse.emf.ecore.change"/>
      <plugin id="org.eclipse.emf.ecore.edit"/>
      <plugin id="org.eclipse.emf.ecore.xmi"/>
      <plugin id="org.eclipse.emf.edit"/>
      <plugin id="org.eclipse.equinox.app"/>
      <plugin id="org.eclipse.equinox.bidi"/>
      <plugin id="org.eclipse.equinox.common"/>
      <plugin id="org.eclipse.equinox.console"/>
      <plugin id="org.eclipse.equinox.event"/>
      <plugin id="org.eclipse.equinox.p2.artifact.repository"/>
      <plugin id="org.eclipse.equinox.p2.core"/>
      <plugin id="org.eclipse.equinox.p2.engine"/>
      <plugin id="org.eclipse.equinox.p2.jarprocessor"/>
      <plugin id="org.eclipse.equinox.p2.metadata"/>
      <plugin id="org.eclipse.equinox.p2.metadata.repository"/>
      <plugin id="org.eclipse.equinox.p2.repository"/>
      <plugin id="org.eclipse.equinox.preferences"/>
      <plugin id="org.eclipse.equinox.registry"/>
      <plugin id="org.eclipse.equinox.security"/>
      <plugin id="org.eclipse.equinox.security.win32.x86_64" fragment="true"/>
      <plugin id="org.eclipse.equinox.simpleconfigurator"/>
      <plugin id="org.eclipse.gef"/>
      <plugin id="org.eclipse.help"/>
      <plugin id="org.eclipse.jface"/>
      <plugin id="org.eclipse.jface.databinding"/>
      <plugin id="org.eclipse.jface.text"/>
      <plugin id="org.eclipse.ltk.core.refactoring"/>
      <plugin id="org.eclipse.nebula.cwt"/>
      <plugin id="org.eclipse.nebula.widgets.cdatetime"/>
      <plugin id="org.eclipse.nebula.widgets.nattable.core"/>
      <plugin id="org.eclipse.nebula.widgets.nattable.extension.glazedlists"/>
      <plugin id="org.eclipse.nebula.widgets.nattable.extension.poi"/>
      <plugin id="org.eclipse.osgi"/>
      <plugin id="org.eclipse.osgi.compatibility.state" fragment="true"/>
      <plugin id="org.eclipse.osgi.services"/>
      <plugin id="org.eclipse.osgi.util"/>
      <plugin id="org.eclipse.swt"/>
      <plugin id="org.eclipse.swt.win32.win32.x86_64" fragment="true"/>
      <plugin id="org.eclipse.text"/>
      <plugin id="org.eclipse.ui"/>
      <plugin id="org.eclipse.ui.console"/>
      <plugin id="org.eclipse.ui.editors"/>
      <plugin id="org.eclipse.ui.forms"/>
      <plugin id="org.eclipse.ui.ide"/>
      <plugin id="org.eclipse.ui.navigator"/>
      <plugin id="org.eclipse.ui.views"/>
      <plugin id="org.eclipse.ui.win32" fragment="true"/>
      <plugin id="org.eclipse.ui.workbench"/>
      <plugin id="org.eclipse.ui.workbench.texteditor"/>
      <plugin id="org.eclipse.urischeme"/>
      <plugin id="org.eclipse.wst.common.core"/>
      <plugin id="org.eclipse.wst.common.emf"/>
      <plugin id="org.eclipse.wst.common.environment"/>
      <plugin id="org.eclipse.wst.common.frameworks"/>
      <plugin id="org.eclipse.wst.common.uriresolver"/>
      <plugin id="org.eclipse.wst.sse.core"/>
      <plugin id="org.eclipse.wst.xml.core"/>
      <plugin id="org.osgi.service.prefs"/>
      <plugin id="org.osgi.util.function"/>
      <plugin id="org.osgi.util.measurement"/>
      <plugin id="org.osgi.util.position"/>
      <plugin id="org.osgi.util.promise"/>
      <plugin id="org.osgi.util.xml"/>
      <plugin id="org.slf4j.api"/>
      <plugin id="org.tukaani.xz"/>
      <plugin id="org.w3c.css.sac"/>
      <plugin id="org.w3c.dom.events"/>
      <plugin id="org.w3c.dom.smil"/>
      <plugin id="org.w3c.dom.svg"/>
   </plugins>

   <configurations>
      <plugin id="org.apache.felix.scr" autoStart="true" startLevel="2" />
      <plugin id="org.eclipse.core.runtime" autoStart="true" startLevel="0" />
      <plugin id="org.eclipse.equinox.common" autoStart="true" startLevel="2" />
      <plugin id="org.eclipse.equinox.event" autoStart="true" startLevel="2" />
      <plugin id="org.eclipse.equinox.simpleconfigurator" autoStart="true" startLevel="1" />
      <property name="com.glory.host" value="TEST" />
      <property name="com.glory.product.version" value="V8.0" />
      <property name="com.glory.product.name" value="MESwell" />
      <property name="eclipse.log.level" value="DEBUG" />
   </configurations>

   <preferencesInfo>
      <targetfile overwrite="false"/>
   </preferencesInfo>

   <cssInfo>
   </cssInfo>

</product>
