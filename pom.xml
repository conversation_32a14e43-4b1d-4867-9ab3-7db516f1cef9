<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.glory.mes.pvc</groupId>
	<artifactId>pvc-pom</artifactId>
	<packaging>pom</packaging>
	<name>pvc-pom</name>
	<version>8.4-SNAPSHOT</version>
	<description>太阳能电池片根目录</description>

	<properties>
		<pvc.version>${project.version}</pvc.version>
		<!-- 这部分继承meswell,不用重复定义
		<framework.version>8.4.0</framework.version>
        <mes.version>8.4.0</mes.version>
		<context.version>8.4.0</context.version>
		<msg.version>8.4.0</msg.version>
		<state.version>8.4.0</state.version>
        <excel.version>3.3.0</excel.version>
        <ecn.version>8.4.0</ecn.version>
		-->
		<doc.version>2.0.0-SNAPSHOT</doc.version>
		<datatransfer.version>2.3.0</datatransfer.version>
		<mes.version>8.4.0</mes.version>
		<alm.version>8.4.0</alm.version>
		<repair.version>8.2.0</repair.version>
		<spc.version>8.3.0</spc.version>
		<pms.version>8.2.0</pms.version>
		<rtd.version>5.2.0</rtd.version>
		<gtm.version>8.4.0</gtm.version>
		<label.version>8.4.0</label.version>
		<print.version>8.4.0</print.version>
		<msa.version>1.0.0-SNAPSHOT</msa.version>
	</properties>

	<parent>
		<groupId>com.glory.mes</groupId>
		<artifactId>mes-pom</artifactId>
		<version>8.4.0</version>
	</parent>
  
	<dependencies>
		<dependency>
			<groupId>com.glory.msg</groupId>
			<artifactId>msg</artifactId>
			<version>${msg.version}</version>
		</dependency>
		<dependency>
			<groupId>com.glory.msg</groupId>
			<artifactId>msgmodel</artifactId>
			<version>${msg.version}</version>
		</dependency>
	</dependencies>

	<modules>
		<module>pvc</module>
		<module>pvcmodel</module>
		<!--<module>pvc-rtd</module>-->
		<module>pvc-ear</module>
		<module>pvc-mes-ear</module>
		<module>pvc-gtm</module>
		<module>pvcAndon</module>
	</modules>
</project>