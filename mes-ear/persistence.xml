<?xml version="1.0" encoding="UTF-8"?>
<persistence version="1.0" xmlns="http://java.sun.com/xml/ns/persistence" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://java.sun.com/xml/ns/persistence http://java.sun.com/xml/ns/persistence/persistence_1_0.xsd">
	<persistence-unit name="framework" transaction-type="JTA">
		<provider>org.hibernate.ejb.HibernatePersistence</provider>
		<jta-data-source>java:/OracleDS</jta-data-source>
		<jar-file>activeentitymodel.jar</jar-file> 
		<jar-file>tenant.jar</jar-file> 
		<jar-file>securitymodel.jar</jar-file> 
		<jar-file>rasmodel.jar</jar-file> 
		<jar-file>edcmodel.jar</jar-file> 
		<jar-file>statemodel.jar</jar-file>
		<jar-file>contextmodel.jar</jar-file>
		<jar-file>ecnmodel.jar</jar-file> 
		<jar-file>mmmodel.jar</jar-file>
		<jar-file>mesbase.jar</jar-file>
		<jar-file>mesbasemodel.jar</jar-file>
		<jar-file>wip.jar</jar-file>
		<jar-file>wipmodel.jar</jar-file>
		<jar-file>excelmodel.jar</jar-file>
		<!--<jar-file>dryrun.jar</jar-file>-->
		<class>com.glory.framework.tenant.interceptor.TenantInterceptor</class>
		<class>com.glory.framework.tenant.interceptor.TenantEntityInterceptor</class>
		<properties>
			<property name="hibernate.dialect" value="org.hibernate.dialect.Oracle10gDialect"/>
         	<property name="hibernate.show_sql" value="true"/>
         	<property name="hibernate.ejb.interceptor" value="com.glory.framework.tenant.interceptor.TenantEntityInterceptor"/>
         	<property name="hibernate.session_factory.statement_inspector" value="com.glory.framework.tenant.interceptor.TenantInterceptor"/>
			<property name="jboss.entity.manager.jndi.name" value="java:/activeEntityManager"/>
			<property name="jboss.entity.manager.factory.jndi.name" value="java:/activeEntityManagerFactory"/>
    	</properties>
	</persistence-unit>
</persistence>

