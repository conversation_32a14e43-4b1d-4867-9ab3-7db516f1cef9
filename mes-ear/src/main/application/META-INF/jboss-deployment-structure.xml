<jboss-deployment-structure>
	<ear-subdeployments-isolated>false</ear-subdeployments-isolated>
	<sub-deployment name="wip.jar">
		<dependencies>
			<module name="deployment.esb.ear.esb.jar" />
			<module name="deployment.task.ear.task.jar" />
			<module name="deployment.rti.ear.rti.jar" />
		</dependencies>
	</sub-deployment>
	<sub-deployment name="mesmsgsend.jar">
		<dependencies>
			<module name="deployment.esb.ear.esb.jar" />
		</dependencies>
	</sub-deployment>
	<sub-deployment name="mesmsgservice.jar">
		<dependencies>
			<module name="deployment.esb.ear.esb.jar" />
		</dependencies>
	</sub-deployment>
	<!--  
	<sub-deployment name="rms.jar">
		<dependencies>
			<module name="deployment.esb.ear.esb.jar" />
		</dependencies>
	</sub-deployment>
	-->
	<sub-deployment name="mm.jar">
		<dependencies>
			<module name="deployment.task.ear.task.jar" />
		</dependencies>
	</sub-deployment>
	<!--<sub-deployment name="auto.jar">
		<dependencies>
			<module name="deployment.esb.ear.esb.jar" />
		</dependencies>
	</sub-deployment>
	
	<sub-deployment name="label.jar">
		<dependencies>
			<module name="deployment.esb.ear.esb.jar" />
		</dependencies>
	</sub-deployment>
	
	<sub-deployment name="dryrun.jar">
		<dependencies>
			<module name="deployment.esb.ear.esb.jar" />
		</dependencies>
	</sub-deployment>
	<sub-deployment name="wipfuture.jar">
		<dependencies>
			<module name="deployment.esb.ear.esb.jar" />
			<module name="deployment.task.ear.task.jar" />
		</dependencies>
	</sub-deployment>
	-->
	<sub-deployment name="ras.jar">
		<dependencies>
			<module name="deployment.task.ear.task.jar" />
			<module name="deployment.rti.ear.rti.jar" />
		</dependencies>
	</sub-deployment>
</jboss-deployment-structure>
