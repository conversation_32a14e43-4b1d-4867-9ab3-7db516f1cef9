package com.glory.mes.pvc.rtd.msg.rcs.send.transportjobcancel;

import java.util.Map;

import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.UUIDUtil;
import com.glory.mes.pvc.rtd.msg.rcs.send.PvcSendTransHandler;
import com.glory.mes.pvc.rtd.msg.service.PvcRtdTransContext;
import com.glory.msg.MessageParser;
import com.glory.msg.MessageParserFactory;
import com.glory.msg.MessageParserModel;
import com.glory.msg.RequestHeader;
import com.glory.msg.Response;
import com.glory.msg.trans.TransContext;

public class PvcTransportCommandCancelHandler extends PvcSendTransHandler {

    @Override
	public TransContext internalExecute(TransContext ctx) throws Exception {
    	PvcRtdTransContext context = (PvcRtdTransContext)ctx;		
		MessageParser parser = getMessageParser(PvcTransportCommandCancelRequest.MESSAGE_NAME);
			
		PvcTransportCommandCancelRequest request = new PvcTransportCommandCancelRequest();
		SessionContext sc = context.getSessionContext();
    	RequestHeader header = new RequestHeader();
    	header.setMessageName(PvcTransportCommandCancelRequest.MESSAGE_NAME);
    	header.setOrgName(sc.getOrgName());
    	header.setOrgRrn(sc.getOrgRrn());
    	header.setTransactionId(UUIDUtil.base58Uuid());
    	header.setUserName(sc.getUserName());
    	request.setHeader(header);

    	Map<String, Object> requestMap = context.getRequestMap();
		if (!requestMap.containsKey(PvcTransportCommandCancelRequest.MESSAGE_NAME)) {
			return context;
		} 
		
		PvcTransportCommandCancelRequestBody body = (PvcTransportCommandCancelRequestBody) requestMap
				.get(PvcTransportCommandCancelRequest.MESSAGE_NAME);
    	request.setBody(body);
    	
		context.setRequest(parser.writerRequest(request));
		return context;
	}
	
	public Object executeResponse(Response response, TransContext context) throws Exception {
		PvcTransportCommandCancelResponse entityResponse = (PvcTransportCommandCancelResponse)response;	
		return entityResponse;
	}
	
	/**
	 * ע����Ϣ������
	 */
	public void initMessageParser() {
		MessageParserModel parserModel = new MessageParserModel();
		parserModel.setMessageName(PvcTransportCommandCancelRequest.MESSAGE_NAME);
		parserModel.setRequestClass(PvcTransportCommandCancelRequest.class);
		parserModel.setResponseClass(PvcTransportCommandCancelResponse.class);
		MessageParserFactory.registerMessageParser(PvcTransportCommandCancelRequest.MESSAGE_NAME, parserModel);
	}
	
	/**
	 * �����Ϣ������
	 */
	@Override
	public MessageParser getMessageParser() {
		return getMessageParser(PvcTransportCommandCancelRequest.MESSAGE_NAME);
	}

	
}
