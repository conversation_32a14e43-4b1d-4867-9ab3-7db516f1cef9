package com.glory.mes.pvc.rtd.msg.rcs.send.transportjobchange;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Request;

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.NONE)
public class PvcTransportCommandChangeRequest extends Request {
	private static final long serialVersionUID = 1L;
	
	public static final String MESSAGE_NAME = "TransportJobChange";
	
	@XmlElement(name="Body")
	private PvcTransportCommandChangeRequestBody body;

	public PvcTransportCommandChangeRequestBody getBody() {
		return body;
	}
	
	public void setBody(PvcTransportCommandChangeRequestBody body) {
		this.body = body;
	}
}