package com.glory.mes.pvc.rtd.msg.rcs.send.transportjobcancel;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Request;

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.NONE)
public class PvcTransportCommandCancelRequest extends Request {
	private static final long serialVersionUID = 1L;
	
	public static final String MESSAGE_NAME = "TransportJobCancel";
	
	@XmlElement(name="Body")
	private PvcTransportCommandCancelRequestBody body;

	public PvcTransportCommandCancelRequestBody getBody() {
		return body;
	}
	
	public void setBody(PvcTransportCommandCancelRequestBody body) {
		this.body = body;
	}
}