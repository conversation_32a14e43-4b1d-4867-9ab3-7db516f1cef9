package com.glory.mes.pvc.rtd.msg.rcs.send.transportjobcancel;

import javax.xml.bind.annotation.XmlElement;

import com.glory.msg.RequestBody;

public class PvcTransportCommandCancelResponseBody extends RequestBody {
	private static final long serialVersionUID = 1L;
	
	@XmlElement(name = "REQCODE")
	private String reqCode;
	
	@XmlElement(name = "CODE")
	private String code;

	@XmlElement(name = "MESSAGE")
	private String message;
	
	@XmlElement(name = "DATA")
	private String data;

	public String getReqCode() {
		return reqCode;
	}

	public void setReqCode(String reqCode) {
		this.reqCode = reqCode;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getData() {
		return data;
	}

	public void setData(String data) {
		this.data = data;
	}
	

}
