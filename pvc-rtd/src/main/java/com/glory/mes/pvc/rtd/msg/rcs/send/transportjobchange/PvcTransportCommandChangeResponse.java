package com.glory.mes.pvc.rtd.msg.rcs.send.transportjobchange;


import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Response;

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.NONE)
public class PvcTransportCommandChangeResponse extends Response {
	private static final long serialVersionUID = 1L;
	
	@XmlElement(name="Body")
	private PvcTransportCommandChangeResponseBody body;

	public PvcTransportCommandChangeResponseBody getBody() {
		return body;
	}

	public void setBody(PvcTransportCommandChangeResponseBody body) {
		this.body = body;
	}

}
