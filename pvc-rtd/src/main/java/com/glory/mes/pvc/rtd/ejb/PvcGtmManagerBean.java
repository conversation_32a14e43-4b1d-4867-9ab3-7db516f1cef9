package com.glory.mes.pvc.rtd.ejb;

import java.util.List;

import javax.ejb.EJB;
import javax.ejb.Remote;
import javax.ejb.Stateless;
import javax.enterprise.inject.Any;
import javax.enterprise.inject.Instance;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.framework.core.exception.ExceptionManager;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.SnowFlake;
import com.glory.framework.core.util.StringUtil;
import com.glory.gtm.client.GtmManager;
import com.glory.gtm.model.GtmBuffer;
import com.glory.gtm.model.GtmExceptions;
import com.glory.gtm.model.TransportHis;
import com.glory.gtm.model.TransportJob;
import com.glory.mes.pvc.rtd.client.PvcGtmManager;
import com.glory.mes.pvc.rtd.model.DispatchLot;
import com.glory.mes.pvc.rtd.model.DispatchResult;
import com.glory.mes.pvc.rtd.model.DispatchUnit;
import com.glory.mes.pvc.rtd.model.cdi.IRtdCdiAction;
import com.glory.mes.pvc.rtd.model.cdi.ITransportCancelRequestAction;
import com.glory.mes.pvc.rtd.model.cdi.ITransportChangeRequestAction;
import com.glory.mes.pvc.rtd.model.cdi.ITransportRequestAction;
import com.glory.rtd.loader.LoaderContext;

@Stateless
@Remote(PvcGtmManager.class)
public class PvcGtmManagerBean implements PvcGtmManager {

	private static final Logger logger = Logger.getLogger(PvcGtmManagerBean.class);

	@PersistenceContext
	private EntityManager em;

	@EJB
	private ADManager adManager;

	@EJB
	private GtmManager gtmManager;

	@Inject
	@Any
	private Instance<ITransportRequestAction> transportRequestActions;
	
	@Inject
	@Any
	private Instance<ITransportCancelRequestAction> transportCancelRequestActions;
	
	@Inject
	@Any
	private Instance<ITransportChangeRequestAction> transportChangeRequestActions;

	public List<ITransportRequestAction> getTransportRequestAction() {
		return (List<ITransportRequestAction>) IRtdCdiAction.sortActions(transportRequestActions.iterator());
	}
	
	public List<ITransportCancelRequestAction> getTransportCancelRequestAction() {
		return (List<ITransportCancelRequestAction>) IRtdCdiAction.sortActions(transportCancelRequestActions.iterator());
	}
	
	public List<ITransportChangeRequestAction> getTransportChangeRequestAction() {
		return (List<ITransportChangeRequestAction>) IRtdCdiAction.sortActions(transportChangeRequestActions.iterator());
	}

	/**
	 * 
	 * @param dispatchUnit
	 *            �����͵������ϼ�ID(DispatchUnit)
	 * @param destEqp
	 *            Ŀ���豸ID/�ֿ�ID
	 * @param destPosition
	 *            Ŀ���豸PortID/RackID
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public DispatchResult transportJobRequest(DispatchUnit dispatchUnit, String destEqp, String destPosition,
			SessionContext sc) throws ClientException {
		try {
			if (DispatchUnit.HOLDSTATE_ON.equals(dispatchUnit.getHoldState())) {
				// Hold���ϼ��޷�����
				throw new ClientException(GtmExceptions.CST_IS_HOLD);
			}

			TransportJob oldTransportJob = getActiveTransportJob(sc.getOrgRrn(), dispatchUnit.getObjectId());
			if (oldTransportJob != null) {
				// �Ѵ��ڰ�������
				throw new ClientException(GtmExceptions.TRANSFER_CMD_ALREADY_EXIST);
			}

			TransportJob transportJob = new TransportJob();
			transportJob.setOrgRrn(sc.getOrgRrn());
			transportJob.setLotId(dispatchUnit.getObjectId());// �����ϼ�ID
			transportJob.setDurableId(dispatchUnit.getCarrierIds());// �ؾ�ID�б�
			transportJob.setDurableType(dispatchUnit.getCarrierType());// �ؾ�����
			transportJob.setLotQty(dispatchUnit.getMainQty().toString());// �ؾ�����
			if (dispatchUnit instanceof DispatchLot) {
				transportJob.setProcessFlowId(((DispatchLot) dispatchUnit).getStepName());
			}
			transportJob.setJobId(SnowFlake.generateId().toString());
			transportJob = this.setJobFromInfo(transportJob, dispatchUnit);
			transportJob = this.setJobToInfo(transportJob, destEqp, destPosition);

			// do action
			ITransportRequestAction action = getTransportRequestAction().get(0);
			return action.transportRequest(transportJob, sc);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	/**
	 * ����LotID��ȡ����ִ���л�δִ�еİ�������
	 * 
	 * @param orgRrn
	 * @param lotId
	 * @return
	 * @throws ClientException
	 */
	public TransportJob getActiveTransportJob(long orgRrn, String lotId) throws ClientException {
		try {
			StringBuilder sql = new StringBuilder(" SELECT TransportJob FROM TransportJob TransportJob ");
			sql.append(" WHERE ");
			sql.append(ADBase.SQL_BASE_CONDITION_N);
			sql.append("AND lotId = :lotId ");
			sql.append("AND jobStatus = :jobStatus ");

			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("lotId", lotId);
			query.setParameter("jobStatus", TransportJob.JOB_STATUS_NOR);

			List<TransportJob> results = query.getResultList();
			if (results != null && !results.isEmpty()) {
				return results.get(0);
			}
			return null;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	public DispatchUnit getDispatchUnitById(long orgRrn, String objectId) throws ClientException {
		try {
			StringBuilder sql = new StringBuilder(" SELECT DispatchUnit FROM DispatchUnit DispatchUnit ");
			sql.append(" WHERE ");
			sql.append(ADBase.SQL_BASE_CONDITION_N);
			sql.append("AND objectId = :objectId ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("objectId", objectId);
			
			List<DispatchUnit> results = query.getResultList();
			if (!CollectionUtils.isEmpty(results)) {
				return results.get(0);
			}
			return null;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	

	/**
	 * ���ð�������FROM��Ϣ
	 * 
	 * @param job
	 * @param dispatchUnit
	 * @return
	 * @throws ClientException
	 */
	private TransportJob setJobFromInfo(TransportJob job, DispatchUnit dispatchUnit) throws ClientException {
		if (!StringUtil.isEmpty(dispatchUnit.getEquipmentId())) {
			job.setFromType(TransportJob.POSITION_TYPE_EQP);
			job.setFromBufferId(dispatchUnit.getEquipmentId());
			job.setFromPortId(dispatchUnit.getPortId());

		} else if (!StringUtil.isEmpty(dispatchUnit.getWarehouseId())) {
			job.setFromType(TransportJob.POSITION_TYPE_BUF);
			job.setFromBufferId(dispatchUnit.getWarehouseId());
			job.setFromRackId(dispatchUnit.getLocatorId());
		} else {
			// ��ϻλ��δ֪ʱ���ɰ���
			throw new ClientException(GtmExceptions.UNKHOW_CARRIER_LOCATION);
		}
		return job;
	}

	/**
	 * ���ð�������TO��Ϣ
	 * 
	 * @param job
	 * @param dispatchUnit
	 * @return
	 * @throws ClientException
	 */
	private TransportJob setJobToInfo(TransportJob job, String destEqp, String destPort) throws ClientException {
		GtmBuffer toBuf = gtmManager.getBuffer(destEqp, true);
		if (toBuf.isBuffer()) {
			job.setToBufferId(destEqp);
			job.setToRackId(destPort);
			job.setToType(TransportJob.POSITION_TYPE_BUF);
		} else if (toBuf.isEqp()) {
			job.setToBufferId(destEqp);
			job.setToPortId(destPort);
			job.setToType(TransportJob.POSITION_TYPE_EQP);
		} else {
			throw new ClientException(GtmExceptions.DESTINATION_ERROR);
		}
		return job;
	}
	
	/**
	 * ���Ϳ�ʼ����
	 * -����DispatchUnit��ǰλ���Լ�����״̬
	 * -���°�������״̬
	 * 
	 * @param job ��������
	 * @param sc
	 * @throws ClientException
	 */
	public void transportJobStart(TransportJob job, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			//Get DispatchUnit
			DispatchUnit dispatchUnit = getDispatchUnitById(sc.getOrgRrn(), job.getLotId());
			if (dispatchUnit != null) {
				dispatchUnit.setUpdated(sc.getTransTime());
				dispatchUnit.setUpdatedBy(sc.getUserName());
				dispatchUnit.setEquipmentId(null);
				dispatchUnit.setPortId(null);
				dispatchUnit.setWarehouseId(null);
				dispatchUnit.setLocatorId(null);
				dispatchUnit.setArrivedTime(null);
				dispatchUnit.setTransferState(LoaderContext.TRANSFERSTATE_AUT);
				em.merge(dispatchUnit);
			}
			//Save Job
			job.setLotId(dispatchUnit.getObjectId());
			job.setJobStatus(TransportJob.JOB_STATUS_NOR);
			gtmManager.saveTransportJob(TransportHis.TRANS_STR, job, sc);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * ������ɱ��� 
	 * @param job ��������
	 * @param sc
	 * @throws ClientException
	 */
	public void transportJobComplete(TransportJob job, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			//Get DispatchUnit
			DispatchUnit dispatchUnit = getDispatchUnitById(sc.getOrgRrn(), job.getLotId());
			
			GtmBuffer endBuf = gtmManager.getBuffer(job.getEndBufferId(), true);//job.getEndBufferId() Ϊ���յ����λ��,��������������е�Ŀ�ĵز�һ��
			if (endBuf.isEqp()) {
				job.setEndType(TransportJob.POSITION_TYPE_EQP);
			} else if (endBuf.isBuffer()) {
				job.setEndType(TransportJob.POSITION_TYPE_BUF);
			}
			if (TransportJob.POSITION_TYPE_EQP.equals(job.getEndType())) {
				dispatchUnit.setWarehouseId(null);
				dispatchUnit.setLocatorId(null);
				dispatchUnit.setEquipmentId(job.getEndBufferId());
				dispatchUnit.setPortId(job.getEndPortId());
				dispatchUnit.setObjectState(DispatchUnit.STATE_COMPLETE);//���͵��豸��DispatchUnit״̬��Ϊ����
			} else if (TransportJob.POSITION_TYPE_BUF.equals(job.getEndType())) {
				dispatchUnit.setWarehouseId(job.getEndBufferId());
				dispatchUnit.setLocatorId(job.getEndRackId());
				dispatchUnit.setEquipmentId(null);
				dispatchUnit.setPortId(null);
				dispatchUnit.setArrivedTime(sc.getTransTime());
				dispatchUnit.setTransferState(LoaderContext.TRANSFERSTATE_WAT);
			}
			dispatchUnit.setUpdated(sc.getTransTime());
			dispatchUnit.setUpdatedBy(sc.getUserName());
			em.merge(dispatchUnit);
			
			//Save Job
			job.setLotId(dispatchUnit.getObjectId());
			job.setJobStatus(TransportJob.JOB_STATUS_CMP);
			gtmManager.saveTransportJob(TransportHis.TRANS_RES, job, sc);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			throw ExceptionManager.handleException(e);
		}
	}
	
	/**
	 * ����������
	 * @param job ��������
	 * @param sc
	 * @throws ClientException
	 */
	public void transportJobCancel(TransportJob job, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			//Get DispatchUnit
			DispatchUnit dispatchUnit = getDispatchUnitById(sc.getOrgRrn(), job.getLotId());
			if (LoaderContext.TRANSFERSTATE_AUT.equals(dispatchUnit.getTransferState())) {
				// ��鿨ϻ�İ���״̬���Ѿ���ʼ���͵Ŀ�ϻ��������ȡ��
				throw new ClientException(GtmExceptions.CST_TRANSFER_STATE_NOT_ALLOWED);
			}
			
			if (TransportJob.JOB_STATUS_ERR.equals(job.getJobStatus())) {
				// �쳣������ֱ��ɾ�������ٸ�MCS�·�ȡ������
				gtmManager.saveTransportJob(TransportJob.TRANSTYPE_CANCEL, job, sc);
			} else if (TransportJob.JOB_STATUS_DEL.equals(job.getJobStatus())
					|| TransportJob.JOB_STATUS_CAN.equals(job.getJobStatus())) {
				// �ѱ�ȡ����ɾ�������񣬲���ȡ��
				throw new ClientParameterException(GtmExceptions.JOB_STATUS_NOT_ALLOWED, job.getJobStatus());
			}
			
			//do action
			ITransportCancelRequestAction action = getTransportCancelRequestAction().get(0);
			DispatchResult dispatchResult = action.transportCancelRequest(job, sc);
			if (dispatchResult.isSuccess()) {
				// ��������״̬����¼ȡ����ʷ
				job.setJobStatus(TransportJob.JOB_STATUS_CAN);
				gtmManager.saveTransportJob(TransportHis.TRANSTYPE_CANCEL, job, sc);
				
				// ���°���״̬
				dispatchUnit.setUpdated(sc.getTransTime());
				dispatchUnit.setUpdatedBy(sc.getUserName());
				dispatchUnit.setTransferState(LoaderContext.TRANSFERSTATE_WAT);
				em.merge(dispatchUnit);
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			throw ExceptionManager.handleException(e);
		}
	}
	
	/**
	 * ����������(Ŀ�ĵر��)
	 * @param job ��������
	 * @param sc
	 * @throws ClientException
	 */
	public void transportJobChange(TransportJob job, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			if (TransportJob.JOB_STATUS_ERR.equals(job.getJobStatus())) {
				// �쳣�����񣬲��ɱ��
				throw new ClientParameterException(GtmExceptions.JOB_STATUS_NOT_ALLOWED, job.getJobStatus());
			} else if (TransportJob.JOB_STATUS_DEL.equals(job.getJobStatus())
					|| TransportJob.JOB_STATUS_CAN.equals(job.getJobStatus())) {
				// �ѱ�ȡ����ɾ�������񣬲��ɱ��
				throw new ClientParameterException(GtmExceptions.JOB_STATUS_NOT_ALLOWED, job.getJobStatus());
			}
			//change job
			this.setJobToInfo(job, job.getToBufferId(), job.getToPortId());
			
			//do action
			ITransportChangeRequestAction action = getTransportChangeRequestAction().get(0);
			DispatchResult dispatchResult = action.transportChangeRequest(job, sc);
			if (dispatchResult.isSuccess()) {
				// Job����ɹ�����¼�����ʷ�������Job��Ϣ��ChangeReport���޸�
				job.setJobStatus(TransportJob.JOB_STATUS_NOR);
				gtmManager.saveTransportJob(TransportHis.TRANS_CHQ, job, sc);
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			throw ExceptionManager.handleException(e);
		}
	}
	
	
	/**
	 * ���ذ�������
	 * @param jobId ��������id
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public TransportJob getTransportJob(String jobId, SessionContext sc) throws ClientException {
		try {
			List<TransportJob> jobs = adManager.getEntityList(0L, TransportJob.class, 1, "jobId = '" + jobId + "'",
					null);
			if (jobs == null || jobs.isEmpty()) {
				throw new ClientException(GtmExceptions.COMMAND_NOT_EXIST);
			}
			return jobs.get(0);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			throw ExceptionManager.handleException(e);
		}
	}
}
