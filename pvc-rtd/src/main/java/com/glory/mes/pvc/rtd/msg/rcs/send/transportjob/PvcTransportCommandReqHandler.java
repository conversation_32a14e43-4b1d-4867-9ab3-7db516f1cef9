package com.glory.mes.pvc.rtd.msg.rcs.send.transportjob;

import java.util.Map;

import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.UUIDUtil;
import com.glory.mes.pvc.rtd.msg.rcs.send.PvcSendTransHandler;
import com.glory.mes.pvc.rtd.msg.service.PvcRtdTransContext;
import com.glory.msg.MessageParser;
import com.glory.msg.MessageParserFactory;
import com.glory.msg.MessageParserModel;
import com.glory.msg.RequestHeader;
import com.glory.msg.Response;
import com.glory.msg.trans.TransContext;

public class PvcTransportCommandReqHandler extends PvcSendTransHandler {

    @Override
	public TransContext internalExecute(TransContext ctx) throws Exception {
    	PvcRtdTransContext context = (PvcRtdTransContext)ctx;		
		MessageParser parser = getMessageParser(PvcTransportCommandReqRequest.MESSAGE_NAME);
			
		PvcTransportCommandReqRequest request = new PvcTransportCommandReqRequest();
		SessionContext sc = context.getSessionContext();
    	RequestHeader header = new RequestHeader();
    	header.setMessageName(PvcTransportCommandReqRequest.MESSAGE_NAME);
    	header.setOrgName(sc.getOrgName());
    	header.setOrgRrn(sc.getOrgRrn());
    	header.setTransactionId(UUIDUtil.base58Uuid());
    	header.setUserName(sc.getUserName());
    	request.setHeader(header);

    	Map<String, Object> requestMap = context.getRequestMap();
		if (!requestMap.containsKey(PvcTransportCommandReqRequest.MESSAGE_NAME)) {
			return context;
		} 
		
		PvcTransportCommandReqRequestBody body = (PvcTransportCommandReqRequestBody) requestMap
				.get(PvcTransportCommandReqRequest.MESSAGE_NAME);
    	request.setBody(body);
    	
		context.setRequest(parser.writerRequest(request));
		return context;
	}
	
	public Object executeResponse(Response response, TransContext context) throws Exception {
		PvcTransportCommandReqResponse entityResponse = (PvcTransportCommandReqResponse)response;	
		return entityResponse;
	}
	
	/**
	 * ע����Ϣ������
	 */
	public void initMessageParser() {
		MessageParserModel parserModel = new MessageParserModel();
		parserModel.setMessageName(PvcTransportCommandReqRequest.MESSAGE_NAME);
		parserModel.setRequestClass(PvcTransportCommandReqRequest.class);
		parserModel.setResponseClass(PvcTransportCommandReqResponse.class);
		MessageParserFactory.registerMessageParser(PvcTransportCommandReqRequest.MESSAGE_NAME, parserModel);
	}
	
	/**
	 * �����Ϣ������
	 */
	@Override
	public MessageParser getMessageParser() {
		return getMessageParser(PvcTransportCommandReqRequest.MESSAGE_NAME);
	}

	
}
