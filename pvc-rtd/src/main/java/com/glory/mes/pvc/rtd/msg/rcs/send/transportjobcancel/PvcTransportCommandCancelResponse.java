package com.glory.mes.pvc.rtd.msg.rcs.send.transportjobcancel;


import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Response;

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.NONE)
public class PvcTransportCommandCancelResponse extends Response {
	private static final long serialVersionUID = 1L;
	
	@XmlElement(name="Body")
	private PvcTransportCommandCancelResponseBody body;

	public PvcTransportCommandCancelResponseBody getBody() {
		return body;
	}

	public void setBody(PvcTransportCommandCancelResponseBody body) {
		this.body = body;
	}

}
