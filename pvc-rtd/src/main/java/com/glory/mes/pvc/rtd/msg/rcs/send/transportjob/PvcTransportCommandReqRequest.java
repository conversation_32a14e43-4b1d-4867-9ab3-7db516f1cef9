package com.glory.mes.pvc.rtd.msg.rcs.send.transportjob;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Request;

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.NONE)
public class PvcTransportCommandReqRequest extends Request {
	private static final long serialVersionUID = 1L;
	
	public static final String MESSAGE_NAME = "TransportJob";
	
	@XmlElement(name="Body")
	private PvcTransportCommandReqRequestBody body;

	public PvcTransportCommandReqRequestBody getBody() {
		return body;
	}
	
	public void setBody(PvcTransportCommandReqRequestBody body) {
		this.body = body;
	}
}