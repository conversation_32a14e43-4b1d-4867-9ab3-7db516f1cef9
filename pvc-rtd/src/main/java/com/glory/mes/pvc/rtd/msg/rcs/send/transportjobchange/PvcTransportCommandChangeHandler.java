package com.glory.mes.pvc.rtd.msg.rcs.send.transportjobchange;

import java.util.Map;

import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.UUIDUtil;
import com.glory.mes.pvc.rtd.msg.rcs.send.PvcSendTransHandler;
import com.glory.mes.pvc.rtd.msg.service.PvcRtdTransContext;
import com.glory.msg.MessageParser;
import com.glory.msg.MessageParserFactory;
import com.glory.msg.MessageParserModel;
import com.glory.msg.RequestHeader;
import com.glory.msg.Response;
import com.glory.msg.trans.TransContext;

public class PvcTransportCommandChangeHandler extends PvcSendTransHandler {

    @Override
	public TransContext internalExecute(TransContext ctx) throws Exception {
    	PvcRtdTransContext context = (PvcRtdTransContext)ctx;		
		MessageParser parser = getMessageParser(PvcTransportCommandChangeRequest.MESSAGE_NAME);
			
		PvcTransportCommandChangeRequest request = new PvcTransportCommandChangeRequest();
		SessionContext sc = context.getSessionContext();
    	RequestHeader header = new RequestHeader();
    	header.setMessageName(PvcTransportCommandChangeRequest.MESSAGE_NAME);
    	header.setOrgName(sc.getOrgName());
    	header.setOrgRrn(sc.getOrgRrn());
    	header.setTransactionId(UUIDUtil.base58Uuid());
    	header.setUserName(sc.getUserName());
    	request.setHeader(header);

    	Map<String, Object> requestMap = context.getRequestMap();
		if (!requestMap.containsKey(PvcTransportCommandChangeRequest.MESSAGE_NAME)) {
			return context;
		} 
		
		PvcTransportCommandChangeRequestBody body = (PvcTransportCommandChangeRequestBody) requestMap
				.get(PvcTransportCommandChangeRequest.MESSAGE_NAME);
    	request.setBody(body);
    	
		context.setRequest(parser.writerRequest(request));
		return context;
	}
	
	public Object executeResponse(Response response, TransContext context) throws Exception {
		PvcTransportCommandChangeResponse entityResponse = (PvcTransportCommandChangeResponse)response;	
		return entityResponse;
	}
	
	/**
	 * ע����Ϣ������
	 */
	public void initMessageParser() {
		MessageParserModel parserModel = new MessageParserModel();
		parserModel.setMessageName(PvcTransportCommandChangeRequest.MESSAGE_NAME);
		parserModel.setRequestClass(PvcTransportCommandChangeRequest.class);
		parserModel.setResponseClass(PvcTransportCommandChangeResponse.class);
		MessageParserFactory.registerMessageParser(PvcTransportCommandChangeRequest.MESSAGE_NAME, parserModel);
	}
	
	/**
	 * �����Ϣ������
	 */
	@Override
	public MessageParser getMessageParser() {
		return getMessageParser(PvcTransportCommandChangeRequest.MESSAGE_NAME);
	}

	
}
