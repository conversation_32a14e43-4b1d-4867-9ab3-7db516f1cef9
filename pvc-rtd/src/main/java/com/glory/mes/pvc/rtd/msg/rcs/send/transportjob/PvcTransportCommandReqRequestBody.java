package com.glory.mes.pvc.rtd.msg.rcs.send.transportjob;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

import com.glory.msg.RequestBody;

@XmlAccessorType(XmlAccessType.NONE)
public class PvcTransportCommandReqRequestBody extends RequestBody {
	private static final long serialVersionUID = 1L;
	
	@XmlElement(name = "JOBID")
	private String jobId;

	@XmlElement(name = "FROMBUFID")
	private String fromBufId;

	@XmlElement(name = "FROMRACKID")
	private String fromRackId;

	@XmlElement(name = "FROMPORTID")
	private String fromPortId;

	@XmlElement(name = "TOBUFID")
	private String toBufId;

	@XmlElement(name = "TOPORTID")
	private String toPortId;

	@XmlElement(name = "TORACKID")
	private String toRackId;

	@XmlElement(name = "PRIORITY")
	private String priority;

	@XmlElement(name = "TRANSTIME")
	private String transTime;

	public String getJobId() {
		return jobId;
	}

	public void setJobId(String jobId) {
		this.jobId = jobId;
	}

	public String getFromBufId() {
		return fromBufId;
	}

	public void setFromBufId(String fromBufId) {
		this.fromBufId = fromBufId;
	}

	public String getFromRackId() {
		return fromRackId;
	}

	public void setFromRackId(String fromRackId) {
		this.fromRackId = fromRackId;
	}

	public String getFromPortId() {
		return fromPortId;
	}

	public void setFromPortId(String fromPortId) {
		this.fromPortId = fromPortId;
	}

	public String getToBufId() {
		return toBufId;
	}

	public void setToBufId(String toBufId) {
		this.toBufId = toBufId;
	}

	public String getToPortId() {
		return toPortId;
	}

	public void setToPortId(String toPortId) {
		this.toPortId = toPortId;
	}

	public String getToRackId() {
		return toRackId;
	}

	public void setToRackId(String toRackId) {
		this.toRackId = toRackId;
	}

	public String getPriority() {
		return priority;
	}

	public void setPriority(String priority) {
		this.priority = priority;
	}

	public String getTransTime() {
		return transTime;
	}

	public void setTransTime(String transTime) {
		this.transTime = transTime;
	}

}
