package com.glory.mes.pvc.rtd.msg.rcs.send.transportjob;


import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Response;

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.NONE)
public class PvcTransportCommandReqResponse extends Response {
	private static final long serialVersionUID = 1L;
	
	@XmlElement(name="Body")
	private PvcTransportCommandReqResponseBody body;

	public PvcTransportCommandReqResponseBody getBody() {
		return body;
	}

	public void setBody(PvcTransportCommandReqResponseBody body) {
		this.body = body;
	}

}
