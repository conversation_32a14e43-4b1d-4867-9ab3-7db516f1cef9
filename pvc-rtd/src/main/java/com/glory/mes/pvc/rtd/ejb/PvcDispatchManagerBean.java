package com.glory.mes.pvc.rtd.ejb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.ejb.EJB;
import javax.ejb.Remote;
import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.hibernate.transform.Transformers;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ExceptionManager;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.SnowFlake;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.pvc.rtd.client.PvcDispatchManager;
import com.glory.mes.pvc.rtd.client.PvcGtmManager;
import com.glory.mes.pvc.rtd.model.DispatchCarrier;
import com.glory.mes.pvc.rtd.model.DispatchLot;
import com.glory.mes.pvc.rtd.model.DispatchResult;
import com.glory.mes.pvc.rtd.model.DispatchUnit;
import com.glory.rtd.client.DispatchManager;
import com.glory.rtd.client.RTDManager;
import com.glory.rtd.domain.RtdCarrier;
import com.glory.rtd.domain.RtdEquipment;
import com.glory.rtd.domain.RtdLot;
import com.glory.rtd.domain.RtdPort;
import com.glory.rtd.domain.RtdStock;
import com.glory.rtd.loader.LoaderContext;
import com.glory.rtd.model.DispatchLog;
import com.glory.rtd.model.RtdRule;
import com.glory.rtd.model.RuleSet;
import com.google.common.collect.Lists;

@Stateless
@Remote(PvcDispatchManager.class)
public class PvcDispatchManagerBean implements PvcDispatchManager {

	private static final Logger logger = Logger.getLogger(PvcDispatchManagerBean.class);
	
	@PersistenceContext
	private EntityManager em;

	@EJB
	private ADManager adManager;
	
	@EJB
	private RTDManager rtdManager;
	
	@EJB
	private DispatchManager dispatchManager;
	
	@EJB
	private PvcGtmManager pvcGtmManager;
	
	/**
	 * ִ���ɹ�LoadRequest(WhatNext)
	 * -�����ɹ������ҳ�BestLot(�����ɹ��߼����ɹ�����)
	 * @param inContext
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public void doLoadRequest(String equipmentId, String portId, SessionContext sc) throws ClientException {
		try {
			RtdPort port = getPort(sc.getOrgRrn(), equipmentId, portId);
			
			if (LoaderContext.PORTTYPE_PL.equals(port.getPortType())) {
				this.dispatchForLoadRequest(port.getPortRrn(), LoaderContext.DSPTYPE_LOT, sc);
				
			} else if (LoaderContext.PORTTYPE_PU.equals(port.getPortType())) {
				this.dispatchForLoadRequest(port.getPortRrn(), LoaderContext.DSPTYPE_CARRIER, sc);
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * ִ���ɹ�UnLoadRequest(WhereNext)
	 * -�����ɹ������ҳ�Ŀ���豸��Ŀ��ֿ�
	 * @param dispatchContext
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public void doUnLoadRequest(String equipmentId, String portId, List<String> carrierIds, SessionContext sc) throws ClientException {
		try {
			RtdPort port = getPort(sc.getOrgRrn(), equipmentId, portId);
			
			//1. �����ɹ���Ԫ��Ϣ
			DispatchUnit dispatchUnit = null;
			if (CollectionUtils.isEmpty(carrierIds)) {
				//���ೡ�����޷������ؾ�/���ε�λ��
				dispatchUnit = generateNewDispatchUnit(port, sc);
			} else {
				List<RtdCarrier> carriers = carrierIds.stream().map(x -> {
					RtdCarrier carrier = getCarrier(sc.getOrgRrn(), x);
					return carrier;
				}).collect(Collectors.toList());
				dispatchUnit = this.generateNewDispatchUnit(port, carriers, sc);
			}
			
			if (dispatchUnit == null) {
				throw new ClientException("error.generate_dispatch_unit_fail");
			}
			//2. �ɹ�+�·���������
			this.dispatchForUnloadRequest(port.getPortRrn(), dispatchUnit, sc);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * �豸��������ʱ������Port�ϵ��ؾ������ɹ���Ԫ��Ϣ
	 * @param equipmentId �豸ID
	 * @param portId PortID
	 * @param sourceLots ���ɹ�����
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public DispatchUnit generateNewDispatchUnit(RtdPort rtdPort, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			if (LoaderContext.PORTTYPE_PL.equals(rtdPort.getPortType())) {
				//PL���͵�Port������ʱ����Ϊ�ǿ��ؾ�
				DispatchCarrier dispatchCarrier = new DispatchCarrier();
				dispatchCarrier.setOrgRrn(sc.getOrgRrn());
				dispatchCarrier.setCreated(sc.getTransTime());
				dispatchCarrier.setCreatedBy(sc.getUserName());
				dispatchCarrier.setObjectId(String.valueOf(SnowFlake.generateId()));
				dispatchCarrier.setObjectState(DispatchUnit.STATE_ACTIVE);
				dispatchCarrier.setEquipmentId(rtdPort.getEquipmentId());
				dispatchCarrier.setPortId(rtdPort.getPortId());
				dispatchCarrier.setTransferState(LoaderContext.TRANSFERSTATE_WAT);
				dispatchCarrier.setHoldState(DispatchUnit.HOLDSTATE_OFF);
				em.persist(dispatchCarrier);
				
				return dispatchCarrier;
			} else if (LoaderContext.PORTTYPE_PU.equals(rtdPort.getPortType())) {
				//PU���͵�Port������ʱ����Ϊ��ʵ�ؾ�
				//���޷����ƻ���/������һ������, ���ೡ�������ʹ��Ŀ��ֿ���Դ�ֿ��������涨�ؾߵ��ƶ���Χ
				DispatchLot dispatchLot = new DispatchLot();
				dispatchLot.setOrgRrn(sc.getOrgRrn());
				dispatchLot.setCreated(sc.getTransTime());
				dispatchLot.setCreatedBy(sc.getUserName());
				dispatchLot.setObjectId(String.valueOf(SnowFlake.generateId()));
				dispatchLot.setObjectState(DispatchUnit.STATE_ACTIVE);
				dispatchLot.setQueueTime(sc.getTransTime());
				dispatchLot.setEquipmentId(rtdPort.getEquipmentId());
				dispatchLot.setPortId(rtdPort.getPortId());
				dispatchLot.setTransferState(LoaderContext.TRANSFERSTATE_WAT);
				dispatchLot.setHoldState(DispatchUnit.HOLDSTATE_OFF);
				em.persist(dispatchLot);
				
				return dispatchLot;
			}
			return null;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * �豸��������ʱ������Port�ϵ��ؾ������ɹ���Ԫ��Ϣ
	 * @param equipmentId �豸ID
	 * @param portId PortID
	 * @param sourceLots ���ɹ�����
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public DispatchUnit generateNewDispatchUnit(RtdPort rtdPort, List<RtdCarrier> sourceCarriers,
			SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			List<RtdLot> sourceLots = Lists.newArrayList();
			for (RtdCarrier carrier : sourceCarriers) {
				RtdLot sourceLot = getLotByDurableId(sc.getOrgRrn(), carrier.getDurableId());
				if (sourceLot != null) {
					sourceLots.add(sourceLot);
				}
			}
			
			String[] carrierIds = sourceCarriers.stream().map(x -> x.getDurableId()).toArray(String[] :: new);
			String[] carrierRrns = sourceCarriers.stream().map(x -> x.getDurableRrn().toString()).toArray(String[] :: new);
			if (!CollectionUtils.isEmpty(sourceLots)) {
				String[] lotIds = sourceLots.stream().map(x -> x.getLotId()).toArray(String[] :: new);
				String[] lotRrns = sourceLots.stream().map(x -> x.getLotRrn().toString()).toArray(String[] :: new);
				Date maxQueueTime = sourceLots.stream().filter(x -> x.getQueueTime() != null).map(x -> x.getQueueTime()).max(Date :: compareTo).get();
				
				DispatchLot dispatchLot = new DispatchLot();
				dispatchLot.setOrgRrn(sc.getOrgRrn());
				dispatchLot.setCreated(sc.getTransTime());
				dispatchLot.setCreatedBy(sc.getUserName());
				dispatchLot.setObjectId(String.valueOf(SnowFlake.generateId()));
				dispatchLot.setObjectState(DispatchUnit.STATE_ACTIVE);
				dispatchLot.setMainQty(new BigDecimal(sourceLots.size()));
				dispatchLot.setStepName(sourceLots.get(0).getStepName());
				dispatchLot.setStepRrn(sourceLots.get(0).getStepRrn());
				dispatchLot.setQueueTime(maxQueueTime);
				dispatchLot.setEquipmentId(rtdPort.getEquipmentId());
				dispatchLot.setPortId(rtdPort.getPortId());
				dispatchLot.setTransferState(LoaderContext.TRANSFERSTATE_WAT);
				dispatchLot.setHoldState(DispatchUnit.HOLDSTATE_OFF);
				dispatchLot.setLotIds(StringUtil.concat(lotIds, LoaderContext.SPLITE_CHARACTER));
				dispatchLot.setLotRrns(StringUtil.concat(lotRrns, LoaderContext.SPLITE_CHARACTER));
				dispatchLot.setCarrierType(sourceCarriers.get(0).getDurableType());
				dispatchLot.setCarrierIds(StringUtil.concat(carrierIds, LoaderContext.SPLITE_CHARACTER));
				dispatchLot.setCarrierRrns(StringUtil.concat(carrierRrns, LoaderContext.SPLITE_CHARACTER));
				em.persist(dispatchLot);
				
				return dispatchLot;
			} else {
				DispatchCarrier dispatchCarrier = new DispatchCarrier();
				dispatchCarrier.setOrgRrn(sc.getOrgRrn());
				dispatchCarrier.setCreated(sc.getTransTime());
				dispatchCarrier.setCreatedBy(sc.getUserName());
				dispatchCarrier.setObjectId(String.valueOf(SnowFlake.generateId()));
				dispatchCarrier.setObjectState(DispatchUnit.STATE_ACTIVE);
				dispatchCarrier.setMainQty(new BigDecimal(sourceCarriers.size()));
				dispatchCarrier.setEquipmentId(rtdPort.getEquipmentId());
				dispatchCarrier.setPortId(rtdPort.getPortId());
				dispatchCarrier.setTransferState(LoaderContext.TRANSFERSTATE_WAT);
				dispatchCarrier.setHoldState(DispatchUnit.HOLDSTATE_OFF);
				dispatchCarrier.setCarrierType(sourceCarriers.get(0).getDurableType());
				dispatchCarrier.setCarrierIds(StringUtil.concat(carrierIds, LoaderContext.SPLITE_CHARACTER));
				dispatchCarrier.setCarrierRrns(StringUtil.concat(carrierRrns, LoaderContext.SPLITE_CHARACTER));
				em.persist(dispatchCarrier);
				
				return dispatchCarrier;
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * ����DispatchLot��ȡRtdLot����
	 * @param objectRrn
	 * @return
	 * @throws ClientException
	 */
	public RtdLot getDispatchLot(long objectRrn) throws ClientException {
		try {
			StringBuilder sql = new StringBuilder(" SELECT ");
			sql.append(" OBJECT_RRN LOT_RRN, ");
			sql.append(" OBJECT_ID LOT_ID, ");
			sql.append(" LOCK_VERSION LOT_LOCK, ");
			sql.append(" STEP_RRN, ");
			sql.append(" STEP_NAME, ");
			sql.append(" QUEUE_TIME ");
			sql.append(" FROM PVC_DISPATCH_UNIT ");
			sql.append(" WHERE 1 = 1 ");
			sql.append(" OBJECT_RRN = :objectRrn ");
			Query query = em.createNativeQuery(sql.toString());
			query.setParameter("objectRrn", objectRrn);
			query.unwrap(org.hibernate.Query.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
			List<Map> resultList = query.getResultList();
			
			if (resultList != null && !resultList.isEmpty()) {
				RtdLot rtdLot = new RtdLot();
				rtdLot.setData(resultList.get(0));
				return rtdLot;
			}
			return null;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * LoadRequestʱ����, RTD�����ҳ�Lot,Carrier, GTM�����°���ָ��
	 * @param portRrn
	 * @param dispatchType
	 * @param sc
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public void dispatchForLoadRequest(long portRrn, String dispatchType, SessionContext sc) throws ClientException {
		sc.buildTransInfo();
		
		// Get Port
		RtdPort rtdPort = dispatchManager.getPort(portRrn);

		DispatchLog dispatchLog = new DispatchLog(rtdPort);
		dispatchLog.setRequestType(LoaderContext.LOAD_REQUEST);
		dispatchLog.setDispatchCategory(RtdRule.RULECATE_WHATNEXT);
		try {
			DispatchResult dispatchResult = null;
			LoaderContext outContext = null;
			LoaderContext context = dispatchManager.initContext(rtdPort);
			// ��������ɹ�3�Σ�ÿ�������ɹ����1��
			int i = 0;
			while (i <= 3) {
				i++;
				dispatchLog.resetResult(); // �����ɹ�ʱ�������ɹ���־����Ϣ
				context.resetResultMsg(); // �����ɹ�ʱ������context���ɹ���Ϣ
				
				RuleSet ruleSet = rtdManager.getRuleByEquipmentId(RtdRule.RULECATE_WHATNEXT, context);
				
				if (LoaderContext.DSPTYPE_LOT.equals(dispatchType)) {
					if (ruleSet.isUseQueue()) {
						//�豸Queue�ɹ�
						outContext = dispatchManager.dispatchLotQueue(context);
					} else {
						//�����ɹ�
						outContext = dispatchManager.dispatchLot(ruleSet, context);
					}
					List<RtdLot> lotList = (List<RtdLot>) outContext.get(LoaderContext.LOT_LIST);
					if (lotList == null || lotList.isEmpty()) {
						break;
					} else {
						RtdLot lot = lotList.get(0);
						DispatchUnit dispatchUnit = em.find(DispatchUnit.class, lot.getLotRrn());
						dispatchResult = pvcGtmManager.transportJobRequest(dispatchUnit, context.getEquipmentId(), context.getPortId(), sc);
						if (dispatchResult.isSuccess()) {
							dispatchManager.changePortTransferState(portRrn, LoaderContext.STATE_RESERVEDTOLOAD);
							break;
						} else {
							// ���ͱ��ܾ��ģ��������ɹ���������ʱ�����޷����͵�����
							context.getNgResults().add(lot.getLotId());
							continue;
						}
					}
				} else if (LoaderContext.DSPTYPE_CARRIER.equals(dispatchType)) {
					outContext = dispatchManager.dispatchCarrier(ruleSet, context);
					List<RtdCarrier> carrierList = (List<RtdCarrier>) outContext.get(LoaderContext.CARRIER_LIST);
					if (carrierList == null || carrierList.isEmpty()) {
						break;
					} else {
						RtdCarrier carrier = carrierList.get(0);
						DispatchUnit dispatchUnit = em.find(DispatchUnit.class, carrier.getDurableRrn());
						dispatchResult = pvcGtmManager.transportJobRequest(dispatchUnit, context.getEquipmentId(), context.getPortId(), sc);
						if (dispatchResult.isSuccess()) {
							dispatchManager.changePortTransferState(portRrn, LoaderContext.STATE_RESERVEDTOLOAD);
							break;
						} else {
							// ���ͱ��ܾ��ģ��������ɹ���������ʱ�����޷����͵�����
							context.getNgResults().add(carrier.getDurableId());
							continue;
						}
					}
				}
			}
			
			dispatchLog.setResultCode(dispatchResult.isSuccess() ? DispatchLog.RESULT_CODE_OK : DispatchLog.RESULT_CODE_NG);
			dispatchLog.setResult(dispatchResult.getReturnMsg());
			dispatchLog.setResultComment(dispatchResult.getReturnComment());
		} catch (Exception e) {
			dispatchLog.setResultCode(DispatchLog.RESULT_CODE_NG);
			if (e instanceof ClientException) {
				dispatchLog.setResultComment(((ClientException) e).getErrorCode());
			} else {
				dispatchLog.setResultComment(e.getMessage());
			}
			logger.error(e.getMessage(), e);
			throw ExceptionManager.handleException(e);
		} finally {
			rtdManager.saveDispatchLog(dispatchLog, sc);
		}
	}
	
	/**
	 * �豸Port����UnloadRequestʱ�ɹ����ҳ�Ŀ�ĵز����Ͱ���ָ��
	 * �˿տ�ʱֱ�ӰᵽStock
	 * ��Lotʱ�������E2E,�����ȿ���E2E
	 * @param portRrn
	 * @param dispatchUnit
	 * @param sc
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public void dispatchForUnloadRequest(long portRrn, DispatchUnit dispatchUnit, SessionContext sc) throws ClientException {
		sc.buildTransInfo();
		// Get Port
		RtdPort rtdPort = dispatchManager.getPort(portRrn);

		DispatchLog dispatchLog = new DispatchLog(rtdPort);
		dispatchLog.setRequestType(LoaderContext.UNLOAD_REQUEST);
		dispatchLog.setDispatchCategory(RtdRule.RULECATE_WHERENEXT);

		DispatchResult dispatchResult = null;
		LoaderContext outContext = null;
		LoaderContext context = dispatchManager.initContext(rtdPort);
		try {
			// ��������ɹ�3�Σ�ÿ�������ɹ����1��
			int i = 0;
			while (i <= 3) {
				i++;
				dispatchLog.resetResult(); // �����ɹ�ʱ�������ɹ���־����Ϣ
				context.resetResultMsg(); // �����ɹ�ʱ������context���ɹ���Ϣ
				
				RuleSet ruleSet = rtdManager.getRuleByEquipmentId(RtdRule.RULECATE_WHERENEXT, context);
				
				if (dispatchUnit instanceof DispatchLot) {
					RtdLot rtdLot = getDispatchLot(dispatchUnit.getObjectRrn());
					context.put(LoaderContext.LOT, rtdLot);
					
					List<RtdEquipment> nextEquipmentList = new ArrayList<RtdEquipment>();
					List<RtdStock> stockList = new ArrayList<RtdStock>();
					
					if (ruleSet.isUseP2P()) {
						//���ȿ���P2P�ɹ�
						outContext = dispatchManager.dispatchEquipment(ruleSet, context);
						nextEquipmentList = (List<RtdEquipment>) outContext.get(LoaderContext.NEXT_EQUIPMENT_LIST);
					} else if (ruleSet.isUseQueue()) {
						//�豸Queue�ɹ�
						outContext = dispatchManager.dispatchEquipmentQueue(rtdLot, ruleSet, context);
						stockList = (List<RtdStock>) outContext.get(LoaderContext.STOCK_LIST);
					}
					
					if (nextEquipmentList != null && !nextEquipmentList.isEmpty()) {
						RtdEquipment nextEqp = nextEquipmentList.get(0);
						dispatchResult = pvcGtmManager.transportJobRequest(dispatchUnit, nextEqp.getEquipmentId(), nextEqp.getPortId(), sc);
						if (dispatchResult.isSuccess()) {
							dispatchManager.changePortTransferState(portRrn, LoaderContext.STATE_RESERVEDTOUNLOAD);
							break;
						} else {
							// ���ͱ��ܾ��ģ��������ɹ���������ʱ�����޷����͵�����
							context.getNgResults().add(nextEqp.getEquipmentId());
							continue;
						}
					} else if (stockList != null && !stockList.isEmpty()) {
						RtdStock stock = stockList.get(0);
						dispatchResult = pvcGtmManager.transportJobRequest(dispatchUnit, stock.getWarehouseId(), stock.getZoneId(), sc);
						if (dispatchResult.isSuccess()) {
							dispatchManager.changePortTransferState(portRrn, LoaderContext.STATE_RESERVEDTOUNLOAD);
							break;
						} else {
							// ���ͱ��ܾ��ģ��������ɹ���������ʱ�����޷����͵�����
							context.getNgResults().add(stock.getWarehouseId() + stock.getZoneId());
							continue;
						}
					}
				}
				context.resetResultMsg();
				context.setDurableId(dispatchUnit.getObjectId());
				outContext = dispatchManager.dispatchStock(ruleSet, context);
				List<RtdStock> stockList = (List<RtdStock>) outContext.get(LoaderContext.STOCK_LIST);
				if (stockList == null || stockList.isEmpty()) {
					break;
				} else {
					RtdStock stock = stockList.get(0);
					dispatchResult = pvcGtmManager.transportJobRequest(dispatchUnit, stock.getWarehouseId(), stock.getZoneId(), sc);
					if (dispatchResult.isSuccess()) {
						dispatchManager.changePortTransferState(portRrn, LoaderContext.STATE_RESERVEDTOUNLOAD);
						break;
					} else {
						// ���ͱ��ܾ��ģ��������ɹ���������ʱ�����޷����͵�����
						context.getNgResults().add(stock.getWarehouseId() + stock.getZoneId());
						continue;
					}
				}
			}
			
			dispatchLog.setResultCode(dispatchResult.isSuccess() ? DispatchLog.RESULT_CODE_OK : DispatchLog.RESULT_CODE_NG);
			dispatchLog.setResult(dispatchResult.getReturnMsg());
			dispatchLog.setResultComment(dispatchResult.getReturnComment());
		} catch (Exception e) {
			dispatchLog.setResultCode(DispatchLog.RESULT_CODE_NG);
			if (e instanceof ClientException) {
				dispatchLog.setResultComment(((ClientException) e).getErrorCode());
			} else {
				dispatchLog.setResultComment(e.getMessage());
			}
			logger.error(e.getMessage(), e);
			throw ExceptionManager.handleException(e);

		} finally {
			rtdManager.saveDispatchLog(dispatchLog, sc);
		}
	}
	
	/**
	 * ����EQPID+PortID��ȡ���µ�Port��Ϣ
	 * @param port
	 * @return
	 */
	public RtdPort getPort(long orgRrn, String equipmentId, String portId) {
		try {
			StringBuilder sql = new StringBuilder(" SELECT ");
			sql.append(" OBJECT_RRN           PORT_RRN, ");
			sql.append(" ORG_RRN               PORT_SHOP, ");
			sql.append(" LOCK_VERSION       PORT_LOCK, ");
			sql.append(" PARENT_EQP_ID      EQUIPMENT_ID, ");
			sql.append(" PARENT_EQP_RRN   EQUIPMENT_RRN, ");
			sql.append(" PORT_ID, ");
			sql.append(" PORT_TYPE, ");
			sql.append(" PORT_USE_TYPE ");
			sql.append(" FROM RAS_PORT ");
			sql.append(" WHERE 1 = 1 ");
			sql.append(" AND ORG_RRN = :orgRrn ");
			sql.append(" AND PARENT_EQP_ID = :parentEqpId ");
			sql.append(" AND PORT_ID = :portId ");
			
			Query query = em.createNativeQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("parentEqpId", equipmentId);
			query.setParameter("portId", portId);
			query.unwrap(org.hibernate.Query.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
			List<Map> results = query.getResultList();
			if (!results.isEmpty()) {
				RtdPort newPort = new RtdPort();
				newPort.setData(results.get(0));
				return newPort;
			}
			return null;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * ����EQPID+PortID��ȡ���µ�Port��Ϣ
	 * @param port
	 * @return
	 */
	public RtdCarrier getCarrier(long orgRrn, String durableId) throws ClientException {
		try {
			StringBuilder sql = new StringBuilder(" SELECT ");
			sql.append(" OBJECT_RRN DURABLE_RRN, ");
			sql.append(" DURABLE_ID, ");
			sql.append(" LOCK_VERSION DURABLE_LOCK ");
			sql.append(" FROM MM_CARRIER ");
			sql.append(" WHERE 1 = 1 ");
			sql.append(" AND ORG_RRN = :orgRrn ");
			sql.append(" AND DURABLE_ID = :durableId ");
			
			Query query = em.createNativeQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("durableId", durableId);
			query.unwrap(org.hibernate.Query.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
			List<Map> results = query.getResultList();
			if (!results.isEmpty()) {
				RtdCarrier newCarrier = new RtdCarrier();
				newCarrier.setData(results.get(0));
				return newCarrier;
			}
			return null;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * ����LotId��ȡ���µ�Lot��Ϣ
	 * @param lot
	 * @return
	 */
	public RtdLot getLotByDurableId(long orgRrn, String durableId) {
		try {
			StringBuilder sql = new StringBuilder(" SELECT ");
			sql.append(" OBJECT_RRN LOT_RRN, ");
			sql.append(" LOT_ID, ");
			sql.append(" LOT_TYPE, ");
			sql.append(" LOCK_VERSION LOT_LOCK, ");
			sql.append(" DURABLE, ");
			sql.append(" STEP_RRN, ");
			sql.append(" STEP_NAME, ");
			sql.append(" PART_NAME, ");
			sql.append(" MAIN_QTY, ");
			sql.append(" QUEUE_TIME, ");
			sql.append(" PLAN_END_DATE ");
			sql.append(" FROM WIP_LOT ");
			sql.append(" WHERE 1 = 1 ");
			sql.append(" AND ORG_RRN = :orgRrn ");
			sql.append(" AND DURABLE = :durableId ");
			Query query = em.createNativeQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("durableId", durableId);
			query.unwrap(org.hibernate.Query.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
			List<Map> results = query.getResultList();
			if (!results.isEmpty()) {
				RtdLot newLot = new RtdLot();
				newLot.setData(results.get(0));
				return newLot;
			}
			return null;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			throw new ClientException(e);
		}
	}
}
