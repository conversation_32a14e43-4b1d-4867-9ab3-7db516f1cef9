package com.glory.mes.msg.model;

import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldDateAdapter;
import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "BOM")
@XmlAccessorType(XmlAccessType.NONE)
public class XBom extends XBase {
	
	private static final long serialVersionUID = 7576336509292420133L;

	@XmlElement(name = "OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name="ORGRRN")
	protected Long orgRrn = 0L;
	
	@XmlElement(name="ISACTIVE")
	protected String isActive = "Y";
	
	@XmlElement(name = "NAME")
	private String name;

	@XmlElement(name = "DESCRIPTION")
	private String description;
	
	@XmlElement(name="VERSION")
	private Long version;
	
	@XmlElement(name="STATUS")
	private String status;

	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="ACTIVETIME")
	private Date activeTime;
	
	@XmlElement(name="ACTIVEUSER")
	private String activeUser;
	
	/**
	 * 对应物料的名称
	 */
	@XmlElement(name="MATERIALNAME")
	private String materialName;
	
	@XmlElement(name="MATERIALVERSION")
	private Long materialVersion;

	/**
	 * BOM使用类型，默认为M，制造BOM
	 */
	@XmlElement(name="BOMUSE")
	private String bomUse; 
	
	/**
	 * 对应物料的单位
	 */
	@XmlElement(name="UOMID")
	private String uomId;
	
	/**
	 * 有效期从
	 */
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="VALIDFROM")
	private Date validFrom;
	
	/**
	 * 有效期到
	 */
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="VALIDTO")
	private Date validTo;
	
	@XmlElement(name="RESERVED1")
	private String reserved1;
	
	@XmlElement(name="RESERVED2")
	private String reserved2;

	@XmlElement(name="RESERVED3")
	private String reserved3;

	@XmlElement(name="RESERVED4")
	private String reserved4;

	@XmlElement(name="RESERVED5")
	private String reserved5;

	@XmlElement(name="RESERVED6")
	private String reserved6;

	@XmlElement(name="RESERVED7")
	private String reserved7;

	@XmlElement(name="RESERVED8")
	private String reserved8;

	@XmlElement(name="RESERVED9")
	private String reserved9;

	@XmlElement(name="RESERVED10")
	private String reserved10;
	
	@XmlElementWrapper(name = "BOMLINES")
	@XmlElementRef
	private List<XBomLine> bomLines;
	
	
	public Long getOrgRrn() {
		return orgRrn;
	}
	
	public void setOrgRrn(Long orgRrn) {
		if (orgRrn == null){
			this.orgRrn = 0L;
		} else {
			this.orgRrn = orgRrn;
		}
	} 
	
	public Boolean getIsActive(){
		return "Y".equalsIgnoreCase(this.isActive) ? true : false; 
	}

	public void setIsActive(Boolean isActive) {
		this.isActive = isActive ? "Y" : "N";
	}

	public List<XBomLine> getBomLines() {
		return bomLines;
	}

	public void setBomLines(List<XBomLine> bomLines) {
		this.bomLines = bomLines;
	}

	public String getMaterialName() {
		return materialName;
	}

	public void setMaterialName(String materialName) {
		this.materialName = materialName;
	}

	public Long getMaterialVersion() {
		return materialVersion;
	}

	public void setMaterialVersion(Long materialVersion) {
		this.materialVersion = materialVersion;
	}

	public String getUomId() {
		return uomId;
	}

	public void setUomId(String uomId) {
		this.uomId = uomId;
	}

	public String getBomUse() {
		return bomUse;
	}

	public void setBomUse(String bomUse) {
		this.bomUse = bomUse;
	}

	public Date getValidFrom() {
		return validFrom;
	}

	public void setValidFrom(Date validFrom) {
		this.validFrom = validFrom;
	}

	public Date getValidTo() {
		return validTo;
	}

	public void setValidTo(Date validTo) {
		this.validTo = validTo;
	}

	public String getReserved1() {
		return reserved1;
	}

	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}

	public String getReserved2() {
		return reserved2;
	}

	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}

	public String getReserved3() {
		return reserved3;
	}

	public void setReserved3(String reserved3) {
		this.reserved3 = reserved3;
	}

	public String getReserved4() {
		return reserved4;
	}

	public void setReserved4(String reserved4) {
		this.reserved4 = reserved4;
	}

	public String getReserved5() {
		return reserved5;
	}

	public void setReserved5(String reserved5) {
		this.reserved5 = reserved5;
	}

	public String getReserved6() {
		return reserved6;
	}

	public void setReserved6(String reserved6) {
		this.reserved6 = reserved6;
	}

	public String getReserved7() {
		return reserved7;
	}

	public void setReserved7(String reserved7) {
		this.reserved7 = reserved7;
	}

	public String getReserved8() {
		return reserved8;
	}

	public void setReserved8(String reserved8) {
		this.reserved8 = reserved8;
	}

	public String getReserved9() {
		return reserved9;
	}

	public void setReserved9(String reserved9) {
		this.reserved9 = reserved9;
	}

	public String getReserved10() {
		return reserved10;
	}

	public void setReserved10(String reserved10) {
		this.reserved10 = reserved10;
	}
	
	public String getName() {
		return name;
	}
	
	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Long getVersion() {
		return version;
	}
	
	public void setVersion(Long version) {
		this.version = version;
	}
	
	public String getStatus() {
		return status;
	}
	
	public void setStatus(String status) {
		this.status = status;
	}

	public Date getActiveTime() {
		return activeTime;
	}
	
	public void setActiveTime(Date activeTime) {
		this.activeTime = activeTime;
	}

	public String getActiveUser() {
		return activeUser;
	}

	public void setActiveUser(String activeUser) {
		this.activeUser = activeUser;
	}

	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

}
