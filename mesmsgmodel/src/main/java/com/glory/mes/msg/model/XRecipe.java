package com.glory.mes.msg.model;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "RECIPE")
@XmlAccessorType(XmlAccessType.NONE)
public class XRecipe extends XBase {

	private static final long serialVersionUID = 1L;

	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name="PPID")
	private String ppId; //线体设备的Recipe
	
	@XmlElement(name="RECIPENAME")
	private String recipeName;
	
	@XmlElement(name="RECIPEDESC")
	private String recipeDesc;
	
	@XmlElement(name="RECIPETYPE")
	private String recipeType;
		
	@XmlElement(name="HOLDSTATE")
	private String holdState;

    @XmlElement(name="EQUIPMENTID")
    private String equipmentId; //线体设备ID

    @XmlElement(name="UNITID")
    private String unitId;
    
    @XmlElement(name="CHECKSUM")
    private String checkSum;
    
    @XmlElement(name="ISCOMPARE")
    private String isCompare;
    
    @XmlElement(name="RECIPECATEGROY")
	private String recipeCategory;
    
    @XmlElementWrapper(name="PARAMALIST")
    @XmlElementRef
    private List<XRecipeEquipmentParameter> paramaList;
    
	public String getRecipeName() {
		return recipeName;
	}

	public void setRecipeName(String recipeName) {
		this.recipeName = recipeName;
	}

	public String getRecipeDesc() {
		return recipeDesc;
	}

	public void setRecipeDesc(String recipeDesc) {
		this.recipeDesc = recipeDesc;
	}

	public String getRecipeType() {
		return recipeType;
	}

	public void setRecipeType(String recipeType) {
		this.recipeType = recipeType;
	}

	public String getHoldState() {
		return holdState;
	}

	public void setHoldState(String holdState) {
		this.holdState = holdState;
	}

	public String getEquipmentId() {
		return equipmentId;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}

	public String getCheckSum() {
		return checkSum;
	}

	public void setCheckSum(String checkSum) {
		this.checkSum = checkSum;
	}

	public List<XRecipeEquipmentParameter> getParamaList() {
		return paramaList;
	}

	public void setParamaList(List<XRecipeEquipmentParameter> paramaList) {
		this.paramaList = paramaList;
	}

	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	public String getIsCompare() {
		return isCompare;
	}

	public void setIsCompare(String isCompare) {
		this.isCompare = isCompare;
	}

	public String getPpId() {
		return ppId;
	}

	public void setPpId(String ppId) {
		this.ppId = ppId;
	}
	
	public String getRecipeCategory() {
		return recipeCategory;
	}

	public void setRecipeCategory(String recipeCategory) {
		this.recipeCategory = recipeCategory;
	}
}
