package com.glory.mes.msg.model.rtd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name="LOT")
@XmlAccessorType(XmlAccessType.NONE)
public class XRtdLot extends XBase {

	private static final long serialVersionUID = -8727487778179151021L;
	
	@XmlElement(name = "LOTRRN")
    private String lotRrn;
    
    @XmlElement(name = "LOTID")
    private String lotId;
    
    @XmlElement(name = "BATCHID")
    private String batchId;
    
    @XmlElement(name = "LOTLOCK")
    private String lotLock;
    
    @XmlElement(name = "LOTTYPE")
    private String lotType;
    
    @XmlElement(name = "MANUFACTURETYPE")
    private String manufactureType;
    
    @XmlElement(name = "DURABLE")
    private String durable;
    
    @XmlElement(name = "CSTTYPE")
    private String cstType;
    
    @XmlElement(name = "WAREHOUSEID")
    private String warehouseId;
    
    @XmlElement(name = "ZONEID")
    private String zoneId;
    
    @XmlElement(name = "LOCATORID")
    private String locatorId;
    
    @XmlElement(name = "EQUIPMENTID")
    private String equipmentId;
    
    @XmlElement(name = "RESERVEDEQUIPMENTID")
    private String reservedEquipmentId;
    
    @XmlElement(name = "PORT")
    private String port;
    
    @XmlElement(name = "PRIORITY")
    private String priority;
    
    @XmlElement(name = "QUEUETIME")
    private String queueTime;
    
    @XmlElement(name = "PARTNAME")
    private String partName;
    
    @XmlElement(name = "RECIPENAME")
    private String recipeName;
    
    @XmlElement(name = "STEPRRN")
    private String stepRrn;
    
    @XmlElement(name = "STEPNAME")
    private String stepName;
    
    @XmlElement(name = "SHEETQTYPE")
    private String sheetQType;
    
    @XmlElement(name = "TRACKOUTTIME")
    private String trackOutTime;
    
    @XmlElement(name = "EXPIRETIME")
    private String expireTime;
    
    @XmlElement(name = "PAIRDURABLEID")
    private String pairDurableId;
    
    @XmlElement(name = "LINKEQPID")
    private String linkEqpID;
    
    @XmlElement(name = "CLEANSTATE")
    private String cleanState;
    
    @XmlElement(name = "DISPATCHSTATE")
    private String dispatchState;
    
    @XmlElement(name = "OWNER")
    private String owner;
    
    @XmlElement(name = "FLOOR")
    private String floor;
    
    @XmlElement(name = "WOID")
    private String woId;
    
    @XmlElement(name = "SUBMATTYPE")
    private String subMatType;
    
    @XmlElement(name = "TARGETTRANSFERSTATE")
    private String targetTransferState;
    
    @XmlElement(name = "MAINQTY")
    private String mainQty;
    
    @XmlElement(name = "RESERVED1")
    private String reserved1;
    
    @XmlElement(name = "RESERVED2")
    private String reserved2;
    
    @XmlElement(name = "RESERVED3")
    private String reserved3;
    
    @XmlElement(name = "RESERVED4")
    private String reserved4;
    
    @XmlElement(name = "RESERVED5")
    private String reserved5;
    
    @XmlElement(name = "RESERVED6")
    private String reserved6;
    
    @XmlElement(name = "RESERVED7")
    private String reserved7;
    
    @XmlElement(name = "RESERVED8")
    private String reserved8;

	public String getLotRrn() {
		return lotRrn;
	}

	public void setLotRrn(String lotRrn) {
		this.lotRrn = lotRrn;
	}

	public String getLotId() {
		return lotId;
	}

	public void setLotId(String lotId) {
		this.lotId = lotId;
	}

	public String getBatchId() {
		return batchId;
	}

	public void setBatchId(String batchId) {
		this.batchId = batchId;
	}

	public String getLotLock() {
		return lotLock;
	}

	public void setLotLock(String lotLock) {
		this.lotLock = lotLock;
	}

	public String getLotType() {
		return lotType;
	}

	public void setLotType(String lotType) {
		this.lotType = lotType;
	}

	public String getManufactureType() {
		return manufactureType;
	}

	public void setManufactureType(String manufactureType) {
		this.manufactureType = manufactureType;
	}

	public String getDurable() {
		return durable;
	}

	public void setDurable(String durable) {
		this.durable = durable;
	}

	public String getCstType() {
		return cstType;
	}

	public void setCstType(String cstType) {
		this.cstType = cstType;
	}

	public String getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(String warehouseId) {
		this.warehouseId = warehouseId;
	}

	public String getZoneId() {
		return zoneId;
	}

	public void setZoneId(String zoneId) {
		this.zoneId = zoneId;
	}

	public String getLocatorId() {
		return locatorId;
	}

	public void setLocatorId(String locatorId) {
		this.locatorId = locatorId;
	}

	public String getEquipmentId() {
		return equipmentId;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	public String getReservedEquipmentId() {
		return reservedEquipmentId;
	}

	public void setReservedEquipmentId(String reservedEquipmentId) {
		this.reservedEquipmentId = reservedEquipmentId;
	}

	public String getPort() {
		return port;
	}

	public void setPort(String port) {
		this.port = port;
	}

	public String getPriority() {
		return priority;
	}

	public void setPriority(String priority) {
		this.priority = priority;
	}

	public String getQueueTime() {
		return queueTime;
	}

	public void setQueueTime(String queueTime) {
		this.queueTime = queueTime;
	}

	public String getPartName() {
		return partName;
	}

	public void setPartName(String partName) {
		this.partName = partName;
	}

	public String getRecipeName() {
		return recipeName;
	}

	public void setRecipeName(String recipeName) {
		this.recipeName = recipeName;
	}

	public String getStepRrn() {
		return stepRrn;
	}

	public void setStepRrn(String stepRrn) {
		this.stepRrn = stepRrn;
	}

	public String getStepName() {
		return stepName;
	}

	public void setStepName(String stepName) {
		this.stepName = stepName;
	}

	public String getSheetQType() {
		return sheetQType;
	}

	public void setSheetQType(String sheetQType) {
		this.sheetQType = sheetQType;
	}

	public String getTrackOutTime() {
		return trackOutTime;
	}

	public void setTrackOutTime(String trackOutTime) {
		this.trackOutTime = trackOutTime;
	}

	public String getExpireTime() {
		return expireTime;
	}

	public void setExpireTime(String expireTime) {
		this.expireTime = expireTime;
	}

	public String getPairDurableId() {
		return pairDurableId;
	}

	public void setPairDurableId(String pairDurableId) {
		this.pairDurableId = pairDurableId;
	}

	public String getLinkEqpID() {
		return linkEqpID;
	}

	public void setLinkEqpID(String linkEqpID) {
		this.linkEqpID = linkEqpID;
	}

	public String getCleanState() {
		return cleanState;
	}

	public void setCleanState(String cleanState) {
		this.cleanState = cleanState;
	}

	public String getDispatchState() {
		return dispatchState;
	}

	public void setDispatchState(String dispatchState) {
		this.dispatchState = dispatchState;
	}

	public String getOwner() {
		return owner;
	}

	public void setOwner(String owner) {
		this.owner = owner;
	}

	public String getFloor() {
		return floor;
	}

	public void setFloor(String floor) {
		this.floor = floor;
	}

	public String getWoId() {
		return woId;
	}

	public void setWoId(String woId) {
		this.woId = woId;
	}

	public String getSubMatType() {
		return subMatType;
	}

	public void setSubMatType(String subMatType) {
		this.subMatType = subMatType;
	}

	public String getTargetTransferState() {
		return targetTransferState;
	}

	public void setTargetTransferState(String targetTransferState) {
		this.targetTransferState = targetTransferState;
	}

	public String getMainQty() {
		return mainQty;
	}

	public void setMainQty(String mainQty) {
		this.mainQty = mainQty;
	}

	public String getReserved1() {
		return reserved1;
	}

	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}

	public String getReserved2() {
		return reserved2;
	}

	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}

	public String getReserved3() {
		return reserved3;
	}

	public void setReserved3(String reserved3) {
		this.reserved3 = reserved3;
	}

	public String getReserved4() {
		return reserved4;
	}

	public void setReserved4(String reserved4) {
		this.reserved4 = reserved4;
	}

	public String getReserved5() {
		return reserved5;
	}

	public void setReserved5(String reserved5) {
		this.reserved5 = reserved5;
	}

	public String getReserved6() {
		return reserved6;
	}

	public void setReserved6(String reserved6) {
		this.reserved6 = reserved6;
	}

	public String getReserved7() {
		return reserved7;
	}

	public void setReserved7(String reserved7) {
		this.reserved7 = reserved7;
	}

	public String getReserved8() {
		return reserved8;
	}

	public void setReserved8(String reserved8) {
		this.reserved8 = reserved8;
	}
    
}
