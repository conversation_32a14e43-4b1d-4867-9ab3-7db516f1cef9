package com.glory.mes.msg.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldDateAdapter;
import com.glory.framework.core.xml.XDynamicComponent;

@XmlRootElement(name = "MLOTHIS")
@XmlAccessorType(XmlAccessType.NONE)
public class XMLotHis extends XDynamicComponent {

	private static final long serialVersionUID = 1L;
	
	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;

	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="CREATED")
	protected Date created;
	
	@XmlElement(name="HISSEQ")
	private String hisSeq;
	
	@XmlElement(name="HISSEQNO")
	private Long hisSeqNo;

	@XmlElement(name="TRANSTYPE")
	private String transType;
	
	@XmlElement(name="MLOTRRN")
	private Long mLotRrn;
	
	@XmlElement(name="MLOTID")
	private String mLotId;
	
	@XmlElement(name="LOTTYPE")
	private String mLotType;
	
	@XmlElement(name="BATCHTYPE")
	private String batchType;
	
	@XmlElement(name="STATE")
	private String state;
	
	@XmlElement(name="HOLDSTATE")
	private String holdState;
	
	@XmlElement(name="WOID")
	private String woId;
	
	@XmlElement(name="MAINQTY")
	private BigDecimal mainQty;
	
	@XmlElement(name="SUBQTY")
	private BigDecimal subQty;
	
	@XmlElement(name="GRADE1")
	private String grade1;
	
	@XmlElement(name="GRADE2")
	private String grade2;
	
	@XmlElement(name="LINEID")
	private String lineId;
	
	@XmlElement(name="TEAMID")
	private String teamId;
		
	@XmlElement(name="MATERIALRRN")
	private Long materialRrn;
	
	@XmlElement(name="MATERIALNAME")
	private String materialName;

	@XmlElement(name="MATERIALVERSION")
	private Long materialVersion;
	
	@XmlElement(name="MATERIALDESC")
	private String materialDesc;
	
	@XmlElement(name="MATERIALTYPE")
	private String materialType;

	@XmlElement(name="UOMID")
	private String uomId;
	
	@XmlElement(name="PARTNERCODE")
	private String partnerCode; 
	
	@XmlElement(name="PARTNERORDER")
	private String partnerOrder;
	
	@XmlElement(name="PARTNERMATERIALID")
	private String partnerMaterialId;
	
	@XmlElement(name="PARTNERLOTID")
	private String partnerLotId;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="DATECODE")
	private Date dateCode;
	
	@XmlElement(name="ISTIMESENSITIVE")
	private String isTimeSensitive;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="SHELFLIFEEXPIRE")
	private Date shelfLifeExpire;	

	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="FLOORLIFEEXPIRE")
	private Date floorLifeExpire;
	
	@XmlElement(name="CURRENTCOUNT")
	private Long currentCount;
	
	@XmlElement(name="LIMITLIFE")
	private Long limitLife;
	
	@XmlElement(name="OWNER")
	private String owner;
	
	@XmlElement(name="LOTCOMMENT")
	private String lotComment;

	@XmlElement(name="TRANSMAINQTY")
	private BigDecimal transMainQty;
	
	@XmlElement(name="TRANSSUBQTY")
	private BigDecimal transSubQty;
	
	@XmlElement(name="TRANSWAREHOUSEID")
	private String transWarehouseId;
	
	@XmlElement(name="TRANSSTORAGETYPE")
	private String transStorageType;

	@XmlElement(name="TRANSSTORAGEID")
	private String transStorageId;
	
	@XmlElement(name="TRANSTARGETWAREHOUSEID")
	private String transTargetWarehouseId;
	
	@XmlElement(name="TRANSTARGETSTORAGETYPE")
	private String transTargetStorageType;

	@XmlElement(name="TRANSTARGETSTORAGEID")
	private String transTargetStorageId;
	
	@XmlElement(name="REFDOCTYPE")
	private String refDocType;
		
	@XmlElement(name="REFDOCID")
	private String refDocId;
	
	@XmlElement(name="ACTIONCODE")
	private String actionCode;
	
	@XmlElement(name="ACTIONREASON")
	private String actionReason;
	
	@XmlElement(name="ACTIONCOMMENT")
	private String actionComment;
	
	@XmlElement(name="OPERATOR")
	private String operator;
	
	@XmlElement(name="HISCOMMENT")
	private String hisComment;
	
	@XmlElement(name="EQUIPMENTID")
	private String equipmentId;
	
	@XmlElement(name="POSITIONNAME")
	private String positionName;
	
	@XmlElement(name="RESERVED1")
	private String reserved1;

	@XmlElement(name="RESERVED2")
	private String reserved2;

	@XmlElement(name="RESERVED3")
	private String reserved3;

	@XmlElement(name="RESERVED4")
	private String reserved4;

	@XmlElement(name="RESERVED5")
	private String reserved5;
	
	@XmlElement(name="RESERVED6")
	private String reserved6;
	
	@XmlElement(name="RESERVED7")
	private String reserved7;
	
	@XmlElement(name="RESERVED8")
	private String reserved8;
	
	@XmlElement(name="UDF")
    private Map udf;
    
	public Map getUdf() {
		return udf;
	}

	public void setUdf(Map udf) {
		this.udf = udf;
	}

	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	public String getHisSeq() {
		return hisSeq;
	}

	public void setHisSeq(String hisSeq) {
		this.hisSeq = hisSeq;
	}

	public Long getHisSeqNo() {
		return hisSeqNo;
	}

	public void setHisSeqNo(Long hisSeqNo) {
		this.hisSeqNo = hisSeqNo;
	}

	public String getTransType() {
		return transType;
	}

	public void setTransType(String transType) {
		this.transType = transType;
	}

	public Long getmLotRrn() {
		return mLotRrn;
	}

	public void setmLotRrn(Long mLotRrn) {
		this.mLotRrn = mLotRrn;
	}

	public String getmLotId() {
		return mLotId;
	}

	public void setmLotId(String mLotId) {
		this.mLotId = mLotId;
	}

	public String getmLotType() {
		return mLotType;
	}

	public void setmLotType(String mLotType) {
		this.mLotType = mLotType;
	}

	public String getBatchType() {
		return batchType;
	}

	public void setBatchType(String batchType) {
		this.batchType = batchType;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getHoldState() {
		return holdState;
	}

	public void setHoldState(String holdState) {
		this.holdState = holdState;
	}

	public String getWoId() {
		return woId;
	}

	public void setWoId(String woId) {
		this.woId = woId;
	}

	public BigDecimal getMainQty() {
		return mainQty;
	}

	public void setMainQty(BigDecimal mainQty) {
		this.mainQty = mainQty;
	}

	public BigDecimal getSubQty() {
		return subQty;
	}

	public void setSubQty(BigDecimal subQty) {
		this.subQty = subQty;
	}

	public String getGrade1() {
		return grade1;
	}

	public void setGrade1(String grade1) {
		this.grade1 = grade1;
	}

	public String getGrade2() {
		return grade2;
	}

	public void setGrade2(String grade2) {
		this.grade2 = grade2;
	}

	public String getLineId() {
		return lineId;
	}

	public void setLineId(String lineId) {
		this.lineId = lineId;
	}

	public String getTeamId() {
		return teamId;
	}

	public void setTeamId(String teamId) {
		this.teamId = teamId;
	}

	public Long getMaterialRrn() {
		return materialRrn;
	}

	public void setMaterialRrn(Long materialRrn) {
		this.materialRrn = materialRrn;
	}

	public String getMaterialName() {
		return materialName;
	}

	public void setMaterialName(String materialName) {
		this.materialName = materialName;
	}

	public Long getMaterialVersion() {
		return materialVersion;
	}

	public void setMaterialVersion(Long materialVersion) {
		this.materialVersion = materialVersion;
	}

	public String getMaterialDesc() {
		return materialDesc;
	}

	public void setMaterialDesc(String materialDesc) {
		this.materialDesc = materialDesc;
	}

	public String getMaterialType() {
		return materialType;
	}

	public void setMaterialType(String materialType) {
		this.materialType = materialType;
	}

	public String getUomId() {
		return uomId;
	}

	public void setUomId(String uomId) {
		this.uomId = uomId;
	}

	public String getPartnerCode() {
		return partnerCode;
	}

	public void setPartnerCode(String partnerCode) {
		this.partnerCode = partnerCode;
	}

	public String getPartnerOrder() {
		return partnerOrder;
	}

	public void setPartnerOrder(String partnerOrder) {
		this.partnerOrder = partnerOrder;
	}

	public String getPartnerMaterialId() {
		return partnerMaterialId;
	}

	public void setPartnerMaterialId(String partnerMaterialId) {
		this.partnerMaterialId = partnerMaterialId;
	}

	public String getPartnerLotId() {
		return partnerLotId;
	}

	public void setPartnerLotId(String partnerLotId) {
		this.partnerLotId = partnerLotId;
	}

	public Date getDateCode() {
		return dateCode;
	}

	public void setDateCode(Date dateCode) {
		this.dateCode = dateCode;
	}

	public Date getShelfLifeExpire() {
		return shelfLifeExpire;
	}

	public void setShelfLifeExpire(Date shelfLifeExpire) {
		this.shelfLifeExpire = shelfLifeExpire;
	}

	public Date getFloorLifeExpire() {
		return floorLifeExpire;
	}

	public void setFloorLifeExpire(Date floorLifeExpire) {
		this.floorLifeExpire = floorLifeExpire;
	}

	public Long getCurrentCount() {
		return currentCount;
	}

	public void setCurrentCount(Long currentCount) {
		this.currentCount = currentCount;
	}

	public Long getLimitLife() {
		return limitLife;
	}

	public void setLimitLife(Long limitLife) {
		this.limitLife = limitLife;
	}

	public String getOwner() {
		return owner;
	}

	public void setOwner(String owner) {
		this.owner = owner;
	}

	public String getLotComment() {
		return lotComment;
	}

	public void setLotComment(String lotComment) {
		this.lotComment = lotComment;
	}

	public BigDecimal getTransMainQty() {
		return transMainQty;
	}

	public void setTransMainQty(BigDecimal transMainQty) {
		this.transMainQty = transMainQty;
	}

	public BigDecimal getTransSubQty() {
		return transSubQty;
	}

	public void setTransSubQty(BigDecimal transSubQty) {
		this.transSubQty = transSubQty;
	}

	public String getTransWarehouseId() {
		return transWarehouseId;
	}

	public void setTransWarehouseId(String transWarehouseId) {
		this.transWarehouseId = transWarehouseId;
	}

	public String getTransStorageType() {
		return transStorageType;
	}

	public void setTransStorageType(String transStorageType) {
		this.transStorageType = transStorageType;
	}

	public String getTransStorageId() {
		return transStorageId;
	}

	public void setTransStorageId(String transStorageId) {
		this.transStorageId = transStorageId;
	}

	public String getTransTargetWarehouseId() {
		return transTargetWarehouseId;
	}

	public void setTransTargetWarehouseId(String transTargetWarehouseId) {
		this.transTargetWarehouseId = transTargetWarehouseId;
	}

	public String getTransTargetStorageType() {
		return transTargetStorageType;
	}

	public void setTransTargetStorageType(String transTargetStorageType) {
		this.transTargetStorageType = transTargetStorageType;
	}

	public String getTransTargetStorageId() {
		return transTargetStorageId;
	}

	public void setTransTargetStorageId(String transTargetStorageId) {
		this.transTargetStorageId = transTargetStorageId;
	}

	public String getRefDocType() {
		return refDocType;
	}

	public void setRefDocType(String refDocType) {
		this.refDocType = refDocType;
	}

	public String getRefDocId() {
		return refDocId;
	}

	public void setRefDocId(String refDocId) {
		this.refDocId = refDocId;
	}

	public String getActionCode() {
		return actionCode;
	}

	public void setActionCode(String actionCode) {
		this.actionCode = actionCode;
	}

	public String getActionReason() {
		return actionReason;
	}

	public void setActionReason(String actionReason) {
		this.actionReason = actionReason;
	}

	public String getActionComment() {
		return actionComment;
	}

	public void setActionComment(String actionComment) {
		this.actionComment = actionComment;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getHisComment() {
		return hisComment;
	}

	public void setHisComment(String hisComment) {
		this.hisComment = hisComment;
	}

	public String getEquipmentId() {
		return equipmentId;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	public String getPositionName() {
		return positionName;
	}

	public void setPositionName(String positionName) {
		this.positionName = positionName;
	}
	
	public Boolean getIsTimeSensitive() {
		return "Y".equalsIgnoreCase(this.isTimeSensitive) ? true : false; 
	}

	public void setIsTimeSensitive(Boolean isTimeSensitive) {
		this.isTimeSensitive = isTimeSensitive ? "Y" : "N";
	}

	public String getReserved1() {
		return reserved1;
	}

	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}

	public String getReserved2() {
		return reserved2;
	}

	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}

	public String getReserved3() {
		return reserved3;
	}

	public void setReserved3(String reserved3) {
		this.reserved3 = reserved3;
	}

	public String getReserved4() {
		return reserved4;
	}

	public void setReserved4(String reserved4) {
		this.reserved4 = reserved4;
	}

	public String getReserved5() {
		return reserved5;
	}

	public void setReserved5(String reserved5) {
		this.reserved5 = reserved5;
	}

	public String getReserved6() {
		return reserved6;
	}

	public void setReserved6(String reserved6) {
		this.reserved6 = reserved6;
	}

	public String getReserved7() {
		return reserved7;
	}

	public void setReserved7(String reserved7) {
		this.reserved7 = reserved7;
	}

	public String getReserved8() {
		return reserved8;
	}

	public void setReserved8(String reserved8) {
		this.reserved8 = reserved8;
	}
}
