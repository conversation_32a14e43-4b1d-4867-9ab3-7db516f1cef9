package com.glory.mes.msg.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldDateAdapter;
import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "BOMLINE")
@XmlAccessorType(XmlAccessType.NONE)
public class XBomLine extends XBase {
	
	private static final long serialVersionUID = -5822057622938759643L;

	@XmlElement(name = "OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name="ORGRRN")
	protected Long orgRrn = 0L;
	
	@XmlElement(name="ISACTIVE")
	protected String isActive = "Y";
	
	@XmlElement(name = "BOMRRN")
	private Long bomRrn;

	/**
	 * BOMLine在BOM中的顺序
	 */
	@XmlElement(name="SEQNO")
	private Long seqNo;
	
	/**
	 * ITEM的类别
	 */
	@XmlElement(name="ITEMCATEGORY")
	private String itemCategory;
	
	/**
	 * BOMLine所在的Step名称
	 */
	@XmlElement(name="STEPNAME")
	private String stepName;
	
	/**
	 * BOMLine所在的Step版本
	 */
	@XmlElement(name="STEPVERSION")
	private Long stepVersion;
	
	/**
	 * 对应物料的ObjectRrn
	 */
	@XmlElement(name="MATERIALRRN")
	private Long materialRrn;
	
	/**
	 * 对应物料的名称
	 */
	@XmlElement(name="MATERIALNAME")
	private String materialName;
	
	@XmlElement(name="MATERIALVERSION")
	private Long materialVersion;
	
	/**
	 * 对应物料的名称
	 */
	@XmlElement(name="MATERIALDESC")
	private String materialDesc;

	/**
	 * 对应物料的类型
	 */
	@XmlElement(name="MATERIALTYPE")
	private String materialType;
	
	/**
	 * 对应物料的单位用量
	 */
	@XmlElement(name="UNITQTY")
	private BigDecimal unitQty;
	
	/**
	 * 按照百分批方式定义物料与batchQty配合使用
	 */
	@XmlElement(name="ISQTYPERCENT")
	private String isQtyPercent = "N";
	
	/**
	 * 对应物料的单位用量
	 */
	@XmlElement(name="BATCHQTY")
	private BigDecimal batchQty;
	
	/**
	 * 对应物料的单位
	 */
	@XmlElement(name="UOMID")
	private String uomId;
	
	/**
	 * 对应物料的固定损耗率
	 */
	@XmlElement(name="FIXEDQTY")
	private BigDecimal fixedQty;
	
	/**
	 * 对应物料的损耗率
	 * 实际所需数量 = fixedQty + 数量 * unitQty * (1 + lossRate)
	 */
	@XmlElement(name="LOSSRATE")
	private BigDecimal lossRate;
	
	/**
	 * BOMLine生效时间(未使用)
	 */
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="VALIDFROM")
	private Date validFrom;
	
	/**
	 * BOMLine失效时间(未使用)
	 */
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="VALIDTO")
	private Date validTo;
	
	/**
	 * 主物料
	 * 主物料在投料的时候使用
	 * 在投料时就需要选择对应的主物料
	 */
	@XmlElement(name="ISMAIN")
	private String isMain;
	
	/**
	 * 关键物料
	 * 关键物料在批次过站的时候进行确认
	 * 一般在进站时检查物料是否正确
	 * 出站时记录对应的使用数量
	 * 需要和特殊的TrackIn/Out配合使用
	 */
	@XmlElement(name="ISCRITICAL")
	private String isCritical;
	
	/**
	 * 键合物料
	 * 需要和特殊的键合TrackIn/Out配合使用
	 */
	@XmlElement(name="ISASSEMBLY")
	private String isAssembly;
	
	/**
	 * 是否需要生产
	 * 如果生产则展开下级BOM，否则将产品作为原材料
	 */
	@XmlElement(name="ISPRODUCTION")
	private String isProduction = "N";
	/**
	 * 可选料,该物料在实际使用过程中可使用也可不使用
	 */
	@XmlElement(name="ISOPTIONAL")
	private String isOptional;
	
	/**
	 * 倒扣料方式
	 */
	@XmlElement(name="FLUSHTYPE")
	private String flushType;
	
	/**
	 * 可替代料,该物料在实际使用过程中可以被其它物料所替代
	 */
	@XmlElement(name="ISALTERNATE")
	private String isAlternate;
	
	/**
	 * 替代策略:全部替代或部分替代
	 * 目前仅使用全部替代
	 */
	@XmlElement(name="ALTERNATESTRATEGY")
	private String alternateStrategy;
	
	/**
	 * 替代组
	 * 只有同一组类的物料才能相互替代
	 */
	@XmlElement(name="ALTERNATEGROUP")
	private String alternateGroup;
	
	/**
	 * 替代优先级
	 */
	@XmlElement(name="ALTERNATEPRIORITY")
	private BigDecimal alternatePriority;
	
	/**
	 * 替代百分百
	 * 只在部分替代时使用(目前未使用)
	 */
	@XmlElement(name="ALTERNATEPERCENT")
	private BigDecimal alternatePercent;
	
	/**
	 * 用于Co-Product时成本分摊
	 */
	@XmlElement(name="COSTALLOCATIONPERCENT")
	private BigDecimal costAllocationPercent;
	
	@XmlElement(name="ISSELECTED")
	private Boolean isSelected;
	
	@XmlElement(name="POSITIONNAME")
	private String positionName;
	
	@XmlElement(name="COMMENTS")
	private String comments;
	
	@XmlElement(name="RESERVED1")
	private String reserved1;
	
	@XmlElement(name="RESERVED2")
	private String reserved2;

	@XmlElement(name="RESERVED3")
	private String reserved3;

	@XmlElement(name="RESERVED4")
	private String reserved4;

	@XmlElement(name="RESERVED5")
	private String reserved5;

	@XmlElement(name="RESERVED6")
	private String reserved6;

	@XmlElement(name="RESERVED7")
	private String reserved7;

	@XmlElement(name="RESERVED8")
	private String reserved8;

	@XmlElement(name="RESERVED9")
	private String reserved9;

	@XmlElement(name="RESERVED10")
	private String reserved10;	
	
	@XmlElement(name="ATTRIBUTE1")
	private Object attribute1;
	
	@XmlElement(name="ATTRIBUTE2")
	private Object attribute2;

	@XmlElement(name="ATTRIBUTE3")
	private Object attribute3;

	@XmlElement(name="ATTRIBUTE4")
	private Object attribute4;

	@XmlElement(name="ATTRIBUTE5")
	private Object attribute5;
	
	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}
	
	public Long getOrgRrn() {
		return orgRrn;
	}
	
	public void setOrgRrn(Long orgRrn) {
		if (orgRrn == null){
			this.orgRrn = 0L;
		} else {
			this.orgRrn = orgRrn;
		}
	} 
	
	public Boolean getIsActive(){
		return "Y".equalsIgnoreCase(this.isActive) ? true : false; 
	}

	public void setIsActive(Boolean isActive) {
		this.isActive = isActive ? "Y" : "N";
	}
	
	public Long getBomRrn() {
		return bomRrn;
	}

	public void setBomRrn(Long bomRrn) {
		this.bomRrn = bomRrn;
	}

	public Long getSeqNo() {
		return seqNo;
	}

	public void setSeqNo(Long seqNo) {
		this.seqNo = seqNo;
	}

	public String getItemCategory() {
		return itemCategory;
	}

	public void setItemCategory(String itemCategory) {
		this.itemCategory = itemCategory;
	}

	public String getStepName() {
		return stepName;
	}

	public void setStepName(String stepName) {
		this.stepName = stepName;
	}

	public Long getStepVersion() {
		return stepVersion;
	}

	public void setStepVersion(Long stepVersion) {
		this.stepVersion = stepVersion;
	}

	public Long getMaterialRrn() {
		return materialRrn;
	}

	public void setMaterialRrn(Long materialRrn) {
		this.materialRrn = materialRrn;
	}

	public String getMaterialName() {
		return materialName;
	}

	public void setMaterialName(String materialName) {
		this.materialName = materialName;
	}

	public Long getMaterialVersion() {
		return materialVersion;
	}

	public void setMaterialVersion(Long materialVersion) {
		this.materialVersion = materialVersion;
	}

	public String getMaterialDesc() {
		return materialDesc;
	}

	public void setMaterialDesc(String materialDesc) {
		this.materialDesc = materialDesc;
	}

	public String getMaterialType() {
		return materialType;
	}

	public void setMaterialType(String materialType) {
		this.materialType = materialType;
	}

	public BigDecimal getUnitQty() {
		return unitQty;
	}

	public void setUnitQty(BigDecimal unitQty) {
		this.unitQty = unitQty;
	}

	public Boolean getIsQtyPercent() {
		return "Y".equalsIgnoreCase(this.isQtyPercent) ? true : false;
	}

	public void setIsQtyPercent(Boolean isQtyPercent) {
		this.isQtyPercent = isQtyPercent ? "Y" : "N";
	}

	public BigDecimal getBatchQty() {
		return batchQty;
	}

	public void setBatchQty(BigDecimal batchQty) {
		this.batchQty = batchQty;
	}

	public String getUomId() {
		return uomId;
	}

	public void setUomId(String uomId) {
		this.uomId = uomId;
	}

	public BigDecimal getFixedQty() {
		return fixedQty;
	}

	public void setFixedQty(BigDecimal fixedQty) {
		this.fixedQty = fixedQty;
	}

	public BigDecimal getLossRate() {
		return lossRate;
	}

	public void setLossRate(BigDecimal lossRate) {
		this.lossRate = lossRate;
	}

	public Date getValidFrom() {
		return validFrom;
	}

	public void setValidFrom(Date validFrom) {
		this.validFrom = validFrom;
	}

	public Date getValidTo() {
		return validTo;
	}

	public void setValidTo(Date validTo) {
		this.validTo = validTo;
	}

	public Boolean getIsMain() {
		return "Y".equalsIgnoreCase(this.isMain) ? true : false;
	}

	public void setIsMain(Boolean isMain) {
		this.isMain = isMain ? "Y" : "N";
	}

	public Boolean getIsCritical() {
		return "Y".equalsIgnoreCase(this.isCritical) ? true : false;
	}

	public void setIsCritical(Boolean isCritical) {
		this.isCritical = isCritical ? "Y" : "N";
	}

	public Boolean getIsAssembly() {
		return "Y".equalsIgnoreCase(this.isAssembly) ? true : false;
	}

	public void setIsAssembly(Boolean isAssembly) {
		this.isAssembly = isAssembly ? "Y" : "N";
	}

	public Boolean getIsOptional() {
		return "Y".equalsIgnoreCase(this.isOptional) ? true : false;
	}

	public void setIsOptional(Boolean isOptional) {
		this.isOptional = isOptional ? "Y" : "N";
	}

	public String getFlushType() {
		return flushType;
	}

	public void setFlushType(String flushType) {
		this.flushType = flushType;
	}

	public Boolean getIsAlternate() {
		return "Y".equalsIgnoreCase(this.isAlternate) ? true : false;
	}

	public void setIsAlternate(Boolean isAlternate) {
		this.isAlternate = isAlternate ? "Y" : "N";
	}

	public String getAlternateStrategy() {
		return alternateStrategy;
	}

	public void setAlternateStrategy(String alternateStrategy) {
		this.alternateStrategy = alternateStrategy;
	}

	public String getAlternateGroup() {
		return alternateGroup;
	}

	public void setAlternateGroup(String alternateGroup) {
		this.alternateGroup = alternateGroup;
	}

	public BigDecimal getAlternatePriority() {
		return alternatePriority;
	}

	public void setAlternatePriority(BigDecimal alternatePriority) {
		this.alternatePriority = alternatePriority;
	}

	public BigDecimal getAlternatePercent() {
		return alternatePercent;
	}

	public void setAlternatePercent(BigDecimal alternatePercent) {
		this.alternatePercent = alternatePercent;
	}

	public BigDecimal getCostAllocationPercent() {
		return costAllocationPercent;
	}

	public void setCostAllocationPercent(BigDecimal costAllocationPercent) {
		this.costAllocationPercent = costAllocationPercent;
	}

	public String getPositionName() {
		return positionName;
	}

	public void setPositionName(String positionName) {
		this.positionName = positionName;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getReserved1() {
		return reserved1;
	}

	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}

	public String getReserved2() {
		return reserved2;
	}

	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}

	public String getReserved3() {
		return reserved3;
	}

	public void setReserved3(String reserved3) {
		this.reserved3 = reserved3;
	}

	public String getReserved4() {
		return reserved4;
	}

	public void setReserved4(String reserved4) {
		this.reserved4 = reserved4;
	}

	public String getReserved5() {
		return reserved5;
	}

	public void setReserved5(String reserved5) {
		this.reserved5 = reserved5;
	}

	public String getReserved6() {
		return reserved6;
	}

	public void setReserved6(String reserved6) {
		this.reserved6 = reserved6;
	}

	public String getReserved7() {
		return reserved7;
	}

	public void setReserved7(String reserved7) {
		this.reserved7 = reserved7;
	}

	public String getReserved8() {
		return reserved8;
	}

	public void setReserved8(String reserved8) {
		this.reserved8 = reserved8;
	}

	public String getReserved9() {
		return reserved9;
	}

	public void setReserved9(String reserved9) {
		this.reserved9 = reserved9;
	}

	public String getReserved10() {
		return reserved10;
	}

	public void setReserved10(String reserved10) {
		this.reserved10 = reserved10;
	}

	public Boolean getIsProduction(){
		return "Y".equalsIgnoreCase(this.isProduction) ? true : false; 
	}

	public void setIsProduction(Boolean isProduction) {
		this.isProduction = isProduction ? "Y" : "N";
	}

	public Boolean getIsSelected() {
		return isSelected;
	}

	public void setIsSelected(Boolean isSelected) {
		this.isSelected = isSelected;
	}

	public Object getAttribute1() {
		return attribute1;
	}

	public void setAttribute1(Object attribute1) {
		this.attribute1 = attribute1;
	}

	public Object getAttribute2() {
		return attribute2;
	}

	public void setAttribute2(Object attribute2) {
		this.attribute2 = attribute2;
	}

	public Object getAttribute3() {
		return attribute3;
	}

	public void setAttribute3(Object attribute3) {
		this.attribute3 = attribute3;
	}

	public Object getAttribute4() {
		return attribute4;
	}

	public void setAttribute4(Object attribute4) {
		this.attribute4 = attribute4;
	}

	public Object getAttribute5() {
		return attribute5;
	}

	public void setAttribute5(Object attribute5) {
		this.attribute5 = attribute5;
	}
	
}
