package com.glory.mes.msg.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

import java.util.List;

@XmlRootElement(name = "NODE")
@XmlAccessorType(XmlAccessType.NONE)
public class XNode extends XBase{
	
	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name="NODETYPE")
	private String nodeType;

	@XmlElement(name="NAME")
	private String name;
	
	@XmlElement(name="VERSION")
	private Long version;
	
	@XmlElement(name="DESCRIPTION")
	protected String description;

	@XmlElement(name="PATH")
	private String path;
	
	@XmlElement(name="SEQNO")
	private Integer seqNo;
	
	@XmlElement(name="SUB_PROCESS_NAME")
	private String subProcessName = null;

	@XmlElement(name="SUB_PROCESS_VERSION")
	private Long subProcessVersion = null;

	@XmlElement(name="SUB_PROCESS_DESCRIPTION")
	private String subProcessDescription = null;

	@XmlElement(name="HAS_REWORK")
	private Boolean hasRework;

	@XmlElement(name="REWORK_NAME")
	private String reworkName;

	@XmlElement(name="REWORK_RRN")
	private Long reworkRrn;

	@XmlElement(name="REWORK_VERSION")
	private Long reworkVersion;

	@XmlElement(name="REWORK_PATH")
	private String reworkPath;

	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public Integer getSeqNo() {
		return seqNo;
	}

	public void setSeqNo(Integer seqNo) {
		this.seqNo = seqNo;
	}

	public String getNodeType() {
		return nodeType;
	}

	public void setNodeType(String nodeType) {
		this.nodeType = nodeType;
	}
	
	public String getSubProcessName() {
		return subProcessName;
	}

	public void setSubProcessName(String subProcessName) {
		this.subProcessName = subProcessName;
	}

	public Long getSubProcessVersion() {
		return subProcessVersion;
	}

	public void setSubProcessVersion(Long subProcessVersion) {
		this.subProcessVersion = subProcessVersion;
	}
	
	public String getSubProcessDescription() {
		return subProcessDescription;
	}

	public void setSubProcessDescription(String subProcessDescription) {
		this.subProcessDescription = subProcessDescription;
	}

	public Boolean getHasRework() {
		return hasRework;
	}

	public void setHasRework(Boolean hasRework) {
		this.hasRework = hasRework;
	}

	public String getReworkName() {
		return reworkName;
	}

	public void setReworkName(String reworkName) {
		this.reworkName = reworkName;
	}

	public Long getReworkRrn() {
		return reworkRrn;
	}

	public void setReworkRrn(Long reworkRrn) {
		this.reworkRrn = reworkRrn;
	}

	public Long getReworkVersion() {
		return reworkVersion;
	}

	public void setReworkVersion(Long reworkVersion) {
		this.reworkVersion = reworkVersion;
	}

	public String getReworkPath() {
		return reworkPath;
	}

	public void setReworkPath(String reworkPath) {
		this.reworkPath = reworkPath;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}
}
