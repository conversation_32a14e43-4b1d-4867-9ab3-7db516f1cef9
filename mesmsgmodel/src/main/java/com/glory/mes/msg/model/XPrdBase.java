package com.glory.mes.msg.model;

import java.util.List;

import javax.persistence.Transient;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;

import com.glory.framework.core.xml.XDynamicComponent;

public abstract class XPrdBase extends XDynamicComponent {

	private static final long serialVersionUID = 1L;
	/**
	 * SA:保存并且激活
	 * A:激活
	 * C:比较是否相同,如果不相同则报错
	 */
	public static final String ACTION_TYPE_SAVEACTIVE = "SA";
	public static final String ACTION_TYPE_ACTIVE = "A";
	public static final String ACTION_TYPE_COMPARE = "C";

	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name="ORGRRN")
	private Long orgRrn;
	
	@XmlElement(name="NAME")
	private String name;
	
	@XmlElement(name="DESCRIPTION")
	private String description;
	
	@XmlElement(name="STATUS")
	private String status;
	
	@XmlElement(name="VERSION")
	private Long version;

	@XmlElementWrapper(name="WFPARAMETERLIST")
	@XmlElementRef
	private List<XWFParameter> wfParameterList;
	/**
	 * 是否定义为要导入
	 * 及在对应的sheet中存在导入记录
	 */
	@Transient
	private boolean isExistUpload = true;
	
	/**
	 * 是否必须使用激活版本
	 * 及当流程中stateState为空时,表示必须使用激活版本
	 * 在流程初始化时必须判断
	 */
	@Transient
	private boolean isUseActive = false;

	@Transient
	private String actionType;
	
	@Transient
	protected String parameterString;
	
	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	public Long getOrgRrn() {
		return orgRrn;
	}

	public void setOrgRrn(Long orgRrn) {
		this.orgRrn = orgRrn;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}
	
	public String getId() {
		if (version == null) {
			return name;
		} else {
			return name + "." + version;
		}
	}

	public boolean isExistUpload() {
		return isExistUpload;
	}

	public void setExistUpload(boolean isExistUpload) {
		this.isExistUpload = isExistUpload;
	}

	public boolean isUseActive() {
		return isUseActive;
	}

	public void setUseActive(boolean isUseActive) {
		this.isUseActive = isUseActive;
	}
	
	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}

	public List<XWFParameter> getWfParameterList() {
		return wfParameterList;
	}

	public void setWfParameterList(List<XWFParameter> wfParameterList) {
		this.wfParameterList = wfParameterList;
	}
	
	public String getParameterString() {
		return parameterString;
	}

	public void setParameterString(String parameterString) {
		this.parameterString = parameterString;
	}

}
