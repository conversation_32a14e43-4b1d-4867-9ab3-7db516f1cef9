package com.glory.mes.msg.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "COMSMSTATE")
@XmlAccessorType(XmlAccessType.NONE)
public class XState extends XBase{

	@XmlElement(name = "OBJECTTYPE")
	private String objectType;
	@XmlElement(name = "COMCLASS")
	private String comClass;
	@XmlElement(name = "STATE")
	private String state;
	@XmlElement(name = "DESCRIPTION")
	private String description;
	@XmlElement(name = "SEQNO")
	private Integer seqNo;
	@XmlElement(name = "ISAVAILABLE")
	private String isAvailable;
	@XmlElement(name = "ISPARALL<PERSON>")
	private String isParallel;

	@XmlElement(name = "OBJECTRRN")
	protected Long objectRrn;

	public Long getObjectRrn() {
		return this.objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}
	
	public String getObjectType() {
		return this.objectType;
	}

	public void setObjectType(String objectType) {
		this.objectType = objectType;
	}

	public String getComClass() {
		return this.comClass;
	}

	public void setComClass(String comClass) {
		this.comClass = comClass;
	}

	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getDescription() {
		return this.description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getSeqNo() {
		return this.seqNo;
	}

	public void setSeqNo(Integer seqNo) {
		this.seqNo = seqNo;
	}

	public void setIsAvailable(Boolean isAvailable) {
		this.isAvailable = isAvailable ? "Y" : "N";
	}

	public Boolean getIsAvailable() {
		return "Y".equalsIgnoreCase(this.isAvailable);
	}

	public void setIsParallel(Boolean isParallel) {
		this.isParallel = isParallel ? "Y" : "N";
	}

	public Boolean getIsParallel() {
		return "Y".equalsIgnoreCase(this.isParallel);
	}
}
