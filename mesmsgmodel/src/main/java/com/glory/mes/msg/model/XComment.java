package com.glory.mes.msg.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "COMMENT")
@XmlAccessorType(XmlAccessType.NONE)
public class XComment extends XBase {
	
	@XmlElement(name="SEQNO")
	private String seqNo;
	
	@XmlElement(name="COMMENTCODE")
	private String commentCode;

	@XmlElement(name="COMMENTS")
	private String comments;

	public String getSeqNo() {
		return seqNo;
	}

	public void setSeqNo(String seqNo) {
		this.seqNo = seqNo;
	}

	public String getCommentCode() {
		return commentCode;
	}

	public void setCommentCode(String commentCode) {
		this.commentCode = commentCode;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}
	
}
