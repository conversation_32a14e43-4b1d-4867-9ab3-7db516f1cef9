package com.glory.mes.msg.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldDateAdapter;
import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "SCHEDULE")
@XmlAccessorType(XmlAccessType.NONE)
public class XSchedule extends XBase {
	
	public static final String STATUS_WAIT= "WAIT";
	public static final String STATUS_RUN = "RUN";
	public static final String STATUS_FINISH = "FINISH";
	
	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name="DOCID")
	private String docId;

	@XmlElement(name="DOCTYPE")
	private String docType;

	@XmlElement(name="DOCSTATUS")
	private String docStatus;

	@XmlElement(name="OWNER")
	private String owner;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="SCHEDULEDATE")
	private Date scheduleDate;//计划日期
	
	@XmlElement(name="MOID")
	private String moId;//工单号
	
	@XmlElement(name="SHIFT")
	private String shift;//班别
	
	@XmlElement(name="CUSTOMERCODE")
	private String customerCode;//客户代码
	
	@XmlElement(name="PARTNAME")
	private String partName;//产品名称
	
	@XmlElement(name="PARTVERSION")
	private Long partVersion;//产品版本
	
	@XmlElement(name="LINEID")
	private String lineId; //线别
	
	@XmlElement(name="EQUIPMENTID")
	private String equipmentId;//设备
	
	@XmlElement(name="STEPNAME")
	private String stepName;//工步名称
	
	@XmlElement(name="MAINQTY")
	protected BigDecimal mainQty;//主数量
	
	@XmlElement(name="SUBQTY")
	protected BigDecimal subQty;//子数量
	
	@XmlElement(name="FINISHMAINQTY")
	private BigDecimal finishMainQty;//完成主数量
	
	@XmlElement(name="FINISHSUBQTY")
	private BigDecimal finishSubQty;//完成子数量
	
	@XmlElement(name="COMMENTS")
	private String comments;//备注
	
	@XmlElement(name="LOTTYPE")
	private String lotType;
	
	@XmlElement(name="SHEETQTYPE")
	private String sheetQType;

	/**
   	 * 描述：计划周期对象key，映射为：OBJECTRRN；
   	 * @return objectRrn
   	 */
	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	/**
   	 * 描述：订单ID，映射为：DOCID；
   	 * @return docId
   	 */
	public String getDocId() {
		return docId;
	}

	public void setDocId(String docId) {
		this.docId = docId;
	}

	/**
   	 * 描述：订单类型，映射为：DOCTYPE；
   	 * @return docType
   	 */
	public String getDocType() {
		return docType;
	}

	public void setDocType(String docType) {
		this.docType = docType;
	}

	/**
   	 * 描述：订单状态，映射为：DOCSTATUS；
   	 * @return docStatus
   	 */
	public String getDocStatus() {
		return docStatus;
	}

	public void setDocStatus(String docStatus) {
		this.docStatus = docStatus;
	}

	/**
   	 * 描述：所属人，映射为：OWNER；
   	 * @return owner
   	 */
	public String getOwner() {
		return owner;
	}

	public void setOwner(String owner) {
		this.owner = owner;
	}

	/**
   	 * 描述：计划日期，映射为：SCHEDULEDATE；
   	 * @return scheduleDate
   	 */
	public Date getScheduleDate() {
		return scheduleDate;
	}

	public void setScheduleDate(Date scheduleDate) {
		this.scheduleDate = scheduleDate;
	}

	/**
   	 * 描述：工单号，映射为：MOID；
   	 * @return moId
   	 */
	public String getMoId() {
		return moId;
	}

	public void setMoId(String moId) {
		this.moId = moId;
	}

	/**
   	 * 描述：班组，映射为：SHIFT；
   	 * @return shift
   	 */
	public String getShift() {
		return shift;
	}

	public void setShift(String shift) {
		this.shift = shift;
	}

	/**
   	 * 描述：客户代码，映射为：CUSTOMERCODE；
   	 * @return customerCode
   	 */
	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	/**
   	 * 描述：产品名称，映射为：PARTNAME；
   	 * @return partName
   	 */
	public String getPartName() {
		return partName;
	}

	public void setPartName(String partName) {
		this.partName = partName;
	}

	/**
   	 * 描述：产品版本，映射为：PARTVERSION；
   	 * @return partVersion
   	 */
	public Long getPartVersion() {
		return partVersion;
	}

	public void setPartVersion(Long partVersion) {
		this.partVersion = partVersion;
	}

	/**
   	 * 描述：设备ID，映射为：EQUIPMENTID；
   	 * @return equipmentId
   	 */
	public String getEquipmentId() {
		return equipmentId;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	/**
   	 * 描述：线别，映射为：LINEID；
   	 * @return lineId
   	 */
	public String getLineId() {
		return lineId;
	}

	public void setLineId(String lineId) {
		this.lineId = lineId;
	}

	/**
   	 * 描述：工步名称，映射为：STEPNAME；
   	 * @return stepName
   	 */
	public String getStepName() {
		return stepName;
	}

	public void setStepName(String stepName) {
		this.stepName = stepName;
	}

	/**
   	 * 描述：主数量，映射为：MAINQTY；
   	 * @return mainQty
   	 */
	public BigDecimal getMainQty() {
		return mainQty;
	}

	public void setMainQty(BigDecimal mainQty) {
		this.mainQty = mainQty;
	}

	/**
   	 * 描述：子数量，映射为：SUBQTY；
   	 * @return subQty
   	 */
	public BigDecimal getSubQty() {
		return subQty;
	}

	public void setSubQty(BigDecimal subQty) {
		this.subQty = subQty;
	}

	/**
   	 * 描述：完成主数量，映射为：FINISHMAINQTY；
   	 * @return finishMainQty
   	 */
	public BigDecimal getFinishMainQty() {
		return finishMainQty;
	}

	public void setFinishMainQty(BigDecimal finishMainQty) {
		this.finishMainQty = finishMainQty;
	}

	/**
   	 * 描述：完成子数量，映射为：FINISHSUBQTY；
   	 * @return finishSubQty
   	 */
	public BigDecimal getFinishSubQty() {
		return finishSubQty;
	}

	public void setFinishSubQty(BigDecimal finishSubQty) {
		this.finishSubQty = finishSubQty;
	}

	/**
   	 * 描述：备注信息，映射为：COMMENTS；
   	 * @return comments
   	 */
	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	/**
   	 * 描述：批次类型，映射为：LOTTYPE；
   	 * @return lotType
   	 */
	public String getLotType() {
		return lotType;
	}

	public void setLotType(String lotType) {
		this.lotType = lotType;
	}

	/**
   	 * 描述：，映射为：SHEETQTYPE；
   	 * @return sheetQType
   	 */
	public String getSheetQType() {
		return sheetQType;
	}

	public void setSheetQType(String sheetQType) {
		this.sheetQType = sheetQType;
	}
	
}
