package com.glory.mes.msg.model;

import java.math.BigDecimal;

import javax.persistence.Transient;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XDynamicComponent;

@XmlRootElement(name = "COMPONENT")
@XmlAccessorType(XmlAccessType.NONE)
public class XQtyUnit extends XDynamicComponent {
	
	private static final long serialVersionUID = 1L;

	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name="ORGRRN")
	private Long orgRrn;
	
	@XmlElement(name="ORGNAME")
	private String orgName;
	
	@XmlElement(name="MAINQTY")
	private BigDecimal mainQty;
	
	@XmlElement(name="SUBQTY")
	private BigDecimal subQty;
	
	@XmlElement(name="STATE")
	private String state;
	
	@XmlElement(name="EQUIPMENTID")
	private String equipmentId;
    
    @XmlElement(name="OPERATOR1")
	private String operator1;

    @XmlElement(name="OPERATOR2")
	private String operator2;
    
    @XmlElement(name="PARENTUNITRRN")
    private Long parentUnitRrn;

    @XmlElement(name="ACTIONCODE")
	private String actionCode;

	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	public Long getOrgRrn() {
		return orgRrn;
	}

	public void setOrgRrn(Long orgRrn) {
		this.orgRrn = orgRrn;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public BigDecimal getMainQty() {
		return mainQty;
	}

	public void setMainQty(BigDecimal mainQty) {
		this.mainQty = mainQty;
	}

	public BigDecimal getSubQty() {
		return subQty;
	}

	public void setSubQty(BigDecimal subQty) {
		this.subQty = subQty;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getEquipmentId() {
		return equipmentId;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	public String getOperator1() {
		return operator1;
	}

	public void setOperator1(String operator1) {
		this.operator1 = operator1;
	}

	public String getOperator2() {
		return operator2;
	}

	public void setOperator2(String operator2) {
		this.operator2 = operator2;
	}

	public Long getParentUnitRrn() {
		return parentUnitRrn;
	}

	public void setParentUnitRrn(Long parentUnitRrn) {
		this.parentUnitRrn = parentUnitRrn;
	}
	
	public String getActionCode() {
		return actionCode;
	}

	public void setActionCode(String actionCode) {
		this.actionCode = actionCode;
	}
}
