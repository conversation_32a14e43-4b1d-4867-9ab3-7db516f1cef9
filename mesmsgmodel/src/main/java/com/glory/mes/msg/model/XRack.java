package com.glory.mes.msg.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "RACK")
@XmlAccessorType(XmlAccessType.NONE)
public class XRack extends XBase{

	private static final long serialVersionUID = 1L;
	
	/**
	 * casstteId
	 */
	@XmlElement(name="CASSETTEID")
	private String cassetteId;
	
	/**
	 * lotId
	 */
	@XmlElement(name="LOTID")
	private String lotId;
	
	/**
	 * 货架编号
	 */
	@XmlElement(name="RACKID")
	private String rackId;
	
	/**
	 * 入库时间
	 */
	@XmlElement(name="INDATE")
	private String inDate;
	
	/**
	 * Y/N是否可以出库
	 */
	@XmlElement(name="SHIPFLAG")
	private String shipFlag;
	
	@XmlElement(name="COMMENT")
	private String comment;
	
	@XmlElement(name="SPARE")
	private String spare;

	public String getCassetteId() {
		return cassetteId;
	}

	public void setCassetteId(String cassetteId) {
		this.cassetteId = cassetteId;
	}

	public String getLotId() {
		return lotId;
	}

	public void setLotId(String lotId) {
		this.lotId = lotId;
	}

	public String getRackId() {
		return rackId;
	}

	public void setRackId(String rackId) {
		this.rackId = rackId;
	}

	public String getInDate() {
		return inDate;
	}

	public void setInDate(String inDate) {
		this.inDate = inDate;
	}

	public String getShipFlag() {
		return shipFlag;
	}
	
	public boolean isDisable() {
		return "N".equals(this.shipFlag) ? true : false;
	}

	public void setShipFlag(String shipFlag) {
		this.shipFlag = shipFlag;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public String getSpare() {
		return spare;
	}

	public void setSpare(String spare) {
		this.spare = spare;
	}
	
}
