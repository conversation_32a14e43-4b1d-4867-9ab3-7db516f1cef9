package com.glory.mes.msg.model;

import com.glory.framework.core.xml.FieldDateAdapter;
import com.glory.framework.core.xml.XDynamicComponent;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.Date;

@XmlRootElement(name = "TOOL")
@XmlAccessorType(XmlAccessType.NONE)
public class XTool extends XDynamicComponent {

	private static final long serialVersionUID = 1L;

	@XmlElement(name = "TOOLID")
    private String toolId;

    @XmlElement(name="TOOLTYPE")
	private String toolType;
    
    @XmlElement(name="MAINQTY")
	private BigDecimal mainQty = BigDecimal.ZERO;
	
	@XmlElement(name="SUBQTY")
	private BigDecimal subQty;
	
	@XmlElement(name="STATE")
	private String state;
	
	@XmlElement(name="HOLDSTATE")
	private String holdState;
	
	@XmlElement(name="MATERIALRRN")
	private Long materialRrn;
	
	@XmlElement(name="MATERIALNAME")
	private String materialName;
	
	@XmlElement(name="MATERIALVERSION")
	private Long materialVersion;
	
	@XmlElement(name="MATERIALDESC")
	private String materialDesc;
	
	@XmlElement(name="MATERIALTYPE")
	private String materialType;
	
	@XmlElement(name="PARTNERCODE")
	private String partnerCode;
	
	@XmlElement(name="PARTNERORDER")
	private String partnerOrder;
	
	@XmlElement(name="PARTNERMATERIALID")
	private String partnerMaterialId;
	
	@XmlElement(name="PARTNERLOTID")
	private String partnerLotId;
	
	@XmlElement(name="GRADE1")
    private String grade1;
    
    @XmlElement(name="GRADE2")
    private String grade2;
    
    @XmlElement(name="ROOTTOOLID")
    private String rootToolId;
    
    @XmlElement(name="TRANSWAREHOUSERRN")
	private Long transWarehouseRrn;
	
	@XmlElement(name="TRANSWAREHOUSEID")
	private String transWarehouseId;
	
	@XmlElement(name="EQUIPMENTID")
	private String equipmentId;
	 
	@XmlElement(name="POSITION")
	private String position;
	
	@XmlElement(name="TRANSMAINQTY")
	private BigDecimal transMainQty;
	
	@XmlElement(name="TRANSSUBQTY")
	private BigDecimal transSubQty;
	
    @XmlElement(name="USECOUNT")
    private Long useCount;
    
    @XmlElement(name="CURRENTCOUNT")
    private Long currentCount;
    
    @XmlElement(name="LIMITCOUNT")
    private Long limitCount;

    @XmlElement(name="DURABLEID")
    private String durableId;
    
    @XmlElement(name="LOTID")
    private String lotId;
    
    @XmlElement(name="COMPONENTID")
    private String componentId;

    @XmlElement(name="MLOTID")
	private String mLotId;
	
	@XmlElement(name="MLOTTYPE")
	private String mLotType;
	
	@XmlElement(name="CLEANSTATE")
	private String cleanState;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="CLEANDATE")
	private Date cleanDate;
	
	@XmlElement(name="LIMITLIFE")
	private Long limitLife;
	
	/**
	 * 保质期失效时间
	 */
    @XmlJavaTypeAdapter(FieldDateAdapter.class)
    @XmlElement(name="SHELFLIFEEXPIRE")
	private Date shelfLifeExpire;

    /**
     * 使用有效期失效时间
     */
    @XmlJavaTypeAdapter(FieldDateAdapter.class)
    @XmlElement(name="FLOORLIFEEXPIRE")
    private Date floorLifeExpire;
	
    /**
	 * 描述：物料批号，映射为：MLOTID；
	 * @return mLotId
	 */
	public String getmLotId() {
		return mLotId;
	}

	public void setmLotId(String mLotId) {
		this.mLotId = mLotId;
	}

	/**
	 * 描述：物料批类型，映射为：MLOTTYPE；
	 * @return mLotType
	 */
	public String getmLotType() {
		return mLotType;
	}

	public void setmLotType(String mLotType) {
		this.mLotType = mLotType;
	}

	/**
	 * 描述：治具ID，映射为：TOOLID；
	 * @return toolId
	 */
	public String getToolId() {
		return toolId;
	}

	public void setToolId(String toolId) {
		this.toolId = toolId;
	}

	/**
	 * 描述：治具类型，映射为：TOOLTYPE；
	 * @return toolType
	 */
	public String getToolType() {
		return toolType;
	}

	public void setToolType(String toolType) {
		this.toolType = toolType;
	}

	/**
	 * 描述：主数量，映射为：MAINQTY；
	 * @return mainQty
	 */
	public BigDecimal getMainQty() {
		return mainQty;
	}

	public void setMainQty(BigDecimal mainQty) {
		this.mainQty = mainQty;
	}

	/**
	 * 描述：子数量，映射为：SUBQTY；
	 * @return subQty
	 */
	public BigDecimal getSubQty() {
		return subQty;
	}

	public void setSubQty(BigDecimal subQty) {
		this.subQty = subQty;
	}

	/**
	 * 描述：状态，映射为：STATE；
	 * @return state
	 */
	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	/**
	 * 描述：暂停状态，映射为：HOLDSTATE；
	 * @return holdState
	 */
	public String getHoldState() {
		return holdState;
	}

	public void setHoldState(String holdState) {
		this.holdState = holdState;
	}

	/**
	 * 描述：物料key，映射为：MATERIALRRN；
	 * @return materialRrn
	 */
	public Long getMaterialRrn() {
		return materialRrn;
	}

	public void setMaterialRrn(Long materialRrn) {
		this.materialRrn = materialRrn;
	}

	/**
	 * 描述：物料名称，映射为：MATERIALNAME；
	 * @return materialName
	 */
	public String getMaterialName() {
		return materialName;
	}

	public void setMaterialName(String materialName) {
		this.materialName = materialName;
	}

	/**
	 * 描述：物料版本，映射为：MATERIALVERSION；
	 * @return materialVersion
	 */
	public Long getMaterialVersion() {
		return materialVersion;
	}

	public void setMaterialVersion(Long materialVersion) {
		this.materialVersion = materialVersion;
	}

	/**
	 * 描述：物料描述，映射为：MATERIALDESC；
	 * @return materialDesc
	 */
	public String getMaterialDesc() {
		return materialDesc;
	}

	public void setMaterialDesc(String materialDesc) {
		this.materialDesc = materialDesc;
	}

	/**
	 * 描述：物料类型，映射为：MATERIALTYPE；
	 * @return materialType
	 */
	public String getMaterialType() {
		return materialType;
	}

	public void setMaterialType(String materialType) {
		this.materialType = materialType;
	}

	/**
	 * 描述：客户代码，映射为：PARTNERCODE；
	 * @return partnerCode
	 */
	public String getPartnerCode() {
		return partnerCode;
	}

	public void setPartnerCode(String partnerCode) {
		this.partnerCode = partnerCode;
	}

	/**
	 * 描述：客户工单，映射为：PARTNERORDER；
	 * @return partnerOrder
	 */
	public String getPartnerOrder() {
		return partnerOrder;
	}

	public void setPartnerOrder(String partnerOrder) {
		this.partnerOrder = partnerOrder;
	}

	/**
	 * 描述：客户物料ID，映射为：PARTNERMATERIALID；
	 * @return partnerMaterialId
	 */
	public String getPartnerMaterialId() {
		return partnerMaterialId;
	}

	public void setPartnerMaterialId(String partnerMaterialId) {
		this.partnerMaterialId = partnerMaterialId;
	}

	/**
	 * 描述：客户批次ID，映射为：PARTNERLOTID；
	 * @return partnerLotId
	 */
	public String getPartnerLotId() {
		return partnerLotId;
	}

	public void setPartnerLotId(String partnerLotId) {
		this.partnerLotId = partnerLotId;
	}

	/**
	 * 描述：规格1，映射为：GRADE1；
	 * @return grade1
	 */
	public String getGrade1() {
		return grade1;
	}

	public void setGrade1(String grade1) {
		this.grade1 = grade1;
	}

	/**
	 * 描述：规格2，映射为：GRADE2；
	 * @return grade2
	 */
	public String getGrade2() {
		return grade2;
	}

	public void setGrade2(String grade2) {
		this.grade2 = grade2;
	}

	/**
	 * 描述：源治具ID，映射为：ROOTTOOLID；
	 * @return rootToolId
	 */
	public String getRootToolId() {
		return rootToolId;
	}

	public void setRootToolId(String rootToolId) {
		this.rootToolId = rootToolId;
	}

	/**
	 * 描述：事物仓库key，映射为：TRANSWAREHOUSERRN；
	 * @return transWarehouseRrn
	 */
	public Long getTransWarehouseRrn() {
		return transWarehouseRrn;
	}

	public void setTransWarehouseRrn(Long transWarehouseRrn) {
		this.transWarehouseRrn = transWarehouseRrn;
	}

	/**
	 * 描述：事物仓库ID，映射为：TRANSWAREHOUSEID；
	 * @return transWarehouseId
	 */
	public String getTransWarehouseId() {
		return transWarehouseId;
	}

	public void setTransWarehouseId(String transWarehouseId) {
		this.transWarehouseId = transWarehouseId;
	}

	/**
	 * 描述：设备ID，映射为：EQUIPMENTID；
	 * @return equipmentId
	 */
	public String getEquipmentId() {
		return equipmentId;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	/**
	 * 描述：位置，映射为：POSITION；
	 * @return position
	 */
	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	/**
	 * 描述：事物处理数量，映射为：TRANSMAINQTY；
	 * @return transMainQty
	 */
	public BigDecimal getTransMainQty() {
		return transMainQty;
	}

	public void setTransMainQty(BigDecimal transMainQty) {
		this.transMainQty = transMainQty;
	}

	/**
	 * 描述：事物处理子数量，映射为：TRANSSUBQTY；
	 * @return transSubQty
	 */
	public BigDecimal getTransSubQty() {
		return transSubQty;
	}

	public void setTransSubQty(BigDecimal transSubQty) {
		this.transSubQty = transSubQty;
	}

	/**
	 * 描述：使用次数，映射为：USECOUNT；
	 * @return useCount
	 */
	public Long getUseCount() {
		return useCount;
	}

	public void setUseCount(Long useCount) {
		this.useCount = useCount;
	}

	/**
	 * 描述：当前次数，映射为：CURRENTCOUNT；
	 * @return currentCount
	 */
	public Long getCurrentCount() {
		return currentCount;
	}

	public void setCurrentCount(Long currentCount) {
		this.currentCount = currentCount;
	}

	/**
	 * 描述：最小次数，映射为：LIMITCOUNT；
	 * @return limitCount
	 */
	public Long getLimitCount() {
		return limitCount;
	}

	public void setLimitCount(Long limitCount) {
		this.limitCount = limitCount;
	}

	/**
	 * 描述：治具ID，映射为：DURABLEID；
	 * @return durableId
	 */
	public String getDurableId() {
		return durableId;
	}

	public void setDurableId(String durableId) {
		this.durableId = durableId;
	}

	/**
	 * 描述：批次ID，映射为：LOTID；
	 * @return lotId
	 */
	public String getLotId() {
		return lotId;
	}

	public void setLotId(String lotId) {
		this.lotId = lotId;
	}

	/**
	 * 描述：组件ID，映射为：COMPONENTID；
	 * @return componentId
	 */
	public String getComponentId() {
		return componentId;
	}

	public void setComponentId(String componentId) {
		this.componentId = componentId;
	}

	/**
	 * 描述：清洗状态，映射为：CLEANSTATE；
	 * @return cleanState
	 */
	public String getCleanState() {
		return cleanState;
	}

	public void setCleanState(String cleanState) {
		this.cleanState = cleanState;
	}

	/**
	 * 描述：清洗日期，映射为：CLEANDATE；
	 * @return cleanDate
	 */

	public Date getCleanDate() {
		return cleanDate;
	}

	public void setCleanDate(Date cleanDate) {
		this.cleanDate = cleanDate;
	}
	
	/**
	 * 描述：生命周期，映射为：LIMITLIFE；
	 * @return limitLife
	 */
	public Long getLimitLife() {
		return limitLife;
	}


	public void setLimitLife(Long limitLife) {
		this.limitLife = limitLife;
	}

	/**
	 * 描述：到期日，映射为：SHELFLIFEEXPIRE；
	 * @return shelfLifeExpire
	 */
	public Date getShelfLifeExpire() {
		return shelfLifeExpire;
	}

	public void setShelfLifeExpire(Date shelfLifeExpire) {
		this.shelfLifeExpire = shelfLifeExpire;
	}

	/**
	 * 描述：周期日，映射为：FLOORLIFEEXPIRE；
	 * @return floorLifeExpire
	 */
    public Date getFloorLifeExpire() {
        return floorLifeExpire;
    }

    public void setFloorLifeExpire(Date floorLifeExpire) {
        this.floorLifeExpire = floorLifeExpire;
    }
}
