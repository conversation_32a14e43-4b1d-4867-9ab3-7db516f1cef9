package com.glory.mes.msg.model;

import java.util.List;

import javax.persistence.Column;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name="EDCBINSET")
@XmlAccessorType(XmlAccessType.NONE)
public class XEdcBinSet extends XBase {
	
	private static final long serialVersionUID = 1L;

	@XmlElement(name="NAME")
	private String name;
	
	@XmlElement(name="DESCRIPTION")
	private String description;
	
	@XmlElement(name="STATUS")
	private String status;
	
	@XmlElement(name="VERSION")
	private Long version;

	@XmlElement(name="OWNER")
	private String owner;
	
	@XmlElement(name="ISREPEATABLE")
	private String isRepeatable;
	
	@XmlElement(name="ISCHECKTOTAL")
	private String isCheckTotal;
	
	@XmlElement(name="ISBYCOMPONENT")
	private String isByComponent;
	
	@XmlElement(name="ISMULTIINPUT")
	private String isMultiInput;
	
	@XmlElement(name="ISSHOWREMARK")
	private String isShwoRemark;
	
	@XmlElement(name="CATEGORY1")
	private String category1;
	
	@XmlElement(name="CATEGORY2")
	private String category2;
	
	@XmlElement(name="CATEGORY3")
	private String category3;
	
	@XmlElement(name="CATEGORY4")
	private String category4;
	
	@XmlElement(name="COMMENTS")
	private String comments;

	@XmlElement(name="EDCTYPE")
	private String edcType;

	@XmlElement(name="CAPABILITY")
	private Long capability;
	
	
	@Column(name="USL")
	@XmlElement String uslString;
	
	@Column(name="SL")
	@XmlElement String slString;
	
	@Column(name="LSL")
	@XmlElement String lslString;
	
	@Column(name="ISHOLDLOT")
	@XmlElement String isHoldLot;
	
	@Column(name="ISHOLDEQP")
	@XmlElement String isHoldEqp;
	
	@Column(name="HOLDCODE")
	@XmlElement String holdCode;
	
	@Column(name="HOLDREASON")
	@XmlElement String holdReason;
	
	@Column(name = "HOLDOWNER")
	@XmlElement String holdOwner;
	
	@Column(name="ISREWORK")
	@XmlElement String isRework;
	
	@Column(name="REWORKCODE")
	@XmlElement String reworkCode;
	
	@Column(name="REWORKPROCEDURENAME")
	@XmlElement String reworkProcedureName;

	@Column(name="REWORKPROCEDUREVERSION")
	@XmlElement Long reworkProcedureVersion;
	
	@XmlElementWrapper(name = "BINSETLINES")
	@XmlElementRef
	private List<XEdcBinSetLine> binSetLines;
	
	
	public String getOwner() {
		return owner;
	}

	public void setOwner(String owner) {
		this.owner = owner;
	}
	
	public void setIsCheckTotal(Boolean isCheckTotal) {
		this.isCheckTotal = isCheckTotal ? "Y" : "N";
	}

	public Boolean getIsCheckTotal() {
		return "Y".equalsIgnoreCase(this.isCheckTotal) ? true : false;
	}
	
	public Boolean getIsRepeatable() {
		return "Y".equalsIgnoreCase(this.isRepeatable) ? true : false;
	}
	
	public void setIsRepeatable(Boolean isRepeatable) {
		this.isRepeatable = isRepeatable ? "Y" : "N";
	}
	
	public void setIsByComponent(Boolean isByComponent) {
		this.isByComponent = isByComponent ? "Y" : "N";
	}

	public Boolean getIsByComponent() {
		return "Y".equalsIgnoreCase(this.isByComponent) ? true : false;
	}

	public void setIsMultiInput(Boolean isMultiInput) {
		this.isMultiInput = isMultiInput ? "Y" : "N";
	}

	public Boolean getIsMultiInput() {
		return "Y".equalsIgnoreCase(this.isMultiInput) ? true : false;
	}
	
	public void setIsShowRemark(Boolean isShwoRemark) {
		this.isShwoRemark = isShwoRemark ? "Y" : "N";
	}

	public Boolean getIsShowRemark() {
		return "Y".equalsIgnoreCase(this.isShwoRemark) ? true : false;
	}
	
	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getEdcType() {
		return edcType;
	}

	public void setEdcType(String edcType) {
		this.edcType = edcType;
	}

	public String getCategory1() {
		return category1;
	}

	public void setCategory1(String category1) {
		this.category1 = category1;
	}

	public String getCategory2() {
		return category2;
	}

	public void setCategory2(String category2) {
		this.category2 = category2;
	}

	public String getCategory3() {
		return category3;
	}

	public void setCategory3(String category3) {
		this.category3 = category3;
	}

	public String getCategory4() {
		return category4;
	}

	public void setCategory4(String category4) {
		this.category4 = category4;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public Long getCapability() {
		return capability;
	}

	public void setCapability(Long capability) {
		this.capability = capability;
	}
	
	public void setIsHoldLot(Boolean isHoldLot) {
		this.isHoldLot = isHoldLot ? "Y" : "N";
	}

	public Boolean getIsHoldLot() {
		return "Y".equalsIgnoreCase(this.isHoldLot) ? true : false;
	}
	
	public void setIsHoldEqp(Boolean isHoldEqp) {
		this.isHoldEqp = isHoldEqp ? "Y" : "N";
	}

	public Boolean getIsHoldEqp() {
		return "Y".equalsIgnoreCase(this.isHoldEqp) ? true : false;
	}
	
	public void setIsRework(Boolean isRework) {
		this.isRework = isRework ? "Y" : "N";
	}

	public Boolean getIsRework() {
		return "Y".equalsIgnoreCase(this.isRework) ? true : false;
	}

	public String getUslString() {
		return uslString;
	}

	public void setUslString(String uslString) {
		this.uslString = uslString;
	}

	public String getSlString() {
		return slString;
	}

	public void setSlString(String slString) {
		this.slString = slString;
	}

	public String getLslString() {
		return lslString;
	}

	public void setLslString(String lslString) {
		this.lslString = lslString;
	}

	public String getHoldCode() {
		return holdCode;
	}

	public void setHoldCode(String holdCode) {
		this.holdCode = holdCode;
	}

	public String getHoldReason() {
		return holdReason;
	}

	public void setHoldReason(String holdReason) {
		this.holdReason = holdReason;
	}

	public String getHoldOwner() {
		return holdOwner;
	}

	public void setHoldOwner(String holdOwner) {
		this.holdOwner = holdOwner;
	}

	public String getReworkCode() {
		return reworkCode;
	}

	public void setReworkCode(String reworkCode) {
		this.reworkCode = reworkCode;
	}

	public String getReworkProcedureName() {
		return reworkProcedureName;
	}

	public void setReworkProcedureName(String reworkProcedureName) {
		this.reworkProcedureName = reworkProcedureName;
	}

	public Long getReworkProcedureVersion() {
		return reworkProcedureVersion;
	}

	public void setReworkProcedureVersion(Long reworkProcedureVersion) {
		this.reworkProcedureVersion = reworkProcedureVersion;
	}

	public List<XEdcBinSetLine> getBinSetLines() {
		return binSetLines;
	}

	public void setBinSetLines(List<XEdcBinSetLine> binSetLines) {
		this.binSetLines = binSetLines;
	}
	
}
