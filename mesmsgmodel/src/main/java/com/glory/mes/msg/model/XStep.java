package com.glory.mes.msg.model;


import java.lang.reflect.Field;
import java.util.Date;
import java.util.List;

import javax.persistence.Transient;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.xml.FieldDateAdapter;
import com.google.common.collect.Lists;

@XmlRootElement(name = "STEP")
@XmlAccessorType(XmlAccessType.NONE)
public class XStep extends XCReworkPrdBase {
		
	@XmlElement(name="OWNER")
	private String owner;
	
	@XmlElement(name="COMMENTS")
	private String comments;
 	
	@XmlElement(name="ISTERMINATIONIMPLICIT")
	private String isTerminationImplicit;
		
	@XmlElement(name="STAGEID")
	private String stageId;
	
	@XmlElement(name="CAPABILITY")
	private Long capability;
	
	@XmlElement(name = "CAPABILITYNAME")
	private String capabilityName;

	/**
	 * 可多设备同时加工一个批次
	 */
	@XmlElement(name="ISMULTIEQP")
	private String isMultiEqp;
	
	/**
	 * 必须设备,否则不能TrackIn
	 */
	@XmlElement(name="ISREQUIREEQP")
	private String isRequireEqp;
	
	/**
	 * 允许Batch作业
	 */
	@XmlElement(name="ISBATCH")
	private String isBatch;
	
	/**
	 * 允许直接MoveNext作业
	 */
	@XmlElement(name="ISMOVENEXT")
	private String isMoveNext;
	
	@XmlElement(name="ISALLOWREPEAT")
	private String isAllowRepeat;
	
	@XmlElement(name="ISUSEPROCESSSTATE")
	private String isUseProcessState;
	
	@XmlElement(name = "USECATEGORY")
	private String useCategory;
	
	@XmlElement(name="HOLDCODESRC")
	private String holdCodeSrc;
	
	@XmlElement(name="RELEASECODESRC")
	private String releaseCodeSrc;
	
	@XmlElement(name="SCRAPCODESRC")
	private String scrapCodeSrc;
	
	@XmlElement(name="BONUSCODESRC")
	private String bonusCodeSrc;
	
	@XmlElement(name="REWORKCODESRC")
	private String reworkCodeSrc;
	
	@XmlElement(name="DEFECTCODESRC")
	private String defectCodeSrc;
	
	@XmlElement(name = "STATUS")
	private String status;
	
	@XmlElement(name = "ACTIVETIME")
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	private Date activeTime;
	
	@XmlElement(name = "ACTIVEUSER")
	private String activeUser;
	
	@XmlElement(name = "TRACKINWIZARD")
	private String trackInWizard;
	
	@XmlElement(name = "ABORTWIZARD")
	private String abortWizard;

	@XmlElement(name = "TRACKOUTWIZARD")
	private String trackOutWizard;
	
	@XmlElement(name = "MOVENEXTWIZARD")
	private String moveNextWizard;
	
	@XmlElement(name = "INMAINMATTYPE")
	private String inMainMatType;
	
	@XmlElement(name = "OUTMAINMATTYPE")
	private String outMainMatType;
	
	@XmlElement(name="CONTAMINATIONLEVEL")
	private String contaminationLevel;
	
	@XmlElementWrapper(name="PRDOPERATIONLIST")
	@XmlElementRef
	private List<XOperation> operationList;
	
	@XmlElementWrapper(name="STEPATTRIBUTELIST")
	@XmlElementRef
	private List<XStepAttribute> stepAttributeList;
	
	@XmlElement(name="RESERVED1")
	private String reserved1;
	
	@XmlElement(name="RESERVED2")
	private String reserved2;
	
	@XmlElement(name="RESERVED3")
	private String reserved3;
	
	@XmlElement(name="RESERVED4")
	private String reserved4;
	
	@XmlElement(name="RESERVED5")
	private String reserved5;
	
	@XmlElement(name="RESERVED6")
	private String reserved6;
	
	@XmlElement(name="RESERVED7")
	private String reserved7;
	
	@XmlElement(name="RESERVED8")
	private String reserved8;
	
	@XmlElement(name="RESERVED9")
	private String reserved9;

	@XmlElement(name="RESERVED10")
	private String reserved10;
	
	@XmlElement(name="KEEPBATCH")
	private String keepBatch;
	
	@XmlElement(name="ISCLEARUSECOUNT")
	private String isClearUseCount;
	
	@XmlElement(name="ISUSECOUNT")
	private String isUseCount;
	
	@XmlElement(name="LOTLOWYIELDHOLD")
	private String lotLowYieldHold;
	
	@XmlElement(name="LOTLOWYIELDHOLDBYSTAGE")
	private String lotLowYieldHoldByStage;
	
	@XmlElement(name="WOLOWYIELDHOLD")
	private String woLowYieldHold;
	
	@XmlElement(name="ISALLOWHOLDTRACKOUT")
	private String isAllowHoldTrackOut;
	
	@XmlElement(name="ISUSEFLOWCONDITION")
	private String isUseFlowCondition;
	
	@XmlElement(name="ISRESERVEEQP")
	private String isReserveEqp;
	
	@XmlElement(name="ISUPDATELOTATTRIBUTE")
	private String isUpdateLotAttribute;
	
	@XmlElement(name="ISUSESUBEQP")
	private String isUseSubEqp;
	
	@XmlElement(name="NOSKIP")
	private String noSkip;
	
	@XmlElement(name="ISUSECAPARANGE")
	private String isUseCapaRange;
	
	@XmlElement(name="N2STOCK")
	private String n2Stock;

	@Transient
	private String reworkFlow;
	
	@Transient
	private String stepAttributeString;

	/**
	 * 描述： 生产阶段，映射为：STAGEID；
	 * @return stageId
	 */
	public String getStageId() {
		return stageId;
	}

	public void setStageId(String stageId) {
		this.stageId = stageId;
	}

	/**
	 * 描述： 工步权限，映射为：CAPABILITY；
	 * @return capability
	 */
	public Long getCapability() {
		return capability;
	}

	public void setCapability(Long capability) {
		this.capability = capability;
	}
	
	public String getCapabilityName() {
		return capabilityName;
	}

	public void setCapabilityName(String capabilityName) {
		this.capabilityName = capabilityName;
	}

	public void setIsMultiEqp(Boolean isMultiEqp) {
		this.isMultiEqp = isMultiEqp ? "Y" : "N";
	}
	
	/**
	 * 描述： 是否多设备作业，映射为：ISMULTIEQP；
	 * @return isMultiEqp
	 */
	public Boolean getIsMultiEqp(){
		return "Y".equalsIgnoreCase(this.isMultiEqp) ? true : false; 
	}

	public void setIsRequireEqp(Boolean isRequireEqp) {
		this.isRequireEqp = isRequireEqp ? "Y" : "N";
	}
	
	/**
	 * 描述： 是否需要设备，映射为：ISREQUIREEQP；
	 * @return isRequireEqp
	 */
	public Boolean getIsRequireEqp(){
		return "Y".equalsIgnoreCase(this.isRequireEqp) ? true : false; 
	}

	public void setIsBatch(Boolean isBatch) {
		this.isBatch = isBatch ? "Y" : "N";
	}
	
	/**
	 * 描述： 按Batch作业，映射为：ISBATCH；
	 * @return isBatch
	 */
	public Boolean getIsBatch() {
		return "Y".equalsIgnoreCase(this.isBatch) ? true : false; 
	}

	public void setIsMoveNext(Boolean isMoveNext) {
		this.isMoveNext = isMoveNext ? "Y" : "N";
	}
	
	/**
	 * 描述：可MoveNext，映射为：ISMOVENEXT；
	 * @return isMoveNext
	 */
	public Boolean getIsMoveNext(){
		return "Y".equalsIgnoreCase(this.isMoveNext) ? true : false; 
	}
	
	public Boolean getIsAllowRepeat() {
		return "Y".equalsIgnoreCase(this.isAllowRepeat) ? true : false;
	}

	public void setIsAllowRepeat(Boolean isAllowRepeat) {
		this.isAllowRepeat = isAllowRepeat ? "Y" : "N";
	}

	public Boolean getIsUseProcessState() {
		return "Y".equalsIgnoreCase(this.isUseProcessState) ? true : false;
	}

	public void setIsUseProcessState(Boolean isUseProcessState) {
		this.isUseProcessState = isUseProcessState ? "Y" : "N";
	}

	/**
	 * 描述： 使用类型，映射为：USECATEGORY；
	 * @return useCategory
	 */
	public String getUseCategory() {
		return useCategory;
	}

	public void setUseCategory(String useCategory) {
		this.useCategory = useCategory;
	}

	/**
	 * 描述： 暂停码，映射为：HOLDCODESRC；
	 * @return holdCodeSrc
	 */
	public String getHoldCodeSrc() {
		return holdCodeSrc;
	}

	public void setHoldCodeSrc(String holdCodeSrc) {
		this.holdCodeSrc = holdCodeSrc;
	}

	/**
	 * 描述： 放行码，映射为：RELEASECODESRC；
	 * @return releaseCodeSrc
	 */
	public String getReleaseCodeSrc() {
		return releaseCodeSrc;
	}

	public void setReleaseCodeSrc(String releaseCodeSrc) {
		this.releaseCodeSrc = releaseCodeSrc;
	}

	/**
	 * 描述： 报废码，映射为：SCRAPCODESRC；
	 * @return scrapCodeSrc
	 */
	public String getScrapCodeSrc() {
		return scrapCodeSrc;
	}

	public void setScrapCodeSrc(String scrapCodeSrc) {
		this.scrapCodeSrc = scrapCodeSrc;
	}

	/**
	 * 描述： 数量增加代码表，映射为：BONUSCODESRC；
	 * @return bonusCodeSrc
	 */
	public String getBonusCodeSrc() {
		return bonusCodeSrc;
	}

	public void setBonusCodeSrc(String bonusCodeSrc) {
		this.bonusCodeSrc = bonusCodeSrc;
	}

	/**
	 * 描述：返工码 ，映射为：REWORKCODESRC；
	 * @return reworkCodeSrc
	 */
	public String getReworkCodeSrc() {
		return reworkCodeSrc;
	}

	public void setReworkCodeSrc(String reworkCodeSrc) {
		this.reworkCodeSrc = reworkCodeSrc;
	}

	/**
	 * 描述： 缺陷码，映射为：DEFECTCODESRC；
	 * @return defectCodeSrc
	 */
	public String getDefectCodeSrc() {
		return defectCodeSrc;
	}

	public void setDefectCodeSrc(String defectCodeSrc) {
		this.defectCodeSrc = defectCodeSrc;
	}

	/**
	 * 描述： 责任人，映射为：OWNER；
	 * @return owner
	 */
	public String getOwner() {
		return owner;
	}

	public void setOwner(String owner) {
		this.owner = owner;
	}

	/**
	 * 描述： 备注，映射为：COMMENTS；
	 * @return comments
	 */
	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}
	
	/**
	 * 描述： ，映射为：ISTERMINATIONIMPLICIT；
	 * @return isTerminationImplicit
	 */
	public Boolean getIsTerminationImplicit() {
		return "Y".equalsIgnoreCase(this.isTerminationImplicit) ? true : false; 
	}
	
	public void setIsTerminationImplicit(Boolean isTerminationImplicit) {
		this.isTerminationImplicit = isTerminationImplicit ?  "Y" : "N";
	}
	
	/**
	 * 描述： 备用字段1，映射为：RESERVED1；
	 * @return reserved1
	 */
	public String getReserved1() {
		return reserved1;
	}

	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}

	/**
	 * 描述： 备用字段2，映射为：RESERVED2；
	 * @return reserved2
	 */
	public String getReserved2() {
		return reserved2;
	}

	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}

	/**
	 * 描述： 备用字段3，映射为：RESERVED3；
	 * @return reserved3
	 */
	public String getReserved3() {
		return reserved3;
	}

	public void setReserved3(String reserved3) {
		this.reserved3 = reserved3;
	}

	/**
	 * 描述：备用字段4 ，映射为：RESERVED4；
	 * @return reserved4
	 */
	public String getReserved4() {
		return reserved4;
	}

	public void setReserved4(String reserved4) {
		this.reserved4 = reserved4;
	}

	/**
	 * 描述： 备用字段5，映射为：RESERVED5；
	 * @return reserved5
	 */
	public String getReserved5() {
		return reserved5;
	}

	public void setReserved5(String reserved5) {
		this.reserved5 = reserved5;
	}

	/**
	 * 描述：备用字段6 ，映射为：RESERVED6；
	 * @return reserved6
	 */
	public String getReserved6() {
		return reserved6;
	}

	public void setReserved6(String reserved6) {
		this.reserved6 = reserved6;
	}

	/**
	 * 描述： 备用字段7，映射为：RESERVED7；
	 * @return reserved7
	 */
	public String getReserved7() {
		return reserved7;
	}

	public void setReserved7(String reserved7) {
		this.reserved7 = reserved7;
	}

	/**
	 * 描述： 备用字段8，映射为：RESERVED8；
	 * @return reserved8
	 */
	public String getReserved8() {
		return reserved8;
	}

	public void setReserved8(String reserved8) {
		this.reserved8 = reserved8;
	}

	/**
	 * 描述： 备用字段9，映射为：RESERVED9；
	 * @return reserved9
	 */
	public String getReserved9() {
		return reserved9;
	}

	public void setReserved9(String reserved9) {
		this.reserved9 = reserved9;
	}

	/**
	 * 描述：备用字段10 ，映射为：RESERVED10；
	 * @return reserved10
	 */
	public String getReserved10() {
		return reserved10;
	}

	public void setReserved10(String reserved10) {
		this.reserved10 = reserved10;
	}

	/**
	 * 描述：状态 ，映射为：STATUS；
	 * @return status
	 */
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	/**
	 * 描述：激活时间 ，映射为：ACTIVETIME；
	 * @return activeTime
	 */
	public Date getActiveTime() {
		return activeTime;
	}

	public void setActiveTime(Date activeTime) {
		this.activeTime = activeTime;
	}

	/**
	 * 描述：激活人 ，映射为：ACTIVEUSER；
	 * @return activeUser
	 */
	public String getActiveUser() {
		return activeUser;
	}

	public void setActiveUser(String activeUser) {
		this.activeUser = activeUser;
	}

	/**
	 * 描述：保留Batch到下一站 ，映射为：KEEPBATCH；
	 * @return keepBatch
	 */
	public Boolean getKeepBatch() {
		return "Y".equalsIgnoreCase(this.keepBatch) ? true : false;
	}

	public void setKeepBatch(Boolean keepBatch) {
		this.keepBatch = keepBatch ? "Y" : "N";
	}

	/**
	 * 描述：清除次数 ，映射为：ISCLEARUSECOUNT；
	 * @return isClearUseCount
	 */
	public Boolean getIsClearUseCount() {
		return "Y".equalsIgnoreCase(this.isClearUseCount) ? true : false;
	}

	public void setIsClearUseCount(Boolean isClearUseCount) {
		this.isClearUseCount = isClearUseCount ? "Y" : "N";
	}

	/**
	 * 描述：使用次数累加 ，映射为：ISUSECOUNT；
	 * @return isUseCount
	 */
	public Boolean getIsUseCount() {
		return "Y".equalsIgnoreCase(this.isUseCount) ? true : false;
	}

	public void setIsUseCount(Boolean isUseCount) {
		this.isUseCount = isUseCount ? "Y" : "N";
	}

	/**
	 * 描述：批次低良率暂停 ，映射为：LOTLOWYIELDHOLD；
	 * @return lotLowYieldHold
	 */
	public Boolean getLotLowYieldHold() {
		return "Y".equalsIgnoreCase(this.lotLowYieldHold) ? true : false;
	}

	public void setLotLowYieldHold(Boolean lotLowYieldHold) {
		this.lotLowYieldHold = lotLowYieldHold ? "Y" : "N";
	}

	public Boolean getLotLowYieldHoldByStage() {
		return "Y".equalsIgnoreCase(this.lotLowYieldHoldByStage) ? true : false;
	}

	public void setLotLowYieldHoldByStage(Boolean lotLowYieldHoldByStage) {
		this.lotLowYieldHoldByStage = lotLowYieldHoldByStage ? "Y" : "N";
	}

	/**
	 * 描述：工单低良率暂停 ，映射为：WOLOWYIELDHOLD；
	 * @return woLowYieldHold
	 */
	public Boolean getWoLowYieldHold() {
		return "Y".equalsIgnoreCase(this.woLowYieldHold) ? true : false;
	}

	public void setWoLowYieldHold(Boolean woLowYieldHold) {
		this.woLowYieldHold = woLowYieldHold ? "Y" : "N";
	}

	/**
	 * 描述：使用流程条件 ，映射为：ISUSEFLOWCONDITION；
	 * @return isUseFlowCondition
	 */
	public Boolean getIsUseFlowCondition() {
		return "Y".equalsIgnoreCase(this.isUseFlowCondition) ? true : false;
	}

	public void setIsUseFlowCondition(Boolean isUseFlowCondition) {
		this.isUseFlowCondition = isUseFlowCondition ? "Y" : "N";
	}

	public Boolean getIsAllowHoldTrackOut() {
		return "Y".equalsIgnoreCase(this.isAllowHoldTrackOut) ? true : false;
	}

	public void setIsAllowHoldTrackOut(Boolean isAllowHoldTrackOut) {
		this.isAllowHoldTrackOut = isAllowHoldTrackOut ? "Y" : "N";
	}

	public Boolean getIsReserveEqp() {
		return "Y".equalsIgnoreCase(this.isReserveEqp) ? true : false;
	}

	public void setIsReserveEqp(Boolean isReserveEqp) {
		this.isReserveEqp = isReserveEqp ? "Y" : "N";
	}

	public Boolean getIsUpdateLotAttribute() {
		return "Y".equalsIgnoreCase(this.isUpdateLotAttribute) ? true : false;
	}

	public void setIsUpdateLotAttribute(Boolean isUpdateLotAttribute) {
		this.isUpdateLotAttribute = isUpdateLotAttribute ? "Y" : "N";
	}

	/**
	 * 描述：使用子设备加工 ，映射为：ISUSESUBEQP；
	 * @return isUseSubEqp
	 */
	public Boolean getIsUseSubEqp() {
		return "Y".equalsIgnoreCase(this.isUseSubEqp) ? true : false;
	}

	public void setIsUseSubEqp(Boolean isUseSubEqp) {
		this.isUseSubEqp = isUseSubEqp ? "Y" : "N";
	}

	/**
	 * 描述：禁止跳步，映射为：NOSKIP；
	 * @return noSkip
	 */
	public Boolean getNoSkip() {
		return "Y".equalsIgnoreCase(this.noSkip) ? true : false;
	}

	public void setNoSkip(Boolean noSkip) {
		this.noSkip = noSkip ? "Y" : "N";
	}

	/**
	 * 描述：使用能力范围 ，映射为：ISUSECAPARANGE；
	 * @return isUseCapaRange
	 */
	public Boolean getIsUseCapaRange() {
		return "Y".equalsIgnoreCase(this.isUseCapaRange) ? true : false;
	}

	public void setIsUseCapaRange(Boolean isUseCapaRange) {
		this.isUseCapaRange = isUseCapaRange ? "Y" : "N";
	}
	
	public Boolean getN2Stock() {
		return "Y".equalsIgnoreCase(this.n2Stock) ? true : false;
	}

	public void setN2Stock(Boolean n2Stock) {
		this.n2Stock = n2Stock ? "Y" : "N";
	}

	/**
	 * 描述：trackInWizard，映射为：TRACKINWIZARD；
	 * @return trackInWizard
	 */
	public String getTrackInWizard() {
		return trackInWizard;
	}

	public void setTrackInWizard(String trackInWizard) {
		this.trackInWizard = trackInWizard;
	}

	/**
	 * 描述：abortWizard ，映射为：ABORTWIZARD；
	 * @return abortWizard
	 */
	public String getAbortWizard() {
		return abortWizard;
	}

	public void setAbortWizard(String abortWizard) {
		this.abortWizard = abortWizard;
	}

	/**
	 * 描述：使用trackOutWizard范围 ，映射为：TRACKOUTWIZARD；
	 * @return trackOutWizard
	 */
	public String getTrackOutWizard() {
		return trackOutWizard;
	}

	public void setTrackOutWizard(String trackOutWizard) {
		this.trackOutWizard = trackOutWizard;
	}

	/**
	 * 描述：moveNextWizard，映射为：MOVENEXTWIZARD；
	 * @return moveNextWizard
	 */
	public String getMoveNextWizard() {
		return moveNextWizard;
	}

	public void setMoveNextWizard(String moveNextWizard) {
		this.moveNextWizard = moveNextWizard;
	}

	public String getInMainMatType() {
		return inMainMatType;
	}

	public void setInMainMatType(String inMainMatType) {
		this.inMainMatType = inMainMatType;
	}

	public String getOutMainMatType() {
		return outMainMatType;
	}

	public void setOutMainMatType(String outMainMatType) {
		this.outMainMatType = outMainMatType;
	}
	
	public String getContaminationLevel() {
		return contaminationLevel;
	}

	public void setContaminationLevel(String contaminationLevel) {
		this.contaminationLevel = contaminationLevel;
	}
	
	public void setIsTerminationImplicit(String isTerminationImplicit) {
		this.isTerminationImplicit = isTerminationImplicit;
	}

	public void setIsMultiEqp(String isMultiEqp) {
		this.isMultiEqp = isMultiEqp;
	}

	public void setIsRequireEqp(String isRequireEqp) {
		this.isRequireEqp = isRequireEqp;
	}

	public void setIsBatch(String isBatch) {
		this.isBatch = isBatch;
	}

	public void setIsMoveNext(String isMoveNext) {
		this.isMoveNext = isMoveNext;
	}

	public void setKeepBatch(String keepBatch) {
		this.keepBatch = keepBatch;
	}

	public void setIsClearUseCount(String isClearUseCount) {
		this.isClearUseCount = isClearUseCount;
	}

	public void setIsUseCount(String isUseCount) {
		this.isUseCount = isUseCount;
	}

	public void setLotLowYieldHold(String lotLowYieldHold) {
		this.lotLowYieldHold = lotLowYieldHold;
	}

	public void setLotLowYieldHoldByStage(String lotLowYieldHoldByStage) {
		this.lotLowYieldHoldByStage = lotLowYieldHoldByStage;
	}

	public void setWoLowYieldHold(String woLowYieldHold) {
		this.woLowYieldHold = woLowYieldHold;
	}

	public void setIsAllowHoldTrackOut(String isAllowHoldTrackOut) {
		this.isAllowHoldTrackOut = isAllowHoldTrackOut;
	}

	public void setIsUseFlowCondition(String isUseFlowCondition) {
		this.isUseFlowCondition = isUseFlowCondition;
	}

	public void setIsReserveEqp(String isReserveEqp) {
		this.isReserveEqp = isReserveEqp;
	}

	public void setIsUpdateLotAttribute(String isUpdateLotAttribute) {
		this.isUpdateLotAttribute = isUpdateLotAttribute;
	}

	public void setIsUseSubEqp(String isUseSubEqp) {
		this.isUseSubEqp = isUseSubEqp;
	}

	public void setNoSkip(String noSkip) {
		this.noSkip = noSkip;
	}
	
	public void setIsUseCapaRange(String isUseCapaRange) {
		this.isUseCapaRange = isUseCapaRange;
	}

	public List<XOperation> getOperationList() {
		return operationList;
	}

	public void setOperationList(List<XOperation> operationList) {
		this.operationList = operationList;
	}

	public List<XStepAttribute> getStepAttributeList() {
		return stepAttributeList;
	}

	public void setStepAttributeList(List<XStepAttribute> stepAttributeList) {
		this.stepAttributeList = stepAttributeList;
	}
	
	public String getReworkFlow() {
		return reworkFlow;
	}

	public void setReworkFlow(String reworkFlow) {
		this.reworkFlow = reworkFlow;
	}
	
	public String getStepAttributeString() {
		return stepAttributeString;
	}

	public void setStepAttributeString(String stepAttributeString) {
		this.stepAttributeString = stepAttributeString;
	}

	public List<String> getDiffProperty(XStep step) {
		List<String> differentProperties = Lists.newArrayList();
		try {
			Class clazz = XStep.class;
			List<String> names = Lists.newArrayList();
			while (clazz != null){
				Field[] fields = clazz.getDeclaredFields();
				for (Field field : fields) {
					if (!"stageId".equals(field.getName()) && !"reworkFlow".equals(field.getName())) {
						names.add(field.getName());
					}
				}
				clazz = clazz.getSuperclass();
			}
			differentProperties =PropertyUtil.compareProperty(this, step, names);
		} catch (Exception e) {
		}
		return differentProperties;
	}

}
