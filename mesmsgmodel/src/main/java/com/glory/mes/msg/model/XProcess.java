package com.glory.mes.msg.model;

import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldDateAdapter;

@XmlRootElement(name = "PROCESS")
@XmlAccessorType(XmlAccessType.NONE)
public class XProcess extends XPrdBase  {
	
	private static final long serialVersionUID = 1L;

	@XmlElement(name="OWNER")
	private String owner;
	
	@XmlElement(name="COMMENTS")
	private String comments;
 	
	@XmlElement(name="ISTERMINATIONIMPLICIT")
	private String isTerminationImplicit;
		
	@XmlElement(name = "ACTIVETIME")
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	private Date activeTime;
	
	@XmlElement(name = "ACTIVEUSER")
	private String activeUser;
	
	@XmlElement(name="STAGEID")
	private String stageId;
	
	@XmlElement(name="CAPABILITY")
	private Long capability;
	
	/**
	 * 可多设备同时加工一个批次
	 */
	@XmlElement(name="ISMULTIEQP")
	private String isMultiEqp;
	
	/**
	 * 必须设备,否则不能TrackIn
	 */
	@XmlElement(name="ISREQUIREEQP")
	private String isRequireEqp;
	
	/**
	 * 允许Batch作业
	 */
	@XmlElement(name="ISBATCH")
	private String isBatch;
	
	/**
	 * 允许直接MoveNext作业
	 */
	@XmlElement(name="ISMOVENEXT")
	private String isMoveNext;
	
	@XmlElement(name = "USECATEGORY")
	private String useCategory;
	
	@XmlElement(name="HOLDCODESRC")
	private String holdCodeSrc;
	
	@XmlElement(name="RELEASECODESRC")
	private String releaseCodeSrc;
	
	@XmlElement(name="SCRAPCODESRC")
	private String scrapCodeSrc;
	
	@XmlElement(name="BONUSCODESRC")
	private String bonusCodeSrc;
	
	@XmlElement(name="REWORKCODESRC")
	private String reworkCodeSrc;
	
	@XmlElement(name="DEFECTCODESRC")
	private String defectCodeSrc;

	@XmlElement(name="COPYFROM")
	private Long copyFrom;
	
	@XmlElement(name="FLOWDOCUMENT")
	private String flowDocument;
	
	//Save Flow Layout
	@XmlElement(name="FLOWCONTENT")
	private String flowContent;

	@XmlElement(name="BPMNFLOWDOCUMENT")
	private String BpmnFlowDocument;
	
	@XmlElementWrapper(name="PROCEDURELIST")
	@XmlElementRef
	private List<XProcedure> procedureList;
	
	@XmlElementWrapper(name="PROCEDURESTATELIST")
	@XmlElementRef
	private List<XProcedureState> procedureStateList;
	
	@XmlElement(name="RESERVED1")
	private String reserved1;
	
	@XmlElement(name="RESERVED2")
	private String reserved2;
	
	@XmlElement(name="RESERVED3")
	private String reserved3;
	
	@XmlElement(name="RESERVED4")
	private String reserved4;
	
	@XmlElement(name="RESERVED5")
	private String reserved5;
	
	@XmlElement(name="RESERVED6")
	private String reserved6;
	
	@XmlElement(name="RESERVED7")
	private String reserved7;
	
	@XmlElement(name="RESERVED8")
	private String reserved8;
	
	@XmlElement(name="RESERVED9")
	private String reserved9;

	@XmlElement(name="RESERVED10")
	private String reserved10;

	/**
	 * 描述： 生产阶段，映射为：STAGEID；
	 * @return stageId
	 */
	public String getStageId() {
		return stageId;
	}

	public void setStageId(String stageId) {
		this.stageId = stageId;
	}

	/**
	 * 描述： 工序能力，映射为：CAPABILITY；
	 * @return capability
	 */
	public Long getCapability() {
		return capability;
	}

	public void setCapability(Long capability) {
		this.capability = capability;
	}

	public void setIsMultiEqp(Boolean isMultiEqp) {
		this.isMultiEqp = isMultiEqp ? "Y" : "N";
	}
	
	/**
	 * 描述：是否主设备 ，映射为：ISMULTIEQP；
	 * @return isMultiEqp
	 */
	public Boolean getIsMultiEqp(){
		return "Y".equalsIgnoreCase(this.isMultiEqp) ? true : false; 
	}

	public void setIsRequireEqp(Boolean isRequireEqp) {
		this.isRequireEqp = isRequireEqp ? "Y" : "N";
	}
	
	/**
	 * 描述：必须设备 ，映射为：ISREQUIREEQP；
	 * @return isRequireEqp
	 */
	public Boolean getIsRequireEqp(){
		return "Y".equalsIgnoreCase(this.isRequireEqp) ? true : false; 
	}

	public void setIsBatch(Boolean isBatch) {
		this.isBatch = isBatch ? "Y" : "N";
	}
	
	/**
	 * 描述： Batch作业，映射为：ISBATCH；
	 * @return isBatch
	 */
	public Boolean getIsBatch() {
		return "Y".equalsIgnoreCase(this.isBatch) ? true : false; 
	}

	public void setIsMoveNext(Boolean isMoveNext) {
		this.isMoveNext = isMoveNext ? "Y" : "N";
	}
	
	/**
	 * 描述： 可MoveNext，映射为：ISMOVENEXT；
	 * @return isMoveNext
	 */
	public Boolean getIsMoveNext(){
		return "Y".equalsIgnoreCase(this.isMoveNext) ? true : false; 
	}

	/**
	 * 描述：使用类型 ，映射为：USECATEGORY；
	 * @return useCategory
	 */
	public String getUseCategory() {
		return useCategory;
	}

	public void setUseCategory(String useCategory) {
		this.useCategory = useCategory;
	}

	/**
	 * 描述：暂停码 ，映射为：HOLDCODESRC；
	 * @return holdCodeSrc
	 */
	public String getHoldCodeSrc() {
		return holdCodeSrc;
	}

	public void setHoldCodeSrc(String holdCodeSrc) {
		this.holdCodeSrc = holdCodeSrc;
	}
	
	/**
	 * 描述： 放行码，映射为：RELEASECODESRC；
	 * @return releaseCodeSrc
	 */
	public String getReleaseCodeSrc() {
		return releaseCodeSrc;
	}

	public void setReleaseCodeSrc(String releaseCodeSrc) {
		this.releaseCodeSrc = releaseCodeSrc;
	}

	/**
	 * 描述： 报废码，映射为：SCRAPCODESRC；
	 * @return scrapCodeSrc
	 */
	public String getScrapCodeSrc() {
		return scrapCodeSrc;
	}

	public void setScrapCodeSrc(String scrapCodeSrc) {
		this.scrapCodeSrc = scrapCodeSrc;
	}

	/**
	 * 描述： bonusCodeSrc，映射为：BONUSCODESRC；
	 * @return bonusCodeSrc
	 */
	public String getBonusCodeSrc() {
		return bonusCodeSrc;
	}

	public void setBonusCodeSrc(String bonusCodeSrc) {
		this.bonusCodeSrc = bonusCodeSrc;
	}

	/**
	 * 描述： 返工码，映射为：REWORKCODESRC；
	 * @return reworkCodeSrc
	 */
	public String getReworkCodeSrc() {
		return reworkCodeSrc;
	}

	public void setReworkCodeSrc(String reworkCodeSrc) {
		this.reworkCodeSrc = reworkCodeSrc;
	}

	/**
	 * 描述： 缺陷码，映射为：DEFECTCODESRC；
	 * @return defectCodeSrc
	 */
	public String getDefectCodeSrc() {
		return defectCodeSrc;
	}

	public void setDefectCodeSrc(String defectCodeSrc) {
		this.defectCodeSrc = defectCodeSrc;
	}

	/**
	 * 描述： 责任人，映射为：OWNER；
	 * @return owner
	 */
	public String getOwner() {
		return owner;
	}

	public void setOwner(String owner) {
		this.owner = owner;
	}

	/**
	 * 描述： 备注，映射为：COMMENTS；
	 * @return comments
	 */
	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}
	
	/**
	 * 描述： ，映射为：ISTERMINATIONIMPLICIT；
	 * @return isTerminationImplicit
	 */
	public Boolean getIsTerminationImplicit() {
		return "Y".equalsIgnoreCase(this.isTerminationImplicit) ? true : false; 
	}
	
	public void setIsTerminationImplicit(Boolean isTerminationImplicit) {
		this.isTerminationImplicit = isTerminationImplicit ?  "Y" : "N";
	}

	public Date getActiveTime() {
		return activeTime;
	}

	public void setActiveTime(Date activeTime) {
		this.activeTime = activeTime;
	}

	public String getActiveUser() {
		return activeUser;
	}

	public void setActiveUser(String activeUser) {
		this.activeUser = activeUser;
	}

	public Long getCopyFrom() {
		return copyFrom;
	}

	public void setCopyFrom(Long copyFrom) {
		this.copyFrom = copyFrom;
	}

	public String getFlowDocument() {
		return flowDocument;
	}

	public void setFlowDocument(String flowDocument) {
		this.flowDocument = flowDocument;
	}

	public String getFlowContent() {
		return flowContent;
	}

	public void setFlowContent(String flowContent) {
		this.flowContent = flowContent;
	}

	public String getBpmnFlowDocument() {
		return BpmnFlowDocument;
	}

	public void setBpmnFlowDocument(String bpmnFlowDocument) {
		BpmnFlowDocument = bpmnFlowDocument;
	}

	public List<XProcedure> getProcedureList() {
		return procedureList;
	}

	public void setProcedureList(List<XProcedure> procedureList) {
		this.procedureList = procedureList;
	}

	public List<XProcedureState> getProcedureStateList() {
		return procedureStateList;
	}

	public void setProcedureStateList(List<XProcedureState> procedureStateList) {
		this.procedureStateList = procedureStateList;
	}

	public String getReserved1() {
		return reserved1;
	}

	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}

	public String getReserved2() {
		return reserved2;
	}

	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}

	public String getReserved3() {
		return reserved3;
	}

	public void setReserved3(String reserved3) {
		this.reserved3 = reserved3;
	}

	public String getReserved4() {
		return reserved4;
	}

	public void setReserved4(String reserved4) {
		this.reserved4 = reserved4;
	}

	public String getReserved5() {
		return reserved5;
	}

	public void setReserved5(String reserved5) {
		this.reserved5 = reserved5;
	}

	public String getReserved6() {
		return reserved6;
	}

	public void setReserved6(String reserved6) {
		this.reserved6 = reserved6;
	}

	public String getReserved7() {
		return reserved7;
	}

	public void setReserved7(String reserved7) {
		this.reserved7 = reserved7;
	}

	public String getReserved8() {
		return reserved8;
	}

	public void setReserved8(String reserved8) {
		this.reserved8 = reserved8;
	}

	public String getReserved9() {
		return reserved9;
	}

	public void setReserved9(String reserved9) {
		this.reserved9 = reserved9;
	}

	public String getReserved10() {
		return reserved10;
	}

	public void setReserved10(String reserved10) {
		this.reserved10 = reserved10;
	}
	
}
