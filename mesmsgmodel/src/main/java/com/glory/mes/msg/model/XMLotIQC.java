package com.glory.mes.msg.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "MMLOTIQC")
@XmlAccessorType(XmlAccessType.NONE)
public class XMLotIQC extends XBase{

	@XmlElement(name="HISSEQ")
	private String hisSeq;
	
	@XmlElement(name="MLOTRRN")
	private Long mLotRrn;

	@XmlElement(name = "MLOTID")
	private String mLotId;
	
	@XmlElement(name = "LOTSAMPQTY")
	private BigDecimal lotSampQty;
	
	@XmlElement(name = "BADQTY")
	private BigDecimal badQty;
	
	@XmlElement(name = "GRADE1")
	private String grade1; 
	
	@XmlElement(name = "GRADE2")
	private String grade2;
	
	@XmlElement(name = "JUDGERESULT")
	private String judgeResult;

	@XmlElement(name = "COMMENTS")
	private String comments;
	
	@XmlElementWrapper(name="MLOTIQCDETAILLIST")
	@XmlElementRef
	private List<XMLotIQCDetail> details = new ArrayList<XMLotIQCDetail>();
	
	@XmlElement(name = "OBJECTRRN")
	protected Long objectRrn;

	public Long getObjectRrn() {
		return this.objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}
	
	public String getHisSeq() {
		return hisSeq;
	}

	public void setHisSeq(String hisSeq) {
		this.hisSeq = hisSeq;
	}

	public Long getmLotRrn() {
		return mLotRrn;
	}

	public void setmLotRrn(Long mLotRrn) {
		this.mLotRrn = mLotRrn;
	}

	public String getmLotId() {
		return mLotId;
	}

	public void setmLotId(String mLotId) {
		this.mLotId = mLotId;
	}

	public BigDecimal getLotSampQty() {
		return lotSampQty;
	}

	public void setLotSampQty(BigDecimal lotSampQty) {
		this.lotSampQty = lotSampQty;
	}

	public BigDecimal getBadQty() {
		return badQty;
	}

	public void setBadQty(BigDecimal badQty) {
		this.badQty = badQty;
	}

	public String getGrade1() {
		return grade1;
	}

	public void setGrade1(String grade1) {
		this.grade1 = grade1;
	}

	public String getGrade2() {
		return grade2;
	}

	public void setGrade2(String grade2) {
		this.grade2 = grade2;
	}

	public String getJudgeResult() {
		return judgeResult;
	}

	public void setJudgeResult(String judgeResult) {
		this.judgeResult = judgeResult;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public List<XMLotIQCDetail> getDetails() {
		return details;
	}

	public void setDetails(List<XMLotIQCDetail> details) {
		this.details = details;
	}
	
}
