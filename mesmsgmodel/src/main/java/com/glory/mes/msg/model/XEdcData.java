package com.glory.mes.msg.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldDateAdapter;

@XmlRootElement(name = "DATA")
@XmlAccessorType(XmlAccessType.NONE)
public class XEdcData implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name="ORGRRN")
	private Long orgRrn = 0L;

	@XmlElement(name="HISSEQ")
	private String hisSeq;

	@XmlElement(name="ORGID")
    private String orgId;

    /**
     * 数据采集类型
     * ITEM:普通的数据采集,包含Variable类型和Attribute类型
     * BIN:BIN数据采集,特殊类型的Attribute类型数据采集 
     * TEXT:文本采集,支持采集字符串
     */
	@XmlElement(name="EDCTYPE")
    private String edcType;

    /**
     * 数据采集来源(使用什么功能进行的数据采集)
     * 不同来源可能会对数据结果有不同的处理方式
     * LOT:在线批次数据采集,对正在加工的批次进行数据采集,采集的结果会实施反馈到生产过程中
     * OFFLINELOT:离线批次数据采集,对批次进行数据采集,记录采集相关批次,不对批次产生影响
     * GENERAL:一般数据采集,与批次无关,如采集环境或设备相关数据
     */
	@XmlElement(name="EDCFROM")
    private String edcFrom;

	@XmlElement(name="EDCSETRRN")
    private Long edcSetRrn;

	@XmlElement(name="EDCSETNAME")
    private String edcSetName;

	@XmlElement(name="EDCSETVERSION")
    private Long edcSetVersion;

	@XmlElement(name="ITEMNAME")
    private String itemName; //数据采集项

	@XmlElement(name="ITEMSEQNO")
    private Long seqNo;

	@XmlElement(name="LINEID")
    private String lineId; //线别

	@XmlElement(name="TEAMID")
    private String teamId; //班组
    
	@XmlElement(name="LOTRRN")
    private Long lotRrn;

	@XmlElement(name="LOTID")
    private String lotId;

	@XmlElement(name="LOTTYPE")
    private String lotType;

	@XmlElement(name="LOTCOUNT")
    private Long lotCount;

	@XmlElement(name="BATCHID")
    private String batchId;

    /**
     * 按Batch采集时,Batch内的所有批次,以";"分隔
     */
	@XmlElement(name="BATCHLOTS")
    private String batchLots;

	@XmlElement(name="OPERATOR")
    private String operator;

	@XmlElement(name="PROCESSEQP")
    private String processEqp; //加工设备

	@XmlElement(name="MEASUREEQP")
    private String measureEqp; //测量设备

	@XmlJavaTypeAdapter(FieldDateAdapter.class)
    @XmlElement(name="MEASURETIME")
    private Date measureTime; //测量时间

    @XmlElement(name="MATERIALID")
    private String materialId;

    @XmlElement(name="CUSTOMER")
    private String customer;
    
    @XmlElement(name="STEPNAME")
    private String stepName;

    @XmlElement(name="STEPVERSION")
    private Long stepVersion;

    @XmlElement(name="PARTNAME")
    private String partName;

    @XmlElement(name="PARTVERSION")
    private Long partVersion;

    /**
     * 数据类型
     * Variable:计量型
     * Attribute:计数型
     */
    @XmlElement(name="DATATYPE")
    private String dataType;

    /**
     * 采样计划类型
     * MANUAL:手工设置,自己制定采用计划(采样数量)
     * GB:采用国标,根据国标计算采样数量
     */
    @XmlElement(name="SAMPLETYPE")
    private String sampleType;
    
    /**
     * 采样计划
     */
    @XmlElement(name="SAMPLEPLAN")
    private String samplePlan;

    /**
     * 样本数量
     */
    @XmlElement(name="SAMPLESIZE")
    private Long sampleSize;

    /**
     * 样本容量
     */
    @XmlElement(name="SUBGROUPSIZE")
    private Long subgroupSize;

    @XmlElement(name="USL")
    private Double usl;

    @XmlElement(name="SL")
    private Double sl;

    @XmlElement(name="LSL")
    private Double lsl;

    @XmlElement(name="TOTLAQTY")
    private BigDecimal totalQty;

    @XmlElement(name="PASSQTY")
    private BigDecimal passQty;
    
    @XmlElement(name="BADQTY")
    private BigDecimal badQty;

    /**
     * 采集时显示的名称
     */
    @XmlElement(name="DCNAME")
    private String dcName;

    /**
     * 采集到的数据(多个数据以";"分隔)
     */
    @XmlElement(name="DCDATA")
    private String dcData;
    
    @XmlElement(name="DCREMARK")
    private String dcRemark;

    /**
     * 采集到的数据的平均值
     */
    @XmlElement(name="DCDATAAVG")
    private Double dcDataAvg;

    /**
     * 数据OOS列表(多个数据以";"分隔)
     * 记录数据所对应的顺序,以1开始
     * 如1;3;8, 表示第1,3,8个数据OOS
     */
    @XmlElement(name="OOSLIST")
    private String oosList;

    /**
     * 对应的片号列表
     */
    @XmlElement(name="COMPONENTLIST")
    private String componentList;

    /**
     * 对应的片RRN列表
     */
    @XmlElement(name="COMPONENTRRNLIST")
    private String componentRrnList;

    /**
     * 是否临时数据采集
     */
    @XmlElement(name="ISTEMP")
    private String isTemp;

    @XmlElement(name="ISRETEST")
    private String isRetest = XSpcProcessInfo.TEST_TYPE_NORMAL;
    
    @XmlElement(name="ISHOLDLOT")
    private String isHoldLot;

    @XmlElement(name="ISHOLDEQP")
    private String isHoldEqp;

    @XmlElement(name="ATTRIBUTE1")
    private String attribute1;
    
    @XmlElement(name="ATTRIBUTE2")
    private String attribute2;

    @XmlElement(name="ATTRIBUTE3")
    private String attribute3;

    @XmlElement(name="ATTRIBUTE4")
    private String attribute4;

    @XmlElement(name="ATTRIBUTE5")
    private String attribute5;

    public String getHisSeq() {
        return hisSeq;
    }

    public void setHisSeq(String hisSeq) {
        this.hisSeq = hisSeq;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getEdcType() {
        return edcType;
    }

    public void setEdcType(String edcType) {
        this.edcType = edcType;
    }

    public String getEdcFrom() {
        return edcFrom;
    }

    public void setEdcFrom(String edcFrom) {
        this.edcFrom = edcFrom;
    }

    public Long getEdcSetRrn() {
        return edcSetRrn;
    }

    public void setEdcSetRrn(Long edcSetRrn) {
        this.edcSetRrn = edcSetRrn;
    }

    public String getEdcSetName() {
        return edcSetName;
    }

    public void setEdcSetName(String edcSetName) {
        this.edcSetName = edcSetName;
    }

    public Long getEdcSetVersion() {
        return edcSetVersion;
    }

    public void setEdcSetVersion(Long edcSetVersion) {
        this.edcSetVersion = edcSetVersion;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public Long getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(Long seqNo) {
        this.seqNo = seqNo;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getTeamId() {
        return teamId;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public Long getLotRrn() {
        return lotRrn;
    }

    public void setLotRrn(Long lotRrn) {
        this.lotRrn = lotRrn;
    }

    public String getLotId() {
        return lotId;
    }

    public void setLotId(String lotId) {
        this.lotId = lotId;
    }

    public String getLotType() {
        return lotType;
    }

    public void setLotType(String lotType) {
        this.lotType = lotType;
    }

    public Long getLotCount() {
        return lotCount;
    }

    public void setLotCount(Long lotCount) {
        this.lotCount = lotCount;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getBatchLots() {
        return batchLots;
    }

    public void setBatchLots(String batchLots) {
        this.batchLots = batchLots;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getProcessEqp() {
        return processEqp;
    }

    public void setProcessEqp(String processEqp) {
        this.processEqp = processEqp;
    }

    public String getMeasureEqp() {
        return measureEqp;
    }

    public void setMeasureEqp(String measureEqp) {
        this.measureEqp = measureEqp;
    }

    public Date getMeasureTime() {
        return measureTime;
    }

    public void setMeasureTime(Date measureTime) {
        this.measureTime = measureTime;
    }

    public String getMaterialId() {
        return materialId;
    }

    public void setMaterialId(String materialId) {
        this.materialId = materialId;
    }

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getStepName() {
        return stepName;
    }

    public void setStepName(String stepName) {
        this.stepName = stepName;
    }

    public Long getStepVersion() {
        return stepVersion;
    }

    public void setStepVersion(Long stepVersion) {
        this.stepVersion = stepVersion;
    }

    public String getPartName() {
        return partName;
    }

    public void setPartName(String partName) {
        this.partName = partName;
    }

    public Long getPartVersion() {
        return partVersion;
    }

    public void setPartVersion(Long partVersion) {
        this.partVersion = partVersion;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getSampleType() {
        return sampleType;
    }

    public void setSampleType(String sampleType) {
        this.sampleType = sampleType;
    }

    public String getSamplePlan() {
        return samplePlan;
    }

    public void setSamplePlan(String samplePlan) {
        this.samplePlan = samplePlan;
    }

    public Long getSampleSize() {
        return sampleSize;
    }

    public void setSampleSize(Long sampleSize) {
        this.sampleSize = sampleSize;
    }

    public Long getSubgroupSize() {
        return subgroupSize;
    }

    public void setSubgroupSize(Long subgroupSize) {
        this.subgroupSize = subgroupSize;
    }

    public Double getUsl() {
        return usl;
    }

    public void setUsl(Double usl) {
        this.usl = usl;
    }

    public Double getSl() {
        return sl;
    }

    public void setSl(Double sl) {
        this.sl = sl;
    }

    public Double getLsl() {
        return lsl;
    }

    public void setLsl(Double lsl) {
        this.lsl = lsl;
    }

    public BigDecimal getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public BigDecimal getPassQty() {
        return passQty;
    }

    public void setPassQty(BigDecimal passQty) {
        this.passQty = passQty;
    }

    public BigDecimal getBadQty() {
        return badQty;
    }

    public void setBadQty(BigDecimal badQty) {
        this.badQty = badQty;
    }

    public String getDcName() {
        return dcName;
    }

    public void setDcName(String dcName) {
        this.dcName = dcName;
    }

    public String getDcData() {
        return dcData;
    }

    public void setDcData(String dcData) {
        this.dcData = dcData;
    }

    public String getDcRemark() {
        return dcRemark;
    }

    public void setDcRemark(String dcRemark) {
        this.dcRemark = dcRemark;
    }

    public Double getDcDataAvg() {
        return dcDataAvg;
    }

    public void setDcDataAvg(Double dcDataAvg) {
        this.dcDataAvg = dcDataAvg;
    }

    public String getOosList() {
        return oosList;
    }

    public void setOosList(String oosList) {
        this.oosList = oosList;
    }

    public String getComponentList() {
        return componentList;
    }

    public void setComponentList(String componentList) {
        this.componentList = componentList;
    }

    public String getComponentRrnList() {
        return componentRrnList;
    }

    public void setComponentRrnList(String componentRrnList) {
        this.componentRrnList = componentRrnList;
    }

	public Boolean getIsHoldEqp() {
		return "Y".equalsIgnoreCase(this.isHoldEqp) ? true : false;
	}
	
	public void setIsHoldEqp(Boolean isHoldEqp) {
		this.isHoldEqp = isHoldEqp ? "Y" : "N";
	}

	public Boolean getIsHoldLot() {
		return "Y".equalsIgnoreCase(this.isHoldLot) ? true : false;
	}
	
	public void setIsHoldLot(Boolean isHoldLot) {
		this.isHoldLot = isHoldLot ? "Y" : "N";
	}

	public Boolean getIsTemp() {
		return "Y".equalsIgnoreCase(this.isTemp) ? true : false;
	}

	public void setIsTemp(Boolean isTemp) {
		this.isTemp = isTemp ? "Y" : "N";
	}
	
	public String getIsRetest() {
		return isRetest;
	}

	public void setIsRetest(String isRetest) {
		this.isRetest = isRetest;
	}

    public String getAttribute1() {
        return attribute1;
    }

    public void setAttribute1(String attribute1) {
        this.attribute1 = attribute1;
    }

    public String getAttribute2() {
        return attribute2;
    }

    public void setAttribute2(String attribute2) {
        this.attribute2 = attribute2;
    }

    public String getAttribute3() {
        return attribute3;
    }

    public void setAttribute3(String attribute3) {
        this.attribute3 = attribute3;
    }

    public String getAttribute4() {
        return attribute4;
    }

    public void setAttribute4(String attribute4) {
        this.attribute4 = attribute4;
    }

    public String getAttribute5() {
        return attribute5;
    }

    public void setAttribute5(String attribute5) {
        this.attribute5 = attribute5;
    }

	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	public Long getOrgRrn() {
		return orgRrn;
	}

	public void setOrgRrn(Long orgRrn) {
		this.orgRrn = orgRrn;
	}

}
