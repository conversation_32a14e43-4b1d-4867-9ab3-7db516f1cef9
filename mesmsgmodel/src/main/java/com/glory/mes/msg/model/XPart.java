package com.glory.mes.msg.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Transient;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldDateAdapter;
import com.glory.framework.core.xml.XDynamicComponent;

@XmlRootElement(name = "PART")
@XmlAccessorType(XmlAccessType.NONE)
public class XPart extends XDynamicComponent {
	
	private static final long serialVersionUID = 1L;

	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name="NAME")
	private String name;
	
	@XmlElement(name="DESCRIPTION")
	private String description;
	
	@XmlElement(name="VERSION")
	private Long version;
	
	@XmlElement(name="STATUS")
	private String status;
	
	@XmlElement(name = "ACTIVETIME")
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	private Date activeTime;
	
	@XmlElement(name = "ACTIVEUSER")
	private String activeUser;
	
	@XmlElement(name="STATUSMODELRRN")
	private Long statusModelRrn;
	
	@XmlElement(name="PROCESSNAME")
	private String processName;
	
	@XmlElement(name="PROCESSVERSION")
	private Long processVersion;
	
	@XmlElement(name="CUSTOMERCODE")
	private String customerCode;
	
	@XmlElement(name="NUMBEROFPACK")
	private BigDecimal numberOfPack;
	
	@XmlElement(name="NUMBEROFPALLET")
	private BigDecimal numberOfPallet;
	
	@XmlElement(name="MAINMATERIALTYPE")
	private String mainMaterialType;
	
	@XmlElement(name="MAINMATTYPE")
	private String mainMatType;
	
	@XmlElement(name="SUBMATERIALTYPE")
	private String subMaterialType;
	
	@XmlElement(name="SUBMATTYPE")
	private String subMatType;
	
	@XmlElement(name="BATCHTYPE")
	private String batchType;
	
	@XmlElement(name="LOTSIZE")
	private BigDecimal lotSize;
	
	@XmlElement(name="SUBLOTSIZE")
	private BigDecimal subLotSize;
	
	@XmlElement(name="MATERIALTYPE")
	private String materialType;
	
	@XmlElement(name="UOMID")
	private String uomId;
	
	@XmlElement(name="CATEGORY")
	private String category;
	
	/**
	 * 责任人1
	 */
	@XmlElement(name="OWNER1")
	private String owner1;
	
	/**
	 * 责任人2
	 */
	@XmlElement(name="OWNER2")
	private String owner2;
	
	@XmlElement(name="ISPRODUCTION")
	private String isProduction = "N";
	
	@XmlElement(name="SPEC1")
	private String spec1;
	
	@XmlElement(name="SPEC2")
	private String spec2;
	
	@XmlElement(name="SPEC3")
	private String spec3;
	
	@XmlElement(name="SPEC4")
	private String spec4;
	
	/**
	 * 备注
	 */
	@XmlElement(name="COMMENTS")
	private String comments;
	
	/**
	 * 客户代码 
	 */
	@XmlElement(name="PARTNERCODE")
	private String partnerCode;
	
	@XmlElement(name="RESERVED1")
	private String reserved1;

	@XmlElement(name="RESERVED2")
	private String reserved2;

	@XmlElement(name="RESERVED3")
	private String reserved3;

	@XmlElement(name="RESERVED4")
	private String reserved4;

	@XmlElement(name="RESERVED5")
	private String reserved5;
	
	@XmlElement(name="RESERVED6")
	private String reserved6;

	@XmlElement(name="RESERVED7")
	private String reserved7;
	
	@XmlElement(name="RESERVED8")
	private String reserved8;
	
	@XmlElementWrapper(name="WFPARAMETERLIST")
	@XmlElementRef
	private List<XWFParameter> wfParameterList;
	
	/**
	 * 是否定义为要导入
	 * 及在对应的sheet中存在导入记录
	 */
	@Transient
	private boolean isExistUpload = true;
	
	@Transient
	private String parameterString;
	
	/**
	 * 描述：物料类型，映射为：MATERIALTYPE；
	 * @return materialType
	 */
	public String getMaterialType() {
		return materialType;
	}

	public void setMaterialType(String materialType) {
		this.materialType = materialType;
	}

	public String getUomId() {
		return uomId;
	}

	public void setUomId(String uomId) {
		this.uomId = uomId;
	}

	/**
	 * 描述：产品名称，映射为：NAME；
	 * @return name
	 */
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	/**
	 * 描述：产品描述，映射为：DESCRIPTION；
	 * @return description
	 */
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	/**
	 * 描述：版本号，映射为：VERSION；
	 * @return version
	 */
	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}
	
	public String getId() {
		if (version == null) {
			return name;
		} else {
			return name + "." + version;
		}
	}

	/**
	 * 描述：状态，映射为：STATUS；
	 * @return status
	 */
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Date getActiveTime() {
		return activeTime;
	}

	public void setActiveTime(Date activeTime) {
		this.activeTime = activeTime;
	}

	public String getActiveUser() {
		return activeUser;
	}

	public void setActiveUser(String activeUser) {
		this.activeUser = activeUser;
	}

	public Long getStatusModelRrn() {
		return statusModelRrn;
	}

	public void setStatusModelRrn(Long statusModelRrn) {
		this.statusModelRrn = statusModelRrn;
	}

	/**
	 * 描述：工艺名称，映射为：PROCESSNAME；
	 * @return processName
	 */
	public String getProcessName() {
		return processName;
	}

	public void setProcessName(String processName) {
		this.processName = processName;
	}

	/**
	 * 描述：工艺版本，映射为：PROCESSVERSION；
	 * @return processVersion
	 */
	public Long getProcessVersion() {
		return processVersion;
	}

	public void setProcessVersion(Long processVersion) {
		this.processVersion = processVersion;
	}

	/**
	 * 描述：客户代码，映射为：CUSTOMERCODE；
	 * @return customerCode
	 */
	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	/**
	 * 描述：包装数量，映射为：NUMBEROFPACK；
	 * @return numberOfPack
	 */
	public BigDecimal getNumberOfPack() {
		return numberOfPack;
	}

	public void setNumberOfPack(BigDecimal numberOfPack) {
		this.numberOfPack = numberOfPack;
	}

	/**
	 * 描述：托盘数量，映射为：NUMBEROFPALLET；
	 * @return numberOfPallet
	 */
	public BigDecimal getNumberOfPallet() {
		return numberOfPallet;
	}

	public void setNumberOfPallet(BigDecimal numberOfPallet) {
		this.numberOfPallet = numberOfPallet;
	}

	public String getMainMaterialType() {
		return mainMaterialType;
	}

	public void setMainMaterialType(String mainMaterialType) {
		this.mainMaterialType = mainMaterialType;
	}

	public String getMainMatType() {
		return mainMatType;
	}

	public void setMainMatType(String mainMatType) {
		this.mainMatType = mainMatType;
	}

	public String getSubMatType() {
		return subMatType;
	}

	public void setSubMatType(String subMatType) {
		this.subMatType = subMatType;
	}

	/**
	 * 描述：子产品类型，映射为：SUBMATERIALTYPE；
	 * @return subMaterialType
	 */
	public String getSubMaterialType() {
		return subMaterialType;
	}

	public void setSubMaterialType(String subMaterialType) {
		this.subMaterialType = subMaterialType;
	}

	public String getBatchType() {
		return batchType;
	}

	public void setBatchType(String batchType) {
		this.batchType = batchType;
	}

	/**
	 * 描述：产品批数量大小 ，映射为：LOTSIZE；
	 * @return lotSize
	 */
	public BigDecimal getLotSize() {
		return lotSize;
	}

	public void setLotSize(BigDecimal lotSize) {
		this.lotSize = lotSize;
	}

	/**
	 * 描述：产品子批数量大小，映射为：SUBLOTSIZE；
	 * @return subLotSize
	 */
	public BigDecimal getSubLotSize() {
		return subLotSize;
	}

	public void setSubLotSize(BigDecimal subLotSize) {
		this.subLotSize = subLotSize;
	}

	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}
	
	public Boolean getIsProduction() {
		return "Y".equalsIgnoreCase(this.isProduction) ? true : false;
	}

	public void setIsProduction(Boolean isProduction) {
		this.isProduction = isProduction ? "Y" : "N";
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getOwner1() {
		return owner1;
	}

	public void setOwner1(String owner1) {
		this.owner1 = owner1;
	}

	public String getOwner2() {
		return owner2;
	}

	public void setOwner2(String owner2) {
		this.owner2 = owner2;
	}

	public String getSpec1() {
		return spec1;
	}

	public void setSpec1(String spec1) {
		this.spec1 = spec1;
	}

	public String getSpec2() {
		return spec2;
	}

	public void setSpec2(String spec2) {
		this.spec2 = spec2;
	}

	public String getSpec3() {
		return spec3;
	}

	public void setSpec3(String spec3) {
		this.spec3 = spec3;
	}

	public String getSpec4() {
		return spec4;
	}

	public void setSpec4(String spec4) {
		this.spec4 = spec4;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}
	
	public String getPartnerCode() {
		return partnerCode;
	}

	public void setPartnerCode(String partnerCode) {
		this.partnerCode = partnerCode;
	}

	public String getReserved1() {
		return reserved1;
	}

	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}

	public String getReserved2() {
		return reserved2;
	}

	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}

	public String getReserved3() {
		return reserved3;
	}

	public void setReserved3(String reserved3) {
		this.reserved3 = reserved3;
	}

	public String getReserved4() {
		return reserved4;
	}

	public void setReserved4(String reserved4) {
		this.reserved4 = reserved4;
	}

	public String getReserved5() {
		return reserved5;
	}

	public void setReserved5(String reserved5) {
		this.reserved5 = reserved5;
	}

	public String getReserved6() {
		return reserved6;
	}

	public void setReserved6(String reserved6) {
		this.reserved6 = reserved6;
	}

	public String getReserved7() {
		return reserved7;
	}

	public void setReserved7(String reserved7) {
		this.reserved7 = reserved7;
	}

	public String getReserved8() {
		return reserved8;
	}

	public void setReserved8(String reserved8) {
		this.reserved8 = reserved8;
	}

	public List<XWFParameter> getWfParameterList() {
		return wfParameterList;
	}

	public void setWfParameterList(List<XWFParameter> wfParameterList) {
		this.wfParameterList = wfParameterList;
	}
	
	public boolean isExistUpload() {
		return isExistUpload;
	}

	public void setExistUpload(boolean isExistUpload) {
		this.isExistUpload = isExistUpload;
	}

	public String getParameterString() {
		return parameterString;
	}

	public void setParameterString(String parameterString) {
		this.parameterString = parameterString;
	}
}
