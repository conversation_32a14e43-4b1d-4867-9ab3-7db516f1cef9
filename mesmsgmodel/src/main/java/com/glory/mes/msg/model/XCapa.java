package com.glory.mes.msg.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "CAPA")
@XmlAccessorType(XmlAccessType.NONE)
public class XCapa extends XBase {
	
	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name="NAME")
	private String name;
	
	@XmlElement(name="DESCRIPTION")
	private String description;

	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}
