package com.glory.mes.msg.model;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;

import org.apache.commons.lang.ObjectUtils;

import com.glory.framework.core.util.StringUtil;
import com.glory.framework.core.xml.XDynamicComponent;
import com.google.common.collect.Lists;

@XmlRootElement(name = "PRDSTEP")
@XmlAccessorType(XmlAccessType.NONE)
public class XPrdStep extends XDynamicComponent {
	
	private static final long serialVersionUID = 1L;

	@XmlElement(name = "PARTNAME")
	private String partName;
	
	@XmlElement(name = "PARTVERSION")
	private Long partVersion;
	
	@XmlElement(name = "PROCESSNAME")
	private String processName;
	
	@XmlElement(name = "PROCESSVERSION")
	private Long processVersion;
	
	@XmlElement(name = "PROCESSSTATEVERSION")
	private Long processStateVersion;

	/**
	 * Process和最底层Procedure之间的中间路径
	 * 在正常条件下及正常的Prcocess/Procedure/Step时,值为null
	 * 在返工(包括多重返工)等条件下有值
	 */
	@XmlElement(name = "PROCEDUREIDPATH")
	private String procedureIdPath;
	
	/**
	 * Step的直接上层Procedure
	 */
	@XmlElement(name = "PROCEDURENAME")
	private String procedureName;
	
	@XmlElement(name = "PROCEDUREVERSION")
	private Long procedureVersion;
	
	@XmlElement(name = "PROCEDURESTATEVERSION")
	private Long procedureStateVersion;
	
	@XmlElement(name = "STEPNAME")
	private String stepName;
	
	@XmlElement(name = "STEPVERSION")
	private Long stepVersion;
	
	@XmlElement(name = "STEPSTATEVERSION")
	private Long stepStateVersion;
	
	@XmlElement(name="STEPDESC")
	private String stepDesc;

	@XmlElement(name="RECIPENAME")
	private String recipeName;

	@XmlElement(name="MASKNAME")
	private String maskName;

	@XmlElement(name="EDCSETNAME")
	private String edcSetName;
	
	@XmlElement(name="CAPABILITY")
	private Long capability;
	
	@XmlElement(name = "CAPABILITYNAME")
	private String capabilityName;
	
	@XmlElement(name="STAGEID")
	private String stageId;
	
	@XmlElement(name="CONTAMINATIONLEVEL")
	private String contaminationLevel;
	
	@XmlElement(name="OWNER")
	private String owner;
	
	@XmlElement(name="NOSKIP")
	private String noSkip;
	
	@XmlElement(name="ISREQUIREEQP")
	private String isRequireEqp;

	@XmlElement(name="ISBATCH")
	private String isBatch;
	
	@XmlElement(name="KEEPBATCH")
	private String keepBatch;
	
	@XmlElement(name="ISCLEARUSECOUNT")
	private String isClearUseCount;
	
	@XmlElement(name="ISUSECOUNT")
	private String isUseCount;
	
	@XmlElement(name="ISMOVENEXT")
	private String isMoveNext;
	
	@XmlElement(name="ISALLOWREPEAT")
	private String isAllowRepeat;
	
	@XmlElement(name="ISUSEPROCESSSTATE")
	private String isUseProcessState;
	
	@XmlElement(name="ISUPDATELOTATTRIBUTE")
	private String isUpdateLotAttribute;
	
	@XmlElement(name="LOTLOWYIELDHOLD")
	private String lotLowYieldHold;
	
	@XmlElement(name="LOTLOWYIELDHOLDBYSTAGE")
	private String lotLowYieldHoldByStage;
	
	@XmlElement(name="WOLOWYIELDHOLD")
	private String woLowYieldHold;
	
	@XmlElement(name="ISALLOWHOLDTRACKOUT")
	private String isAllowHoldTrackOut;
	
	@XmlElement(name="ISUSEFLOWCONDITION")
	private String isUseFlowCondition;
	
	@XmlElement(name="ISUSESUBEQP")
	private String isUseSubEqp;
	
	@XmlElement(name="ISUSECAPARANGE")
	private String isUseCapaRange;
	
	@XmlElement(name="N2STOCK")
	private String n2Stock;
	
	@XmlElement(name="ISMULTIEQP")
	private String isMultiEqp;
	
	@XmlElement(name="ISRESERVEEQP")
	private String isReserveEqp;
	
	@XmlElement(name = "USECATEGORY")
	private String useCategory;
	
	@XmlElement(name="HOLDCODESRC")
	private String holdCodeSrc;
	
	@XmlElement(name="RELEASECODESRC")
	private String releaseCodeSrc;
	
	@XmlElement(name="SCRAPCODESRC")
	private String scrapCodeSrc;
	
	@XmlElement(name="BONUSCODESRC")
	private String bonusCodeSrc;
	
	@XmlElement(name="REWORKCODESRC")
	private String reworkCodeSrc;
	
	@XmlElement(name="DEFECTCODESRC")
	private String defectCodeSrc;
	
	@XmlElement(name = "TRACKINWIZARD")
	private String trackInWizard;
	
	@XmlElement(name = "ABORTWIZARD")
	private String abortWizard;

	@XmlElement(name = "TRACKOUTWIZARD")
	private String trackOutWizard;
	
	@XmlElement(name = "MOVENEXTWIZARD")
	private String moveNextWizard;
	
	@XmlElement(name = "INMAINMATTYPE")
	private String inMainMatType;
	
	@XmlElement(name = "OUTMAINMATTYPE")
	private String outMainMatType;
	
	@XmlElementWrapper(name="WFPARAMETERLIST")
	@XmlElementRef
	private List<XWFParameter> wfParameterList;
	
	/**
	 * 用于记录产品流程时,是否存在相同的工步
	 */
	private boolean isRepeatInFlow = false;
	
	private String reworkFlow;
	
	private String stepAttributeString;
	
	private String parameterString;
	
	public String getPartName() {
		return partName;
	}

	public void setPartName(String partName) {
		this.partName = partName;
	}

	public Long getPartVersion() {
		return partVersion;
	}

	public void setPartVersion(Long partVersion) {
		this.partVersion = partVersion;
	}
	
	public String getPartId() {
		return partName + "." + partVersion;
	}

	public String getProcessName() {
		return processName;
	}

	public void setProcessName(String processName) {
		this.processName = processName;
	}

	public Long getProcessVersion() {
		return processVersion;
	}

	public void setProcessVersion(Long processVersion) {
		this.processVersion = processVersion;
	}

	public String getProcessId() {
		if (processVersion == null) {
			return processName;
		} else {
			return processName + "." + processVersion;
		}
	}
	
	public String getProcedureIdPath() {
		return procedureIdPath;
	}

	public void setProcedureIdPath(String procedureIdPath) {
		this.procedureIdPath = procedureIdPath;
	}

	public String getProcedureName() {
		return procedureName;
	}

	public void setProcedureName(String procedureName) {
		this.procedureName = procedureName;
	}

	public Long getProcedureVersion() {
		return procedureVersion;
	}

	public void setProcedureVersion(Long procedureVersion) {
		this.procedureVersion = procedureVersion;
	}

	public String getProcedureId() {
		if (procedureVersion == null) {
			return procedureName;
		} else {
			return procedureName + "." + procedureVersion;
		}
	}
	
	public String getStepName() {
		return stepName;
	}

	public void setStepName(String stepName) {
		this.stepName = stepName;
	}

	public Long getStepVersion() {
		return stepVersion;
	}

	public void setStepVersion(Long stepVersion) {
		this.stepVersion = stepVersion;
	}

	public String getStepId() {
		if (stepVersion == null) {
			return stepName;
		} else {
			return stepName + "." + stepVersion;
		}
	}

	public List<XWFParameter> getWfParameterList() {
		return wfParameterList;
	}

	public void setWfParameterList(List<XWFParameter> wfParameterList) {
		this.wfParameterList = wfParameterList;
	}
	
	public Long getProcessStateVersion() {
		return processStateVersion;
	}

	public void setProcessStateVersion(Long processStateVersion) {
		this.processStateVersion = processStateVersion;
	}

	public Long getProcedureStateVersion() {
		return procedureStateVersion;
	}

	public void setProcedureStateVersion(Long procedureStateVersion) {
		this.procedureStateVersion = procedureStateVersion;
	}

	public Long getStepStateVersion() {
		return stepStateVersion;
	}

	public void setStepStateVersion(Long stepStateVersion) {
		this.stepStateVersion = stepStateVersion;
	}
	
	public String getRecipeName() {
		return recipeName;
	}

	public void setRecipeName(String recipeName) {
		this.recipeName = recipeName;
	}

	public String getMaskName() {
		return maskName;
	}

	public void setMaskName(String maskName) {
		this.maskName = maskName;
	}

	public String getEdcSetName() {
		return edcSetName;
	}

	public void setEdcSetName(String edcSetName) {
		this.edcSetName = edcSetName;
	}
	
	public boolean isRepeatInFlow() {
		return isRepeatInFlow;
	}

	public void setRepeatInFlow(boolean isRepeatInFlow) {
		this.isRepeatInFlow = isRepeatInFlow;
	}

	public String getStepDesc() {
		return stepDesc;
	}

	public void setStepDesc(String stepDesc) {
		this.stepDesc = stepDesc;
	}

	public Long getCapability() {
		return capability;
	}

	public void setCapability(Long capability) {
		this.capability = capability;
	}

	public String getCapabilityName() {
		return capabilityName;
	}

	public void setCapabilityName(String capabilityName) {
		this.capabilityName = capabilityName;
	}

	public String getStageId() {
		return stageId;
	}

	public void setStageId(String stageId) {
		this.stageId = stageId;
	}
	
	public String getContaminationLevel() {
		return contaminationLevel;
	}

	public void setContaminationLevel(String contaminationLevel) {
		this.contaminationLevel = contaminationLevel;
	}

	public String getOwner() {
		return owner;
	}

	public void setOwner(String owner) {
		this.owner = owner;
	}

	public Boolean getNoSkip() {
		return "Y".equalsIgnoreCase(this.noSkip) ? true : false;
	}

	public void setNoSkip(Boolean noSkip) {
		this.noSkip = noSkip ? "Y" : "N";
	}

	public void setIsRequireEqp(Boolean isRequireEqp) {
		this.isRequireEqp = isRequireEqp ? "Y" : "N";
	}
	public Boolean getIsRequireEqp(){
		return "Y".equalsIgnoreCase(this.isRequireEqp) ? true : false; 
	}

	public void setIsBatch(Boolean isBatch) {
		this.isBatch = isBatch ? "Y" : "N";
	}

	public Boolean getIsBatch() {
		return "Y".equalsIgnoreCase(this.isBatch) ? true : false; 
	}

	public Boolean getKeepBatch() {
		return "Y".equalsIgnoreCase(this.keepBatch) ? true : false;
	}

	public void setKeepBatch(Boolean keepBatch) {
		this.keepBatch = keepBatch ? "Y" : "N";
	}

	public Boolean getIsClearUseCount() {
		return "Y".equalsIgnoreCase(this.isClearUseCount) ? true : false;
	}

	public void setIsClearUseCount(Boolean isClearUseCount) {
		this.isClearUseCount = isClearUseCount ? "Y" : "N";
	}

	public Boolean getIsUseCount() {
		return "Y".equalsIgnoreCase(this.isUseCount) ? true : false;
	}

	public void setIsUseCount(Boolean isUseCount) {
		this.isUseCount = isUseCount ? "Y" : "N";
	}

	public Boolean getIsMoveNext(){
		return "Y".equalsIgnoreCase(this.isMoveNext) ? true : false; 
	}
	
	public void setIsMoveNext(Boolean isMoveNext) {
		this.isMoveNext = isMoveNext ? "Y" : "N";
	}

	public Boolean getIsAllowRepeat() {
		return "Y".equalsIgnoreCase(this.isAllowRepeat) ? true : false;
	}

	public void setIsAllowRepeat(Boolean isAllowRepeat) {
		this.isAllowRepeat = isAllowRepeat ? "Y" : "N";
	}

	public Boolean getIsUseProcessState() {
		return "Y".equalsIgnoreCase(this.isUseProcessState) ? true : false;
	}

	public void setIsUseProcessState(Boolean isUseProcessState) {
		this.isUseProcessState = isUseProcessState ? "Y" : "N";
	}
	
	public Boolean getIsUpdateLotAttribute() {
		return "Y".equalsIgnoreCase(this.isUpdateLotAttribute) ? true : false;
	}

	public void setIsUpdateLotAttribute(Boolean isUpdateLotAttribute) {
		this.isUpdateLotAttribute = isUpdateLotAttribute ? "Y" : "N";
	}
	
	public Boolean getLotLowYieldHold() {
		return "Y".equalsIgnoreCase(this.lotLowYieldHold) ? true : false;
	}

	public void setLotLowYieldHold(Boolean lotLowYieldHold) {
		this.lotLowYieldHold = lotLowYieldHold ? "Y" : "N";
	}

	public Boolean getLotLowYieldHoldByStage() {
		return "Y".equalsIgnoreCase(this.lotLowYieldHoldByStage) ? true : false;
	}

	public void setLotLowYieldHoldByStage(Boolean lotLowYieldHoldByStage) {
		this.lotLowYieldHoldByStage = lotLowYieldHoldByStage ? "Y" : "N";
	}

	public Boolean getWoLowYieldHold() {
		return "Y".equalsIgnoreCase(this.woLowYieldHold) ? true : false;
	}

	public void setWoLowYieldHold(Boolean woLowYieldHold) {
		this.woLowYieldHold = woLowYieldHold ? "Y" : "N";
	}
	
	public Boolean getIsAllowHoldTrackOut() {
		return "Y".equalsIgnoreCase(this.isAllowHoldTrackOut) ? true : false;
	}

	public void setIsAllowHoldTrackOut(Boolean isAllowHoldTrackOut) {
		this.isAllowHoldTrackOut = isAllowHoldTrackOut ? "Y" : "N";
	}
	
	public Boolean getIsUseFlowCondition() {
		return "Y".equalsIgnoreCase(this.isUseFlowCondition) ? true : false;
	}

	public void setIsUseFlowCondition(Boolean isUseFlowCondition) {
		this.isUseFlowCondition = isUseFlowCondition ? "Y" : "N";
	}
	
	public Boolean getIsUseSubEqp() {
		return "Y".equalsIgnoreCase(this.isUseSubEqp) ? true : false;
	}

	public void setIsUseSubEqp(Boolean isUseSubEqp) {
		this.isUseSubEqp = isUseSubEqp ? "Y" : "N";
	}
	
	public Boolean getIsUseCapaRange() {
		return "Y".equalsIgnoreCase(this.isUseCapaRange) ? true : false;
	}

	public void setIsUseCapaRange(Boolean isUseCapaRange) {
		this.isUseCapaRange = isUseCapaRange ? "Y" : "N";
	}
	
	public Boolean getN2Stock() {
		return "Y".equalsIgnoreCase(this.n2Stock) ? true : false;
	}

	public void setN2Stock(Boolean n2Stock) {
		this.n2Stock = n2Stock ? "Y" : "N";
	}
	
	public Boolean getIsMultiEqp(){
		return "Y".equalsIgnoreCase(this.isMultiEqp) ? true : false; 
	}
	
	public void setIsMultiEqp(Boolean isMultiEqp) {
		this.isMultiEqp = isMultiEqp ? "Y" : "N";
	}
	
	public Boolean getIsReserveEqp() {
		return "Y".equalsIgnoreCase(this.isReserveEqp) ? true : false; 
	}
	
	public void setIsReserveEqp(Boolean isReserveEqp) {
		this.isReserveEqp = isReserveEqp ? "Y" : "N";
	}
	
	public String getUseCategory() {
		return useCategory;
	}

	public void setUseCategory(String useCategory) {
		this.useCategory = useCategory;
	}

	public String getHoldCodeSrc() {
		return holdCodeSrc;
	}

	public void setHoldCodeSrc(String holdCodeSrc) {
		this.holdCodeSrc = holdCodeSrc;
	}

	public String getReleaseCodeSrc() {
		return releaseCodeSrc;
	}

	public void setReleaseCodeSrc(String releaseCodeSrc) {
		this.releaseCodeSrc = releaseCodeSrc;
	}

	public String getScrapCodeSrc() {
		return scrapCodeSrc;
	}

	public void setScrapCodeSrc(String scrapCodeSrc) {
		this.scrapCodeSrc = scrapCodeSrc;
	}

	public String getBonusCodeSrc() {
		return bonusCodeSrc;
	}

	public void setBonusCodeSrc(String bonusCodeSrc) {
		this.bonusCodeSrc = bonusCodeSrc;
	}

	public String getReworkCodeSrc() {
		return reworkCodeSrc;
	}

	public void setReworkCodeSrc(String reworkCodeSrc) {
		this.reworkCodeSrc = reworkCodeSrc;
	}

	public String getDefectCodeSrc() {
		return defectCodeSrc;
	}

	public void setDefectCodeSrc(String defectCodeSrc) {
		this.defectCodeSrc = defectCodeSrc;
	}

	public String getTrackInWizard() {
		return trackInWizard;
	}

	public void setTrackInWizard(String trackInWizard) {
		this.trackInWizard = trackInWizard;
	}

	public String getAbortWizard() {
		return abortWizard;
	}

	public void setAbortWizard(String abortWizard) {
		this.abortWizard = abortWizard;
	}

	public String getTrackOutWizard() {
		return trackOutWizard;
	}

	public void setTrackOutWizard(String trackOutWizard) {
		this.trackOutWizard = trackOutWizard;
	}

	public String getMoveNextWizard() {
		return moveNextWizard;
	}

	public void setMoveNextWizard(String moveNextWizard) {
		this.moveNextWizard = moveNextWizard;
	}

	public String getInMainMatType() {
		return inMainMatType;
	}

	public void setInMainMatType(String inMainMatType) {
		this.inMainMatType = inMainMatType;
	}

	public String getOutMainMatType() {
		return outMainMatType;
	}

	public void setOutMainMatType(String outMainMatType) {
		this.outMainMatType = outMainMatType;
	}
	
	public String getReworkFlow() {
		return reworkFlow;
	}

	public void setReworkFlow(String reworkFlow) {
		this.reworkFlow = reworkFlow;
	}

	public String getStepAttributeString() {
		return stepAttributeString;
	}

	public void setStepAttributeString(String stepAttributeString) {
		this.stepAttributeString = stepAttributeString;
	}

	public String getParameterString() {
		return parameterString;
	}

	public void setParameterString(String parameterString) {
		this.parameterString = parameterString;
	}

	public boolean isSameEdcSet(String otherEdcSetName) {
		if (ObjectUtils.equals(edcSetName, otherEdcSetName)) {
			return true;
		}
		if (StringUtil.isEmpty(edcSetName) && StringUtil.isEmpty(otherEdcSetName)) {
			return true;
		}
		if (StringUtil.isEmpty(edcSetName) && !StringUtil.isEmpty(otherEdcSetName)) {
			return false;
		}
		if (!StringUtil.isEmpty(otherEdcSetName)) {
			List<String> otherNames = Lists.newArrayList();
			if (otherEdcSetName.indexOf(";") == -1) {
				otherNames.add(otherEdcSetName);
			} else {
				otherNames.addAll(Lists.newArrayList(otherEdcSetName.split(";")));
			}
			
			List<String> names = Lists.newArrayList();
			names.addAll(Lists.newArrayList(edcSetName.split(";")));
			if (names.size() != otherNames.size()) {
				return false;
			}
			
			if (otherNames.containsAll(names)) {
				return true;
			}			
		}
		return false;
	}
}
