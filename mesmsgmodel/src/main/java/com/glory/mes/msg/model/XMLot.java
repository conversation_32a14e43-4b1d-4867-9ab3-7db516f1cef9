package com.glory.mes.msg.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldDateAdapter;
import com.glory.framework.core.xml.XDynamicComponent;

@XmlRootElement(name = "MLOT")
@XmlAccessorType(XmlAccessType.NONE)
public class XMLot extends XDynamicComponent {
	
	private static final long serialVersionUID = 1L;

	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name="ORGRRN")
	private Long orgRrn;
	
	@XmlElement(name="ORGNAME")
	private String orgName;
	
	@XmlElement(name="MLOTID")
	private String mLotId;
	
	@XmlElement(name="MLOTTYPE")
	private String mLotType;
	
	@XmlElement(name="MAINMATTYPE")
	private String mainMatType;
	
	@XmlElement(name="SUBMATTYPE")
	private String subMatType;
	
	@XmlElement(name="MAINQTY")
	private BigDecimal mainQty = BigDecimal.ZERO;
	
	@XmlElement(name="SUBQTY")
	private BigDecimal subQty;
	
	@XmlElement(name="MOID")
	private String moId;
	
	@XmlElement(name="STATE")
	private String state;
	
	@XmlElement(name="HOLDSTATE")
	private String holdState;
	
	@XmlElement(name="MATERIALRRN")
	private Long materialRrn;
	
	@XmlElement(name="MATERIALNAME")
	private String materialName;
	
	@XmlElement(name="MATERIALVERSION")
	private Long materialVersion;
	
	@XmlElement(name="MATERIALDESC")
	private String materialDesc;
	
	@XmlElement(name="MATERIALTYPE")
	private String materialType;
	
	@XmlElement(name="PARTNERCODE")
	private String partnerCode;
	
	@XmlElement(name="PARTNERORDER")
	private String partnerOrder;
	
	@XmlElement(name="PARTNERMATERIALID")
	private String partnerMaterialId;
	
	@XmlElement(name="PARTNERLOTID")
	private String partnerLotId;
	
	@XmlElement(name="TRANSWAREHOUSERRN")
	private Long transWarehouseRrn;
	
	@XmlElement(name="TRANSWAREHOUSEID")
	private String transWarehouseId;
	
	@XmlElement(name="TRANSSTORAGETYPE")
	private String transStorageType;
	
	@XmlElement(name="TRANSSTORAGEID")
	private String transStorageId;
	
	@XmlElement(name="TRANSTARGETWAREHOUSERRN")
	private Long transTargetWarehouseRrn;
	
	@XmlElement(name="TRANSTARGETWAREHOUSEID")
	private String transTargetWarehouseId;
	
	@XmlElement(name="TRANSTARGETSTORAGETYPE")
	private String transTargetStorageType;
	
	@XmlElement(name="TRANSTARGETSTORAGEID")
	private String transTargetStorageId;
	
	@XmlElement(name="TRANSMAINQTY")
	private BigDecimal transMainQty;

	@XmlElement(name="TRANSSUBQTY")
	private BigDecimal transSubQty;
	
	@XmlElement(name="POSITION")
	private String position;
	
    @XmlElement(name = "ROOTMLOTID")
    private String rootMLotId;
    
    @XmlElement(name="GRADE1")
    private String grade1;
    
    @XmlElement(name="GRADE2")
    private String grade2;
    
    @XmlElement(name="SINGLEMLOTID")
    private String singleMLotId;
    
    @XmlElement(name="USEQTY")
    private BigDecimal useQty = BigDecimal.ZERO;
    
    @XmlElement(name="EQUIPMENTID")
    private String equipmentId;
    
    @XmlElement(name="UNITID")
    private String unitId;
	
	/**
	 * 是否时间敏感物料
	 */
    @XmlElement(name="ISTIMESENSITIVE")
	private String isTimeSensitive = "N";

	/**
	 * 保质期警告时间
	 */
    @XmlJavaTypeAdapter(FieldDateAdapter.class)
    @XmlElement(name="SHELFWARNINGEXPIRE")
	private Date shelfWarningExpire;	

	/**
	 * 保质期失效时间
	 */
    @XmlJavaTypeAdapter(FieldDateAdapter.class)
    @XmlElement(name="SHELFLIFEEXPIRE")
	private Date shelfLifeExpire;	

	/**
	 * 使用有效期失效时间
	 */
    @XmlJavaTypeAdapter(FieldDateAdapter.class)
    @XmlElement(name="FLOORLIFEEXPIRE")
	private Date floorLifeExpire;
    
    @XmlJavaTypeAdapter(FieldDateAdapter.class)
    @XmlElement(name="CREATED")
    private Date created;
    
    @XmlElement(name="OWNER")
    private String owner;
    
    @XmlElement(name="LOTCOMMENT")
    private String lotComment;
    
    @XmlElement(name="UOMID")
    private String uomId;
    
    @XmlElement(name="RESERVED1")
    private String reserved1;
    
    @XmlElement(name="RESERVED2")
    private String reserved2;
    
    @XmlElement(name="RESERVED3")
    private String reserved3;
    
    @XmlElement(name="RESERVED4")
    private String reserved4;
    
    @XmlElement(name="RESERVED5")
    private String reserved5;
    
    @XmlElement(name="RESERVED6")
    private String reserved6;
    
    @XmlElement(name="RESERVED7")
    private String reserved7;
    
    @XmlElement(name="RESERVED8")
    private String reserved8;
    
    @XmlElement(name="DURABLE")
    private String durable;
    
    @XmlElement(name="BATCHTYPE")
    private String batchType;
    
    @XmlElement(name="MCOMPONENTUNITLIST")
	private List<XMComponentUnit> subMComponentUnit;
    
    @XmlElement(name="CURRENTCOUNT")
    private Long currentCount;
    
    @XmlElement(name="MAXQTIME")
    private Long maxQTime;
    
    @XmlElement(name="ACTIONCODE")
    private String actionCode;
    
    @XmlElement(name="ACTIONREASON")
    private String actionReason;
    
    @XmlElement(name="ACTIONCOMMENT")
    private String actionComment;
    
    @XmlElement(name="ATTRIBUTE1")
    private String attribute1;
    
   
    @XmlElement(name="UDF")
    private Map udf;
    
	public Map getUdf() {
		return udf;
	}

	public void setUdf(Map udf) {
		this.udf = udf;
	}

	/**
	 * 描述： 物料单位，映射为：UOMID；
	 * @return uomId
	 */
	public String getUomId() {
		return uomId;
	}

	public void setUomId(String uomId) {
		this.uomId = uomId;
	}

	/**
	 * 描述：备注 ，映射为：LOTCOMMENT；
	 * @return lotComment
	 */
	public String getLotComment() {
		return lotComment;
	}

	public void setLotComment(String lotComment) {
		this.lotComment = lotComment;
	}

	/**
	 * 描述： 创建时间，映射为：CREATED；
	 * @return created
	 */
	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	/**
	 * 描述：位置 ，映射为：POSITION；
	 * @return position
	 */
	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	/**
	 * 描述：主数量，映射为：MAINQTY；
	 * @return mainQty
	 */
	public BigDecimal getMainQty() {
		return mainQty;
	}

	public void setMainQty(BigDecimal mainQty) {
		this.mainQty = mainQty;
	}

	/**
	 * 描述：子数量 ，映射为：SUBQTY；
	 * @return subQty
	 */
	public BigDecimal getSubQty() {
		return subQty;
	}

	public void setSubQty(BigDecimal subQty) {
		this.subQty = subQty;
	}

	/**
	 * 描述： 状态，映射为：STATE；
	 * @return state
	 */
	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	/**
	 * 描述： 工单ID，映射为：MOID；
	 * @return moId
	 */
	public String getMoId() {
		return moId;
	}

	public void setMoId(String moId) {
		this.moId = moId;
	}

	/**
	 * 描述： 物料key，映射为：MATERIALRRN；
	 * @return materialRrn
	 */
	public Long getMaterialRrn() {
		return materialRrn;
	}

	public void setMaterialRrn(Long materialRrn) {
		this.materialRrn = materialRrn;
	}

	/**
	 * 描述： 物料名称，映射为：MATERIALNAME；
	 * @return materialName
	 */
	public String getMaterialName() {
		return materialName;
	}

	public void setMaterialName(String materialName) {
		this.materialName = materialName;
	}

	/**
	 * 描述： 物料版本，映射为：MATERIALVERSION；
	 * @return materialVersion
	 */
	public Long getMaterialVersion() {
		return materialVersion;
	}

	public void setMaterialVersion(Long materialVersion) {
		this.materialVersion = materialVersion;
	}

	/**
	 * 描述： 物料类型，映射为：MATERIALTYPE；
	 * @return materialType
	 */
	public String getMaterialType() {
		return materialType;
	}

	public void setMaterialType(String materialType) {
		this.materialType = materialType;
	}

	/**
	 * 描述： 物料描述，映射为：MATERIALDESC；
	 * @return materialDesc
	 */
	public String getMaterialDesc() {
		return materialDesc;
	}

	public void setMaterialDesc(String materialDesc) {
		this.materialDesc = materialDesc;
	}

	/**
	 * 描述：供应商代码 ，映射为：PARTNERCODE；
	 * @return partnerCode
	 */
	public String getPartnerCode() {
		return partnerCode;
	}

	public void setPartnerCode(String partnerCode) {
		this.partnerCode = partnerCode;
	}

	/**
	 * 描述： 供应商订单，映射为：PARTNERORDER；
	 * @return partnerOrder
	 */
	public String getPartnerOrder() {
		return partnerOrder;
	}

	public void setPartnerOrder(String partnerOrder) {
		this.partnerOrder = partnerOrder;
	}

	/**
	 * 描述： 供应商物料料号，映射为：PARTNERMATERIALID；
	 * @return partnerMaterialId
	 */
	public String getPartnerMaterialId() {
		return partnerMaterialId;
	}

	public void setPartnerMaterialId(String partnerMaterialId) {
		this.partnerMaterialId = partnerMaterialId;
	}
	
	/**
	 * 描述： 供应商批号，映射为：PARTNERLOTID；
	 * @return partnerLotId
	 */
	public String getPartnerLotId() {
		return partnerLotId;
	}

	public void setPartnerLotId(String partnerLotId) {
		this.partnerLotId = partnerLotId;
	}

	/**
	 * 描述： 目标仓库key，映射为：TRANSWAREHOUSERRN；
	 * @return transWarehouseRrn
	 */
	public Long getTransWarehouseRrn() {
		return transWarehouseRrn;
	}

	public void setTransWarehouseRrn(Long transWarehouseRrn) {
		this.transWarehouseRrn = transWarehouseRrn;
	}

	/**
	 * 描述： 仓库号，映射为：TRANSWAREHOUSEID；
	 * @return transWarehouseId
	 */
	public String getTransWarehouseId() {
		return transWarehouseId;
	}

	public void setTransWarehouseId(String transWarehouseId) {
		this.transWarehouseId = transWarehouseId;
	}

	/**
	 * 描述： 位置类型，映射为：TRANSSTORAGETYPE；
	 * @return transStorageType
	 */
	public String getTransStorageType() {
		return transStorageType;
	}

	public void setTransStorageType(String transStorageType) {
		this.transStorageType = transStorageType;
	}

	/**
	 * 描述： 位置号，映射为：TRANSSTORAGEID；
	 * @return transStorageId
	 */
	public String getTransStorageId() {
		return transStorageId;
	}

	public void setTransStorageId(String transStorageId) {
		this.transStorageId = transStorageId;
	}

	/**
	 * 描述： 目标仓库key，映射为：TRANSTARGETWAREHOUSERRN；
	 * @return transTargetWarehouseRrn
	 */
	public Long getTransTargetWarehouseRrn() {
		return transTargetWarehouseRrn;
	}

	public void setTransTargetWarehouseRrn(Long transTargetWarehouseRrn) {
		this.transTargetWarehouseRrn = transTargetWarehouseRrn;
	}

	/**
	 * 描述：目标仓库号 ，映射为：TRANSTARGETWAREHOUSEID；
	 * @return transTargetWarehouseId
	 */
	public String getTransTargetWarehouseId() {
		return transTargetWarehouseId;
	}

	public void setTransTargetWarehouseId(String transTargetWarehouseId) {
		this.transTargetWarehouseId = transTargetWarehouseId;
	}

	/**
	 * 描述：目标区域类型 ，映射为：TRANSTARGETSTORAGETYPE；
	 * @return transTargetStorageType
	 */
	public String getTransTargetStorageType() {
		return transTargetStorageType;
	}

	public void setTransTargetStorageType(String transTargetStorageType) {
		this.transTargetStorageType = transTargetStorageType;
	}

	/**
	 * 描述：目标位置号 ，映射为：TRANSTARGETSTORAGEID；
	 * @return transTargetStorageId
	 */
	public String getTransTargetStorageId() {
		return transTargetStorageId;
	}

	public void setTransTargetStorageId(String transTargetStorageId) {
		this.transTargetStorageId = transTargetStorageId;
	}

	/**
	 * 描述： 批次事务数量，映射为：TRANSMAINQTY；
	 * @return transMainQty
	 */
	public BigDecimal getTransMainQty() {
		return transMainQty;
	}

	public void setTransMainQty(BigDecimal transMainQty) {
		this.transMainQty = transMainQty;
	}

	/**
	 * 描述： 批次事务数量，映射为：TRANSMAINQTY；
	 * @return transMainQty
	 */
	public BigDecimal getTransSubQty() {
		return transSubQty;
	}

	public void setTransSubQty(BigDecimal transSubQty) {
		this.transSubQty = transSubQty;
	}
	
	/**
	 * 描述：根批号 ，映射为：ROOTMLOTID；
	 * @return rootMLotId
	 */
	public String getRootMLotId() {
		return rootMLotId;
	}

	public void setRootMLotId(String rootMLotId) {
		this.rootMLotId = rootMLotId;
	}

	/**
	 * 描述： 型号1，映射为：GRADE1；
	 * @return grade1
	 */
	public String getGrade1() {
		return grade1;
	}

	public void setGrade1(String grade1) {
		this.grade1 = grade1;
	}

	/**
	 * 描述：型号2，映射为：GRADE2；
	 * @return grade2
	 */
	public String getGrade2() {
		return grade2;
	}

	public void setGrade2(String grade2) {
		this.grade2 = grade2;
	}

	/**
	 * 描述： ，映射为：SINGLEMLOTID；
	 * @return singleMLotId
	 */
	public String getSingleMLotId() {
		return singleMLotId;
	}

	public void setSingleMLotId(String singleMLotId) {
		this.singleMLotId = singleMLotId;
	}

	/**
	 * 描述： ，映射为：USEQTY；
	 * @return useQty
	 */
	public BigDecimal getUseQty() {
		return useQty;
	}

	public void setUseQty(BigDecimal useQty) {
		this.useQty = useQty;
	}
	
	/**
	 * 描述： 暂停状态，映射为：HOLDSTATE；
	 * @return holdState
	 */
	public String getHoldState() {
		return holdState;
	}

	public void setHoldState(String holdState) {
		this.holdState = holdState;
	}

	/**
	 * 描述： 物料批号，映射为：MLOTID；
	 * @return mLotId
	 */
	public String getmLotId() {
		return mLotId;
	}

	public void setmLotId(String mLotId) {
		this.mLotId = mLotId;
	}

	/**
	 * 描述： 物料批类型，映射为：MLOTTYPE；
	 * @return mLotType
	 */
	public String getmLotType() {
		return mLotType;
	}

	public void setmLotType(String mLotType) {
		this.mLotType = mLotType;
	}

	/**
	 * 描述： 设备号，映射为：EQUIPMENTID；
	 * @return equipmentId
	 */
	public String getEquipmentId() {
		return equipmentId;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	/**
	 * 描述： Unit Id，映射为：UNITID；
	 * @return unitId
	 */
	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}

	/**
	 * 描述： 批次包装类型，映射为：MAINMATTYPE；
	 * @return mainMatType
	 */
	public String getMainMatType() {
		return mainMatType;
	}

	public void setMainMatType(String mainMatType) {
		this.mainMatType = mainMatType;
	}

	/**
	 * 描述： 组件尺寸，映射为：SUBMATTYPE；
	 * @return subMatType
	 */
	public String getSubMatType() {
		return subMatType;
	}

	public void setSubMatType(String subMatType) {
		this.subMatType = subMatType;
	}

	/**
	 * 描述：责任人 ，映射为：OWNER；
	 * @return owner
	 */
	public String getOwner() {
		return owner;
	}

	public void setOwner(String owner) {
		this.owner = owner;
	}

	/**
	 * 描述： 备用字段1，映射为：RESERVED1；
	 * @return reserved1
	 */
	public String getReserved1() {
		return reserved1;
	}

	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}

	/**
	 * 描述： 备用字段2，映射为：RESERVED2；
	 * @return reserved2
	 */
	public String getReserved2() {
		return reserved2;
	}

	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}

	/**
	 * 描述： 备用字段3，映射为：RESERVED3；
	 * @return reserved3
	 */
	public String getReserved3() {
		return reserved3;
	}

	public void setReserved3(String reserved3) {
		this.reserved3 = reserved3;
	}

	/**
	 * 描述： 备用字段4，映射为：RESERVED4；
	 * @return reserved4
	 */
	public String getReserved4() {
		return reserved4;
	}

	public void setReserved4(String reserved4) {
		this.reserved4 = reserved4;
	}

	/**
	 * 描述： 备用字段5，映射为：RESERVED5；
	 * @return reserved5
	 */
	public String getReserved5() {
		return reserved5;
	}

	public void setReserved5(String reserved5) {
		this.reserved5 = reserved5;
	}

	/**
	 * 描述： 备用字段6，映射为：RESERVED6；
	 * @return reserved6
	 */
	public String getReserved6() {
		return reserved6;
	}

	public void setReserved6(String reserved6) {
		this.reserved6 = reserved6;
	}

	/**
	 * 描述： 备用字段7，映射为：RESERVED7；
	 * @return reserved7
	 */
	public String getReserved7() {
		return reserved7;
	}

	public void setReserved7(String reserved7) {
		this.reserved7 = reserved7;
	}

	/**
	 * 描述： 备用字段8，映射为：RESERVED8；
	 * @return reserved8
	 */
	public String getReserved8() {
		return reserved8;
	}

	public void setReserved8(String reserved8) {
		this.reserved8 = reserved8;
	}

	/**
	 * 描述： ，映射为：ISTIMESENSITIVE；
	 * @return isTimeSensitive
	 */
	public Boolean getIsTimeSensitive() {
		return "Y".equalsIgnoreCase(this.isTimeSensitive) ? true : false; 
	}

	public void setIsTimeSensitive(Boolean isTimeSensitive) {
		this.isTimeSensitive = isTimeSensitive ? "Y" : "N";
	}

	/**
	 * 描述： ，映射为：SHELFWARNINGEXPIRE；
	 * @return shelfWarningExpire
	 */
	public Date getShelfWarningExpire() {
		return shelfWarningExpire;
	}

	public void setShelfWarningExpire(Date shelfWarningExpire) {
		this.shelfWarningExpire = shelfWarningExpire;
	}

	/**
	 * 描述： 保质期失效时间，映射为：SHELFLIFEEXPIRE；
	 * @return shelfLifeExpire
	 */
	public Date getShelfLifeExpire() {
		return shelfLifeExpire;
	}

	public void setShelfLifeExpire(Date shelfLifeExpire) {
		this.shelfLifeExpire = shelfLifeExpire;
	}

	/**
	 * 描述： 有效期，映射为：FLOORLIFEEXPIRE；
	 * @return floorLifeExpire
	 */
	public Date getFloorLifeExpire() {
		return floorLifeExpire;
	}

	public void setFloorLifeExpire(Date floorLifeExpire) {
		this.floorLifeExpire = floorLifeExpire;
	}

	/**
	 * 描述： 载具号，映射为：DURABLE；
	 * @return durable
	 */
	public String getDurable() {
		return durable;
	}

	public void setDurable(String durable) {
		this.durable = durable;
	}

	/**
	 * 描述： Batch类型，映射为：batchType；
	 * @return batchType
	 */
	public String getBatchType() {
		return batchType;
	}

	public void setBatchType(String batchType) {
		this.batchType = batchType;
	}

	/**
	 * 描述： 对象key，映射为：OBJECTRRN；
	 * @return objectRrn
	 */
	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	/**
	 * 描述： 对象区域，映射为：ORGRRN；
	 * @return orgRrn
	 */
	public Long getOrgRrn() {
		return orgRrn;
	}

	public void setOrgRrn(Long orgRrn) {
		this.orgRrn = orgRrn;
	}

	/**
	 * 描述： 对象区域名称，映射为：ORGNAME；
	 * @return orgName
	 */
	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public List<XMComponentUnit> getSubMComponentUnit() {
		return subMComponentUnit;
	}

	public void setSubMComponentUnit(List<XMComponentUnit> subMComponentUnit) {
		this.subMComponentUnit = subMComponentUnit;
	}

	public Long getCurrentCount() {
		return currentCount;
	}

	public void setCurrentCount(Long currentCount) {
		this.currentCount = currentCount;
	}

	public Long getMaxQTime() {
		return maxQTime;
	}

	public void setMaxQTime(Long maxQTime) {
		this.maxQTime = maxQTime;
	}

	public String getActionCode() {
		return actionCode;
	}

	public void setActionCode(String actionCode) {
		this.actionCode = actionCode;
	}

	public String getActionReason() {
		return actionReason;
	}

	public void setActionReason(String actionReason) {
		this.actionReason = actionReason;
	}

	public String getActionComment() {
		return actionComment;
	}

	public void setActionComment(String actionComment) {
		this.actionComment = actionComment;
	}

	public String getAttribute1() {
		return attribute1;
	}

	public void setAttribute1(String attribute1) {
		this.attribute1 = attribute1;
	}
}
