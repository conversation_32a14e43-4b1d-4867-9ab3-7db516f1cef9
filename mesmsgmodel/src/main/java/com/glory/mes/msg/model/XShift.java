package com.glory.mes.msg.model;

import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.model.XObject;

@XmlRootElement(name = "SHIFT")
@XmlAccessorType(XmlAccessType.NONE)
public class XShift extends XObject{
	/**
	 * 主键
	 */
	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name="STARTTIME")
	private Date startTime;

	@XmlElement(name="ENDTIME")
	private Date endTime;

	@XmlElement(name="NAME")
	private String name;

	@XmlElement(name="DESCRIPTION")
	private String description;

	@XmlElement(name = "SEQNO")
	private Long seqNo;
	
	@XmlElement(name = "SHIFTID")
	private String shiftId;
	
	@XmlElement(name = "CALENDARID")
	private String calendarId;
	
	@XmlElement(name = "STARTHOUR")
	private Long startHour;
	
	@XmlElement(name = "STARTMINUTE")
	private Long startMinute;
	
	@XmlElement(name = "ENDHOUR")
	private Long endHour;
	
	@XmlElement(name = "ENDMINUTE")
	private Long endMinute;
	
	/**
   	 * 描述：开始时间，映射为：STARTTIME；
   	 * @return startTime
   	 */
	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	/**
   	 * 描述：结束时间，映射为：ENDTIME；
   	 * @return endTime
   	 */
	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	/**
   	 * 描述：名称，映射为：NAME；
   	 * @return name
   	 */
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	/**
   	 * 描述：描述，映射为：DESCRIPTION；
   	 * @return description
   	 */
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Long getSeqNo() {
		return seqNo;
	}

	public void setSeqNo(Long seqNo) {
		this.seqNo = seqNo;
	}

	public String getShiftId() {
		return shiftId;
	}

	public void setShiftId(String shiftId) {
		this.shiftId = shiftId;
	}

	public Long getStartHour() {
		return startHour;
	}

	public void setStartHour(Long startHour) {
		this.startHour = startHour;
	}

	public Long getStartMinute() {
		return startMinute;
	}

	public void setStartMinute(Long startMinute) {
		this.startMinute = startMinute;
	}

	public Long getEndHour() {
		return endHour;
	}

	public void setEndHour(Long endHour) {
		this.endHour = endHour;
	}

	public Long getEndMinute() {
		return endMinute;
	}

	public void setEndMinute(Long endMinute) {
		this.endMinute = endMinute;
	}

	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}
}
