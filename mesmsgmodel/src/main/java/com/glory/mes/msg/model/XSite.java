package com.glory.mes.msg.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "SITE")
@XmlAccessorType(XmlAccessType.NONE)
public class XSite extends XBase{

    @XmlElement(name = "SITENAME")
    private String siteName;

    @XmlElement(name = "SITEVALUE")
    private String siteValue;

    /**
   	 * 描述：site采集名称，映射为：SITENAME；
   	 * @return siteName
   	 */
    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    /**
   	 * 描述：site采集数值，映射为：SITEVALUE；
   	 * @return siteValue
   	 */
    public String getSiteValue() {
        return siteValue;
    }

    public void setSiteValue(String siteValue) {
        this.siteValue = siteValue;
    }
}
