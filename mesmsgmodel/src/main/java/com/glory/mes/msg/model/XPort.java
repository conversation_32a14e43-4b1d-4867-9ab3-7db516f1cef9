package com.glory.mes.msg.model;

import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XDynamicComponent;

@XmlRootElement(name = "PORT")
@XmlAccessorType(XmlAccessType.NONE)
public class XPort extends XDynamicComponent {

	private static final long serialVersionUID = 1L;

	@XmlElement(name = "OBJECTRRN")
    private Long objectRrn;
	
	@XmlElement(name = "PORTID")
    private String portId;
	
	@XmlElement(name = "PORTNUM")
    private String portNum;

	@XmlElement(name = "DESCRIPTION")
	private String description;
	
	@XmlElement(name = "LOCATION")
	private String location;
	
	@XmlElement(name = "DEPARTMENT")
	private String department;
	
	@XmlElement(name = "PORTTYPE")
    private String portType;

    @XmlElement(name = "PORTUSETYPE")
    private String portUseType;
    
    @XmlElement(name = "MODEL")
    private String model;
    
    @XmlElement(name = "VENDOR")
    private String vendor;
    
    @XmlElement(name = "PARENTEQPRRN")
    private Long parentEqpRrn;
	
    @XmlElement(name = "PARENTEQPID")
	private String parentEqpId;
	
	/**
	 * 设备状态模型
	 */
    @XmlElement(name = "STATUSMODELRRN")
	private Long statusModelRrn;
	
    @XmlElement(name = "COMCLASS")
	private String comClass;
	
    @XmlElement(name = "STATE")
    private String state;
    
    @XmlElement(name = "SUBSTATE")
    private String subState;

    @XmlElement(name = "ACCESSSTATE")
    private String accessState;
    
    @XmlElement(name = "TRANSFERSTATE")
    private String transferState;
    
    @XmlElement(name = "TRANSFERSTATEENTRYTIME")
    private Date transferStateEntryTime;
    
    @XmlElement(name = "DURABLETYPE")
    private String durableType;
    
    @XmlElement(name="HOLDSTATE")
	private String holdState;
    
    @XmlElement(name = "MAINMATTYPE")
    private String mainMatType;

    @XmlElement(name = "SUBMATTYPE")
	private String subMatType;
    
    @XmlElement(name = "PORTACCESSMODE")
    private String portAccessModel;

    @XmlElement(name = "DURABLEID")
    private String durableId;
    
    @XmlElement(name = "CASSETTEID")
    private String cassetteId;
    
    @XmlElement(name="LOTID")
    private String lotId;

    public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	public String getHoldState() {
		return holdState;
	}

	public void setHoldState(String holdState) {
		this.holdState = holdState;
	}

	public String getPortId() {
		return portId;
	}

	public void setPortId(String portId) {
		this.portId = portId;
	}

    public String getPortNum() {
		return portNum;
	}

	public void setPortNum(String portNum) {
		this.portNum = portNum;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getPortType() {
        return portType;
    }

    public void setPortType(String portType) {
        this.portType = portType;
    }

    public String getPortUseType() {
        return portUseType;
    }

    public void setPortUseType(String portUseType) {
        this.portUseType = portUseType;
    }

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getPortAccessModel() {
		return portAccessModel;
	}

	public void setPortAccessModel(String portAccessModel) {
		this.portAccessModel = portAccessModel;
	}

	public String getAccessState() {
		return accessState;
	}

	public void setAccessState(String accessState) {
		this.accessState = accessState;
	}

	public String getDurableId() {
		return durableId;
	}

	public void setDurableId(String durableId) {
		this.durableId = durableId;
	}
	
	public String getCassetteId() {
		return cassetteId;
	}

	public void setCassetteId(String cassetteId) {
		this.cassetteId = cassetteId;
	}

	public String getLotId() {
		return lotId;
	}

	public void setLotId(String lotId) {
		this.lotId = lotId;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getVendor() {
		return vendor;
	}

	public void setVendor(String vendor) {
		this.vendor = vendor;
	}

	public Long getParentEqpRrn() {
		return parentEqpRrn;
	}

	public void setParentEqpRrn(Long parentEqpRrn) {
		this.parentEqpRrn = parentEqpRrn;
	}

	public String getParentEqpId() {
		return parentEqpId;
	}

	public void setParentEqpId(String parentEqpId) {
		this.parentEqpId = parentEqpId;
	}

	public Long getStatusModelRrn() {
		return statusModelRrn;
	}

	public void setStatusModelRrn(Long statusModelRrn) {
		this.statusModelRrn = statusModelRrn;
	}

	public String getComClass() {
		return comClass;
	}

	public void setComClass(String comClass) {
		this.comClass = comClass;
	}

	public String getSubState() {
		return subState;
	}

	public void setSubState(String subState) {
		this.subState = subState;
	}

	public String getTransferState() {
		return transferState;
	}

	public void setTransferState(String transferState) {
		this.transferState = transferState;
	}

	public Date getTransferStateEntryTime() {
		return transferStateEntryTime;
	}

	public void setTransferStateEntryTime(Date transferStateEntryTime) {
		this.transferStateEntryTime = transferStateEntryTime;
	}

	public String getDurableType() {
		return durableType;
	}

	public void setDurableType(String durableType) {
		this.durableType = durableType;
	}

	public String getMainMatType() {
		return mainMatType;
	}

	public void setMainMatType(String mainMatType) {
		this.mainMatType = mainMatType;
	}

	public String getSubMatType() {
		return subMatType;
	}

	public void setSubMatType(String subMatType) {
		this.subMatType = subMatType;
	}
	
}
