package com.glory.mes.msg.model;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "CONTEXT")
@XmlAccessorType(XmlAccessType.NONE)
public class XContext extends XBase {
	
	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name="NAME")
	private String name;
	
	@XmlElement(name="TABLERRN")
	private Long tableRrn;
	
	/**
	 * 指示是否允许相同的条件的ContextValue重复
	 * 如EDC中：
	 * 如果同一个工步只允许有一个数据采集,则为N
	 * 如果同一个工步只允许有多个数据采集,则为Y
	 */
	@XmlElement(name="ISREPEATALE")
	private String isRepeatale;
	
	@XmlElement(name="CONTEXTFIELDRRN1")
	private Long contextFieldRrn1;
	
	@XmlElement(name="CONTEXTFIELDRRN2")
	private Long contextFieldRrn2;
	
	@XmlElement(name="CONTEXTFIELDRRN3")
	private Long contextFieldRrn3;
	
	@XmlElement(name="CONTEXTFIELDRRN4")
	private Long contextFieldRrn4;
	
	@XmlElement(name="CONTEXTFIELDRRN5")
	private Long contextFieldRrn5;
	
	@XmlElement(name="CONTEXTFIELDRRN6")
	private Long contextFieldRrn6;
	
	@XmlElement(name="CONTEXTFIELDRRN7")
	private Long contextFieldRrn7;
	
	@XmlElement(name="CONTEXTFIELDRRN8")
	private Long contextFieldRrn8;
	
	@XmlElement(name="CONTEXTFIELDRRN9")
	private Long contextFieldRrn9;
	
	@XmlElement(name="CONTEXTFIELDRRN10")
	private Long contextFieldRrn10;
	
	@XmlElement(name="CONTEXTFIELDID1")
	private String contextFieldId1;
	
	@XmlElement(name="CONTEXTFIELDID2")
	private String contextFieldId2;
	
	@XmlElement(name="CONTEXTFIELDID3")
	private String contextFieldId3;
	
	@XmlElement(name="CONTEXTFIELDID4")
	private String contextFieldId4;
	
	@XmlElement(name="CONTEXTFIELDID5")
	private String contextFieldId5;
	
	@XmlElement(name="CONTEXTFIELDID6")
	private String contextFieldId6;
	
	@XmlElement(name="CONTEXTFIELDID7")
	private String contextFieldId7;
	
	@XmlElement(name="CONTEXTFIELDID8")
	private String contextFieldId8;
	
	@XmlElement(name="CONTEXTFIELDID9")
	private String contextFieldId9;
	
	@XmlElement(name="CONTEXTFIELDID10")
	private String contextFieldId10;
	
	@XmlElement(name="RESULTFIELDRRN1")
	private Long resultFieldRrn1;
	
	@XmlElement(name="RESULTFIELDRRN2")
	private Long resultFieldRrn2;
	
	@XmlElement(name="RESULTFIELDRRN3")
	private Long resultFieldRrn3;
	
	@XmlElement(name="RESULTFIELDRRN4")
	private Long resultFieldRrn4;
	
	@XmlElement(name="RESULTFIELDRRN5")
	private Long resultFieldRrn5;
	
	@XmlElement(name="RESULTFIELDRRN6")
	private Long resultFieldRrn6;
	
	@XmlElement(name="RESULTFIELDRRN7")
	private Long resultFieldRrn7;
	
	@XmlElement(name="RESULTFIELDRRN8")
	private Long resultFieldRrn8;
	
	@XmlElement(name="RESULTFIELDRRN9")
	private Long resultFieldRrn9;
	
	@XmlElement(name="RESULTFIELDRRN10")
	private Long resultFieldRrn10;

	@XmlElement(name="RESULTFIELDID1")
	private String resultFieldId1;
	
	@XmlElement(name="RESULTFIELDID2")
	private String resultFieldId2;
	
	@XmlElement(name="RESULTFIELDID3")
	private String resultFieldId3;
	
	@XmlElement(name="RESULTFIELDID4")
	private String resultFieldId4;
	
	@XmlElement(name="RESULTFIELDID5")
	private String resultFieldId5;
	
	@XmlElement(name="RESULTFIELDID6")
	private String resultFieldId6;
	
	@XmlElement(name="RESULTFIELDID7")
	private String resultFieldId7;
	
	@XmlElement(name="RESULTFIELDID8")
	private String resultFieldId8;
	
	@XmlElement(name="RESULTFIELDID9")
	private String resultFieldId9;
	
	@XmlElement(name="RESULTFIELDID10")
	private String resultFieldId10;
	
	@XmlElementWrapper(name = "CONTEXTRULELIST")
	@XmlElementRef
	private List<XContextRule> contextRuleList;
	
	@XmlElementWrapper(name = "CONTEXTVALUELIST")
	@XmlElementRef
	private List<XContextValue> contextValueList;
	
	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getName() {
		return name;
	}
	
	public Long getTableRrn() {
		return tableRrn;
	}

	public void setTableRrn(Long tableRrn) {
		this.tableRrn = tableRrn;
	}

	public void setIsRepeatable(Boolean isRepeatale) {
		this.isRepeatale = isRepeatale ? "Y" : "N";
	}
	
	public Boolean getIsRepeatable(){
		return "Y".equalsIgnoreCase(this.isRepeatale) ? true : false; 
	}
	
	public Long getContextFieldRrn1() {
		return contextFieldRrn1;
	}

	public void setContextFieldRrn1(Long contextFieldRrn1) {
		this.contextFieldRrn1 = contextFieldRrn1;
	}

	public Long getContextFieldRrn2() {
		return contextFieldRrn2;
	}

	public void setContextFieldRrn2(Long contextFieldRrn2) {
		this.contextFieldRrn2 = contextFieldRrn2;
	}

	public Long getContextFieldRrn3() {
		return contextFieldRrn3;
	}

	public void setContextFieldRrn3(Long contextFieldRrn3) {
		this.contextFieldRrn3 = contextFieldRrn3;
	}

	public Long getContextFieldRrn4() {
		return contextFieldRrn4;
	}

	public void setContextFieldRrn4(Long contextFieldRrn4) {
		this.contextFieldRrn4 = contextFieldRrn4;
	}

	public Long getContextFieldRrn5() {
		return contextFieldRrn5;
	}

	public void setContextFieldRrn5(Long contextFieldRrn5) {
		this.contextFieldRrn5 = contextFieldRrn5;
	}

	public Long getContextFieldRrn6() {
		return contextFieldRrn6;
	}

	public void setContextFieldRrn6(Long contextFieldRrn6) {
		this.contextFieldRrn6 = contextFieldRrn6;
	}

	public Long getContextFieldRrn7() {
		return contextFieldRrn7;
	}

	public void setContextFieldRrn7(Long contextFieldRrn7) {
		this.contextFieldRrn7 = contextFieldRrn7;
	}

	public Long getContextFieldRrn8() {
		return contextFieldRrn8;
	}

	public void setContextFieldRrn8(Long contextFieldRrn8) {
		this.contextFieldRrn8 = contextFieldRrn8;
	}

	public Long getContextFieldRrn9() {
		return contextFieldRrn9;
	}

	public void setContextFieldRrn9(Long contextFieldRrn9) {
		this.contextFieldRrn9 = contextFieldRrn9;
	}

	public Long getContextFieldRrn10() {
		return contextFieldRrn10;
	}

	public void setContextFieldRrn10(Long contextFieldRrn10) {
		this.contextFieldRrn10 = contextFieldRrn10;
	}

	public String getContextFieldId1() {
		return contextFieldId1;
	}

	public void setContextFieldId1(String contextFieldId1) {
		this.contextFieldId1 = contextFieldId1;
	}

	public String getContextFieldId2() {
		return contextFieldId2;
	}

	public void setContextFieldId2(String contextFieldId2) {
		this.contextFieldId2 = contextFieldId2;
	}

	public String getContextFieldId3() {
		return contextFieldId3;
	}

	public void setContextFieldId3(String contextFieldId3) {
		this.contextFieldId3 = contextFieldId3;
	}

	public String getContextFieldId4() {
		return contextFieldId4;
	}

	public void setContextFieldId4(String contextFieldId4) {
		this.contextFieldId4 = contextFieldId4;
	}

	public String getContextFieldId5() {
		return contextFieldId5;
	}

	public void setContextFieldId5(String contextFieldId5) {
		this.contextFieldId5 = contextFieldId5;
	}

	public String getContextFieldId6() {
		return contextFieldId6;
	}

	public void setContextFieldId6(String contextFieldId6) {
		this.contextFieldId6 = contextFieldId6;
	}

	public String getContextFieldId7() {
		return contextFieldId7;
	}

	public void setContextFieldId7(String contextFieldId7) {
		this.contextFieldId7 = contextFieldId7;
	}

	public String getContextFieldId8() {
		return contextFieldId8;
	}

	public void setContextFieldId8(String contextFieldId8) {
		this.contextFieldId8 = contextFieldId8;
	}

	public String getContextFieldId9() {
		return contextFieldId9;
	}

	public void setContextFieldId9(String contextFieldId9) {
		this.contextFieldId9 = contextFieldId9;
	}

	public String getContextFieldId10() {
		return contextFieldId10;
	}

	public void setContextFieldId10(String contextFieldId10) {
		this.contextFieldId10 = contextFieldId10;
	}

	public Long getResultFieldRrn1() {
		return resultFieldRrn1;
	}

	public void setResultFieldRrn1(Long resultFieldRrn1) {
		this.resultFieldRrn1 = resultFieldRrn1;
	}

	public Long getResultFieldRrn2() {
		return resultFieldRrn2;
	}

	public void setResultFieldRrn2(Long resultFieldRrn2) {
		this.resultFieldRrn2 = resultFieldRrn2;
	}

	public Long getResultFieldRrn3() {
		return resultFieldRrn3;
	}

	public void setResultFieldRrn3(Long resultFieldRrn3) {
		this.resultFieldRrn3 = resultFieldRrn3;
	}

	public Long getResultFieldRrn4() {
		return resultFieldRrn4;
	}

	public void setResultFieldRrn4(Long resultFieldRrn4) {
		this.resultFieldRrn4 = resultFieldRrn4;
	}

	public Long getResultFieldRrn5() {
		return resultFieldRrn5;
	}

	public void setResultFieldRrn5(Long resultFieldRrn5) {
		this.resultFieldRrn5 = resultFieldRrn5;
	}

	public Long getResultFieldRrn6() {
		return resultFieldRrn6;
	}

	public void setResultFieldRrn6(Long resultFieldRrn6) {
		this.resultFieldRrn6 = resultFieldRrn6;
	}

	public Long getResultFieldRrn7() {
		return resultFieldRrn7;
	}

	public void setResultFieldRrn7(Long resultFieldRrn7) {
		this.resultFieldRrn7 = resultFieldRrn7;
	}

	public Long getResultFieldRrn8() {
		return resultFieldRrn8;
	}

	public void setResultFieldRrn8(Long resultFieldRrn8) {
		this.resultFieldRrn8 = resultFieldRrn8;
	}

	public Long getResultFieldRrn9() {
		return resultFieldRrn9;
	}

	public void setResultFieldRrn9(Long resultFieldRrn9) {
		this.resultFieldRrn9 = resultFieldRrn9;
	}

	public Long getResultFieldRrn10() {
		return resultFieldRrn10;
	}

	public void setResultFieldRrn10(Long resultFieldRrn10) {
		this.resultFieldRrn10 = resultFieldRrn10;
	}

	public String getResultFieldId1() {
		return resultFieldId1;
	}

	public void setResultFieldId1(String resultFieldId1) {
		this.resultFieldId1 = resultFieldId1;
	}

	public String getResultFieldId2() {
		return resultFieldId2;
	}

	public void setResultFieldId2(String resultFieldId2) {
		this.resultFieldId2 = resultFieldId2;
	}

	public String getResultFieldId3() {
		return resultFieldId3;
	}

	public void setResultFieldId3(String resultFieldId3) {
		this.resultFieldId3 = resultFieldId3;
	}

	public String getResultFieldId4() {
		return resultFieldId4;
	}

	public void setResultFieldId4(String resultFieldId4) {
		this.resultFieldId4 = resultFieldId4;
	}

	public String getResultFieldId5() {
		return resultFieldId5;
	}

	public void setResultFieldId5(String resultFieldId5) {
		this.resultFieldId5 = resultFieldId5;
	}

	public String getResultFieldId6() {
		return resultFieldId6;
	}

	public void setResultFieldId6(String resultFieldId6) {
		this.resultFieldId6 = resultFieldId6;
	}

	public String getResultFieldId7() {
		return resultFieldId7;
	}

	public void setResultFieldId7(String resultFieldId7) {
		this.resultFieldId7 = resultFieldId7;
	}

	public String getResultFieldId8() {
		return resultFieldId8;
	}

	public void setResultFieldId8(String resultFieldId8) {
		this.resultFieldId8 = resultFieldId8;
	}

	public String getResultFieldId9() {
		return resultFieldId9;
	}

	public void setResultFieldId9(String resultFieldId9) {
		this.resultFieldId9 = resultFieldId9;
	}

	public String getResultFieldId10() {
		return resultFieldId10;
	}

	public void setResultFieldId10(String resultFieldId10) {
		this.resultFieldId10 = resultFieldId10;
	}

	public List<XContextRule> getContextRuleList() {
		return contextRuleList;
	}

	public void setContextRuleList(List<XContextRule> contextRuleList) {
		this.contextRuleList = contextRuleList;
	}

	public List<XContextValue> getContextValueList() {
		return contextValueList;
	}

	public void setContextValueList(List<XContextValue> contextValueList) {
		this.contextValueList = contextValueList;
	}

}
