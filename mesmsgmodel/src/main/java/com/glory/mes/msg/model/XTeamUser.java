package com.glory.mes.msg.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.glory.framework.core.xml.XBase;
import com.glory.msg.model.XObject;

@XmlRootElement(name = "STAGE")
@XmlAccessorType(XmlAccessType.NONE)
public class XTeamUser extends XObject{

	@XmlElement(name = "TEAM_RRN")
	private Long teamRrn;
	
	@XmlElement(name = "TEAM_ID")
	private String teamId;
	
	@XmlElement(name = "USER_RRN")
	private Long userRrn;
	
	@XmlElement(name = "USER_NAME")
	private String userName;

	@JsonSerialize(using = ToStringSerializer.class)
	public Long getTeamRrn() {
		return this.teamRrn;
	}

	public void setTeamRrn(Long teamRrn) {
		this.teamRrn = teamRrn;
	}

	public void setTeamId(String teamId) {
		this.teamId = teamId;
	}

	public String getTeamId() {
		return this.teamId;
	}

	@JsonSerialize(using = ToStringSerializer.class)
	public Long getUserRrn() {
		return this.userRrn;
	}

	public void setUserRrn(Long userRrn) {
		this.userRrn = userRrn;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserName() {
		return this.userName;
	}
	
}
