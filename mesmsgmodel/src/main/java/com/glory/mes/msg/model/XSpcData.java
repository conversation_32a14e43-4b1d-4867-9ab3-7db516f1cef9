package com.glory.mes.msg.model;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "DATA")
@XmlAccessorType(XmlAccessType.NONE)
public class XSpcData implements Serializable {

    private static final long serialVersionUID = 1L;

    public XSpcData() {

    }

    public XSpcData(XSpcData spcData) {
        this.dvName = spcData.getDvName();
        this.dataType = spcData.getDataType();
        this.sampleType = spcData.getSampleType();
        this.usl = spcData.getUsl();
        this.sl = spcData.getSl();
        this.lsl = spcData.getLsl();
        this.dataString = spcData.getDataString();
    }

    /**
     * 采集项名称
     */
    @XmlElement(name = "DVNAME")
    private String dvName;

    /**
     * 数据类型 Variable:计量型 Attribute:计数型
     */
    @XmlElement(name = "DATATYPE")
    private String dataType;

    /**
     * 采样类型 ITEM COMP SITE BUMP
     */
    @XmlElement(name = "SAMPLETYPE")
    private String sampleType;

    /**
     * 上限值
     */
    @XmlElement(name = "USL")
    private Double usl;

    /**
     * 中间制
     */
    @XmlElement(name = "SL")
    private Double sl;

    /**
     * 下限值
     */
    @XmlElement(name = "LSL")
    private Double lsl;

    /**
     * 采集的所有数据，以";"分隔
     */
    @XmlElement(name = "DATASTR")
    private String dataString;

    @XmlElementWrapper(name = "ITEMLIST")
    @XmlElementRef
    private List<XSpcDataItem> itemList;

    /**
   	 * 描述：采集项名称，映射为：DVNAME；
   	 * @return dvName
   	 */
    public String getDvName() {
        return dvName;
    }

    public void setDvName(String dvName) {
        this.dvName = dvName;
    }

    /**
   	 * 描述：数据类型 Variable:计量型 Attribute:计数型，映射为：DATATYPE；
   	 * @return dataType
   	 */
    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    /**
   	 * 描述：采样类型 ITEM COMP SITE BUMP，映射为：SAMPLETYPE；
   	 * @return sampleType
   	 */
    public String getSampleType() {
        return sampleType;
    }

    public void setSampleType(String sampleType) {
        this.sampleType = sampleType;
    }

    /**
   	 * 描述：采样类型 上线，映射为：USL；
   	 * @return usl
   	 */
    public Double getUsl() {
        return usl;
    }

    public void setUsl(Double usl) {
        this.usl = usl;
    }

    /**
   	 * 描述：标准线，映射为：SL；
   	 * @return sl
   	 */
    public Double getSl() {
        return sl;
    }

    public void setSl(Double sl) {
        this.sl = sl;
    }

    /**
   	 * 描述：采样类型 下线，映射为：LSL；
   	 * @return lsl
   	 */
    public Double getLsl() {
        return lsl;
    }

    public void setLsl(Double lsl) {
        this.lsl = lsl;
    }

    /**
   	 * 描述：采集的所有数据，以";"分隔，映射为：DATASTRING；
   	 * @return dataString
   	 */
    public String getDataString() {
        return dataString;
    }

    public void setDataString(String dataString) {
        this.dataString = dataString;
    }

    /**
   	 * 描述：采集的所有数据，以";"分隔，映射为：ITEMLIST；
   	 * @return itemList
   	 */
    public List<XSpcDataItem> getItemList() {
        return itemList;
    }

    public void setItemList(List<XSpcDataItem> itemList) {
        this.itemList = itemList;
    }

}
