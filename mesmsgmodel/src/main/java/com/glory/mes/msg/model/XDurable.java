package com.glory.mes.msg.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldDateAdapter;
import com.glory.framework.core.xml.XDynamicComponent;

@XmlRootElement(name = "DURABLE")
@XmlAccessorType(XmlAccessType.NONE)
public class XDurable extends XDynamicComponent {
	
	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name="DURABLEID")
	private String durableId;
	
	@XmlElement(name="SEQNO")
	private Long seqNo;
	
	@XmlElement(name="DURABLETYPE")
	private String durableType;
	
	@XmlElement(name="STATE")
	private String state;
	
	@XmlElement(name="DURABLESPECNAME")
	private String durableSpecName;
	
	@XmlElement(name="DURABLESPECVERSION")
	private Long durableSpecVersion;

	@XmlElement(name="HOLDSTATE")
	private String holdState;
	
	@XmlElement(name="CLEANSTATE")
	private String cleanState;
	
	@XmlElement(name="ACTIONCODE")
	private String actionCode;
	
	@XmlElement(name="ACTIONCOMMENT")
	private String actionComment;
	
	@XmlElement(name="ACTIONREASON")
	private String actionReason;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="UPDATED")
	private Date updated;
	
	@XmlElement(name="UPDATEDBY")
	private String updatedBy;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="CREATED")
	private Date created;
	
	@XmlElement(name="CREATEDBY")
	private String createdBy;
	
	@XmlElement(name="TRANSFERSTATE")
	private String transferState;
	
	@XmlElement(name="COMMENTS")
	private String comments;

	@XmlElement(name="ISGLOBAL")
	private String isGlobal;
	
	/*
	 * 新增2014-11-05
	 */
	@XmlElement(name="MAINMATTYPE")
	private String mainMatType;
	
	@XmlElement(name="CAPACITY")
	private BigDecimal capacity;
	
	@XmlElement(name="SUBMATTYPE")
	private String subMatType;
	
	@XmlElement(name="WARNINGCOUNT")
	private BigDecimal warningCount;
	
	@XmlElement(name="WARNINGTIME")
	private BigDecimal warningTime;
	
	@XmlElement(name="TIMEUNIT")
	private String timeUnit;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="CLEANDATE")
	private Date cleanDate;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="ARRIVALDATE")
	private Date arrivalDate;
	
	@XmlElement(name="PORTID")
	private String portId;
	
	@XmlElement(name="CATEGORY")
	private String category;
	
	@XmlElement(name="CURRENTCOUNT")
    private Long currentCount;
	
	@XmlElement(name="CURRENTQTY")
	private BigDecimal currentQty;
	/*
	 * 结束
	 */
	
	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}
	public String getDurableId() {
		return durableId;
	}

	public Long getSeqNo() {
		return seqNo;
	}

	public void setSeqNo(Long seqNo) {
		this.seqNo = seqNo;
	}

	public BigDecimal getCapacity() {
		return capacity;
	}

	public void setCapacity(BigDecimal capacity) {
		this.capacity = capacity;
	}

	public void setDurableId(String durableId) {
		this.durableId = durableId;
	}

	public String getDurableType() {
		return durableType;
	}

	public void setDurableType(String durableType) {
		this.durableType = durableType;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public Long getDurableSpecVersion() {
		return durableSpecVersion;
	}

	public void setDurableSpecVersion(Long durableSpecVersion) {
		this.durableSpecVersion = durableSpecVersion;
	}

	public String getDurableSpecName() {
		return durableSpecName;
	}

	public void setDurableSpecName(String durableSpecName) {
		this.durableSpecName = durableSpecName;
	}

	public String getHoldState() {
		return holdState;
	}

	public void setHoldState(String holdState) {
		this.holdState = holdState;
	}

	public String getCleanState() {
		return cleanState;
	}

	public void setCleanState(String cleanState) {
		this.cleanState = cleanState;
	}

	public String getActionCode() {
		return actionCode;
	}

	public void setActionCode(String actionCode) {
		this.actionCode = actionCode;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getActionComment() {
		return actionComment;
	}

	public void setActionComment(String actionComment) {
		this.actionComment = actionComment;
	}

	public String getActionReason() {
		return actionReason;
	}

	public void setActionReason(String actionReason) {
		this.actionReason = actionReason;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public Date getUpdated() {
		return updated;
	}

	public void setUpdated(Date updated) {
		this.updated = updated;
	}

	public String getTransferState() {
		return transferState;
	}

	public void setTransferState(String transferState) {
		this.transferState = transferState;
	}

	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}
	
	public Boolean getIsGlobal() {
		return "Y".equalsIgnoreCase(this.isGlobal) ? true : false;
	}

	public void setIsGlobal(Boolean isGlobal) {
		this.isGlobal = isGlobal ? "Y" : "N";
	}

	public String getMainMatType() {
		return mainMatType;
	}

	public void setMainMatType(String mainMatType) {
		this.mainMatType = mainMatType;
	}

	public String getSubMatType() {
		return subMatType;
	}

	public void setSubMatType(String subMatType) {
		this.subMatType = subMatType;
	}

	public BigDecimal getWarningCount() {
		return warningCount;
	}

	public void setWarningCount(BigDecimal warningCount) {
		this.warningCount = warningCount;
	}

	public BigDecimal getWarningTime() {
		return warningTime;
	}

	public void setWarningTime(BigDecimal warningTime) {
		this.warningTime = warningTime;
	}

	public String getTimeUnit() {
		return timeUnit;
	}

	public void setTimeUnit(String timeUnit) {
		this.timeUnit = timeUnit;
	}

	public Date getCleanDate() {
		return cleanDate;
	}

	public void setCleanDate(Date cleanDate) {
		this.cleanDate = cleanDate;
	}

	public Date getArrivalDate() {
		return arrivalDate;
	}

	public void setArrivalDate(Date arrivalDate) {
		this.arrivalDate = arrivalDate;
	}

	public String getPortId() {
		return portId;
	}

	public void setPortId(String portId) {
		this.portId = portId;
	}

	public void setIsGlobal(String isGlobal) {
		this.isGlobal = isGlobal;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public Long getCurrentCount() {
		return currentCount;
	}

	public void setCurrentCount(Long currentCount) {
		this.currentCount = currentCount;
	}

	public BigDecimal getCurrentQty() {
		return currentQty;
	}

	public void setCurrentQty(BigDecimal currentQty) {
		this.currentQty = currentQty;
	}
	
}
