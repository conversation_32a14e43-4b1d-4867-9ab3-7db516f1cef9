package com.glory.mes.msg.model;

import java.util.List;

import javax.persistence.Column;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "UNIT")
@XmlAccessorType(XmlAccessType.NONE)
public class XUnit extends XBase {

	private static final long serialVersionUID = 1L;
	
    @XmlElement(name = "UNITID")
    private String unitId;
    
    @XmlElement(name = "PORTID")
    private String portId;

    @XmlElement(name="COMCLASS")
	private String comClass;
    
    @XmlElement(name = "STATE")
    private String state;

    @XmlElement(name = "RECIPENAME")
    private String recipeName;

    @XmlElement(name="VERSION")
	private Long version;
    
    @XmlElement(name="CHECKSUM")
    private String checkSum;
    
    @XmlElement(name="SUBSTATE")
    private String subState;
	
	@XmlElement(name="PROCESSINGFLAG")
    private String processingFlag;
	
	@XmlElementWrapper(name="MLOTLIST")
	@XmlElementRef
	private List<XMLot> mLotList;
	
	@XmlElementWrapper(name="PARAMALIST")
    @XmlElementRef
    private List<XRecipeEquipmentParameter> paramaList;
	
	/**
	 * 描述：单位ID，映射为：UNITID；
	 * @return unitId
	 */
	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}

	public String getPortId() {
		return portId;
	}

	public void setPortId(String portId) {
		this.portId = portId;
	}

	public String getComClass() {
		return comClass;
	}

	public void setComClass(String comClass) {
		this.comClass = comClass;
	}

	/**
	 * 描述：状态，映射为：STATE；
	 * @return state
	 */
	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	/**
	 * 描述：recipe名称，映射为：RECIPENAME；
	 * @return recipeName
	 */
	public String getRecipeName() {
		return recipeName;
	}

	public void setRecipeName(String recipeName) {
		this.recipeName = recipeName;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getCheckSum() {
		return checkSum;
	}

	public void setCheckSum(String checkSum) {
		this.checkSum = checkSum;
	}

	/**
	 * 描述：子状态，映射为：SUBSTATE；
	 * @return subState
	 */
	public String getSubState() {
		return subState;
	}

	public void setSubState(String subState) {
		this.subState = subState;
	}

	/**
	 * 描述：，映射为：PROCESSINGFLAG；
	 * @return processingFlag
	 */
	public String getProcessingFlag() {
		return processingFlag;
	}

	public void setProcessingFlag(String processingFlag) {
		this.processingFlag = processingFlag;
	}

	/**
	 * 描述：物料批列表，映射为：MLOTLIST；
	 * @return mLotList
	 */
	public List<XMLot> getmLotList() {
		return mLotList;
	}

	public void setmLotList(List<XMLot> mLotList) {
		this.mLotList = mLotList;
	}

	public List<XRecipeEquipmentParameter> getParamaList() {
		return paramaList;
	}

	public void setParamaList(List<XRecipeEquipmentParameter> paramaList) {
		this.paramaList = paramaList;
	}
    
}
