package com.glory.mes.msg.model;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;

public class XIfState extends XCReworkPrdBase {
	
	@XmlElement(name="OBJECTTYPE")
	private String ifObjectType;

	@XmlElement(name="PARAMETER")
	private String ifParameter;
	
	@XmlElement(name="COMPARISON")
	private String ifParameterComparison;

	@XmlElement(name="PARAMETERVALUE")
	private String ifParameterValue;

	@XmlElement(name="EXPRESSION")
	private String ifExpression;
	
	@XmlElement(name = "elseName")
	private String elseName;

	@XmlElement(name = "endIfName")
	private String endIfName;
	
	public String getIfObjectType() {
		return ifObjectType;
	}

	public void setIfObjectType(String ifObjectType) {
		this.ifObjectType = ifObjectType;
	}

	public String getIfParameter() {
		return ifParameter;
	}

	public void setIfParameter(String ifParameter) {
		this.ifParameter = ifParameter;
	}

	public String getIfParameterComparison() {
		return ifParameterComparison;
	}

	public void setIfParameterComparison(String ifParameterComparison) {
		this.ifParameterComparison = ifParameterComparison;
	}

	public String getIfParameterValue() {
		return ifParameterValue;
	}

	public void setIfParameterValue(String ifParameterValue) {
		this.ifParameterValue = ifParameterValue;
	}

	public String getIfExpression() {
		return ifExpression;
	}

	public void setIfExpression(String ifExpression) {
		this.ifExpression = ifExpression;
	}

	public String getElseName() {
		return elseName;
	}

	public void setElseName(String elseName) {
		this.elseName = elseName;
	}

	public String getEndIfName() {
		return endIfName;
	}

	public void setEndIfName(String endIfName) {
		this.endIfName = endIfName;
	}

}
