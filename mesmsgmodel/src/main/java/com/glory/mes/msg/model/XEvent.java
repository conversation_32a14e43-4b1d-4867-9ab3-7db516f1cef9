package com.glory.mes.msg.model;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "EVENT")
@XmlAccessorType(XmlAccessType.NONE)
public class XEvent extends XBase {
	
	private static final long serialVersionUID = 1L;
	
	@XmlElement(name = "OBJECTRRN")
	protected Long objectRrn;
	
	@XmlElement(name="OBJECTTYPE")
	private String objectType;
	
	@XmlElement(name="EVENTID")
	private String eventId;
	
	@XmlElement(name="DESCRIPTION")
	private String description;
	
	@XmlElement(name="EVENTTYPE")
	private String eventType;

	@XmlElement(name="ISSYSTEM")
	private String isSystem;
	
	@XmlElement(name="CATEGORY")
	private String category;
	
	@XmlElementWrapper(name="EVENTSTATUSLIST")
	@XmlElementRef
	private List<XEventStatus> eventStatus;
	
	public Long getObjectRrn() {
		return this.objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}
	
	public String getObjectType() {
		return objectType;
	}

	public void setObjectType(String objectType) {
		this.objectType = objectType;
	}
	
	public String getEventId() {
		return eventId;
	}

	public void setEventId(String eventId) {
		this.eventId = eventId;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}
	
	public void setIsSystem(Boolean isSystem) {
		this.isSystem = isSystem ? "Y" : "N";
	}
	
	public Boolean getIsSystem(){
		return "Y".equalsIgnoreCase(this.isSystem) ? true : false; 
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public List<XEventStatus> getEventStatus() {
		return eventStatus;
	}

	public void setEventStatus(List<XEventStatus> eventStatus) {
		this.eventStatus = eventStatus;
	}
}