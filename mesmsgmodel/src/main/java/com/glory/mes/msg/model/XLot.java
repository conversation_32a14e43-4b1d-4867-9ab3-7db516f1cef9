package com.glory.mes.msg.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldDateAdapter;
import com.glory.framework.core.xml.XDynamicComponent;

@XmlRootElement(name = "LOT")
@XmlAccessorType(XmlAccessType.NONE)
public class XLot extends XDynamicComponent {
	
	public static final String VIRTUAL_LOT_TYPE_DUMMY="Dummy";
	public static final String VIRTUAL_LOT_TYPE_MONITOR="Monitor";
	public static final String VIRTUAL_LOT_TYPE_PRODUCTION="Production";
	
	private static final long serialVersionUID = 1L;

	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name="ORGRRN")
	private Long orgRrn;
	
	@XmlElement(name="ORGNAME")
	private String orgName;
	
	@XmlElement(name="LOTID")
	private String lotId;
	
	@XmlElement(name="LOTALIAS")
	private String lotAlias;

	@XmlElement(name="SUBSTRATEID1")
	private String substrateId1;

	@XmlElement(name="SUBSTRATEID2")
	private String substrateId2;
	
	@XmlElement(name="LOTTYPE")
	private String lotType;
	
	/**
	 * 虚拟type，Product，Dummy，Monitor，根据配置转换
	 */
	@XmlElement(name="VIRTUALLOTTYPE")
	private String virtualLotType;
	
	@XmlElement(name="PARTNAME")
	private String partName;

	@XmlElement(name="PARTVERSION")
	private Long partVersion;
	
	@XmlElement(name="PARTTYPE")
	private String partType;
	
	@XmlElement(name="PARTDESC")
	private String partDesc;
	
	@XmlElement(name="CUSTOMERCODE")
	private String customerCode;
	
	@XmlElement(name="MAINQTY")
	private BigDecimal mainQty;
	
	@XmlElement(name="SUBQTY")
	private BigDecimal subQty;
	
	@XmlElement(name="PROCESSNAME")
	private String processName;
	
	@XmlElement(name="PROCESSVERSION")
	private Long processVersion;
	
	@XmlElement(name="PROCEDURENAME")
	private String procedureName;
	
	@XmlElement(name="PROCEDUREVERSION")
	private Long procedureVersion;
	
	@XmlElement(name="STEPNAME")
	private String stepName;
	
	@XmlElement(name="STEPVERSION")
	private Long stepVersion;
	
	@XmlElement(name="STEPDESC")
	private String stepDesc;
	
	@XmlElement(name="STEPRRN")
	private Long stepRrn;
	
	@XmlElement(name="STAGEID")
	private String stageId;

	@XmlElement(name="COMCLASS")
	private String comClass;
	
	@XmlElement(name="STATE")
	private String state;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="UPDATED")
	private Date updated;
	
	@XmlElement(name="UPDATEDBY")
	private String updatedBy;
	
	@XmlElement(name="MAINMATTYPE")
	private String mainMatType;
	
	@XmlElement(name="EQUIPMENTID")
	private String equipmentId;
	
	@XmlElement(name="DURABLEID")
	private String durable;
	
	@XmlElement(name="GRADE1")
	private String grade1;
	
	@XmlElement(name="GRADE2")
	private String grade2;
	
	@XmlElement(name="JUDGE1")
	private String judge1;
	
	@XmlElement(name="JUDGE2")
	private String judge2;
	
	@XmlElement(name="REWORKCODE")
	private String reworkCode;
	
	@XmlElement(name="HOLDSTATE")
	private String holdState;
	
    @XmlElement(name = "PORTID")
    private String portId;

    @XmlElement(name = "RECIPENAME")
    private String recipeName;

    @XmlElement(name = "RECIPEVERSION")
    private Long recipeVersion;
    
    @XmlElement(name = "MASK")
    private String mask;
    
    @XmlElement(name = "EQUIPMENTRECIPE")
    private String equipmentRecipe;

	@XmlElement(name = "LAYOUTRECIPE")
    private String layoutRecipe;
    
    @XmlElement(name = "EQUIPMENTMASK")
    private String equipmentMask;

	@XmlElement(name = "POSITION")
    private String position;

    @XmlElement(name = "PRIORITY")
    private Long priority;

    @XmlElement(name = "WOID")
    private String woId;
    
    @XmlElement(name = "REWORKCOUNT")
    private Long reworkCount;
    
	@XmlElement(name = "USECOUNT")
    private Long useCount;

    @XmlElement(name = "BATCHID")
    private String batchId;
    
    @XmlElement(name="OPERATOR1")
	private String operator1;

    @XmlElement(name="OPERATOR2")
	private String operator2;
    
    @XmlElement(name="CREATED")
    @XmlJavaTypeAdapter(FieldDateAdapter.class)
    private Date created;
    
    @XmlElement(name="WAREHOUSEID")
    private String warehouseId;
    
    @XmlElement(name="TRANSFERSTATE")
    private String transferState;
    
    @XmlElement(name="ISSUBLOT")
    private String isSubLot;
    
    @XmlElement(name="LOCATORID")
    private String locatorId;
	
    @XmlElement(name="LOCATION")
	private String location;

    @XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="QUEUETIME")
    private Date queueTime;
    
    @XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="TRACKINTIME")
	private Date trackInTime;
    
    @XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="TRACKOUTTIME")
	private Date trackOutTime;
    
    @XmlElement(name="LASTPARTNAME")
	private String lastPartName;
    
    @XmlElement(name="LASTEQUIPMENTID")
	private String lastEquipmentId;
    
    @XmlElement(name="LASTSTEP")
   	private String lastStep;
    
    @XmlElement(name="LOTCOMMENT")
	private String lotComment;
    
    @XmlElementWrapper(name = "COMPONENTLIST")
	@XmlElementRef
	private List<XComponentUnit> componentList;
    
    @XmlElementWrapper(name = "SAMPLINGCOMPONENTLIST")
	@XmlElementRef
	private List<XComponentUnit> samplingComponentList;

	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="ENDTIME")
    private Date endTime;
    
    @XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="PACKINGTIME")
    private Date packingTime;
    
    @XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="PALLETTIME")
    private Date palletTime;
    
    @XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="SHIPTIME")
    private Date shipTime;
    
	/**
	 * 计划开始时间
	 */
    @XmlElement(name="PLANSTARTDATE")
	private Date planStartDate;

	/**
	 * 计划结束时间
	 */
    @XmlElement(name="PLANENDDATE")
	private Date planEndDate;
	
	/**
	 * 需求日期
	 */
    @XmlElement(name="REQUIREDATE")
	private Date requireDate;
    
	@XmlElement(name = "ATTRIBUTE1")
	private Object attribute1;

	@XmlElement(name = "ATTRIBUTE2")
	private Object attribute2;

	@XmlElement(name = "ATTRIBUTE3")
	private Object attribute3;

	@XmlElement(name = "ATTRIBUTE4")
	private Object attribute4;

	@XmlElement(name = "ATTRIBUTE5")
	private Object attribute5;
	
	@XmlElement(name = "SUBUNITTYPE")
	protected String subUnitType;
	
	@XmlElement(name = "CUSTOMERORDER")
	private String customerOrder;
	
	@XmlElement(name = "CUSTOMERPARTID")
	private String customerPartId;
	
	@XmlElement(name = "CUSTOMERLOTID")
	private String customerLotId;
	
	@XmlElement(name = "PARTRRN")
	private Long partRrn;
	
	@XmlElement(name = "ROOTLOTRRN")
	private Long rootLotRrn;

	@XmlElement(name = "PARENTLOTRRN")
	private Long parentLotRrn;
	
	@XmlElement(name = "CONTAMINATIONLEVEL")
	private String contaminationLevel;
	
	@XmlElement(name = "COMPONENTIDS")
	private String componentIds;

	@XmlElement(name = "PEQP")
	private String pEqp;

	@XmlElement(name = "PSTEP")
	private String pStep;

	@XmlElement(name = "ISRESERVE")
	private boolean isReserve;

	@XmlElement(name = "PMASK")
	private String pMask;
	
	@XmlElement(name = "PEQPPPID")
	private String pEqpPpid;

	@XmlElement(name = "PEQPRETICLE")
	private String pEqpReticle;

	@XmlElement(name = "JOBGROUP")
	private String jobGroup;

	@XmlElement(name = "CONTROLID")
	private String controlId;
	
	@XmlElement(name = "IS_SAMPLE") 
	private String isSample;
	
	@XmlElement(name = "PARENT_LOT_ID")
	private String parentLotId;
	
	public String getpMask() {
		return pMask;
	}

	public void setpMask(String pMask) {
		this.pMask = pMask;
	}

	public boolean isReserve() {
		return isReserve;
	}

	public void setIsReserve(boolean isReserve) {
		this.isReserve = isReserve;
	}

	public String getpEqp() {
		return pEqp;
	}

	public void setpEqp(String pEqp) {
		this.pEqp = pEqp;
	}

	public String getpStep() {
		return pStep;
	}

	public void setpStep(String pStep) {
		this.pStep = pStep;
	}

	public XLot() {
		super();
	}
	
	/**
	 * 描述： 批次号，映射为：LOTID；
	 * @return lotId
	 */
	public String getLotId() {
		return lotId;
	}

	public void setLotId(String lotId) {
		this.lotId = lotId;
	}

	/**
	 * 描述： ，映射为：LOTALIAS；
	 * @return lotAlias
	 */
	public String getLotAlias() {
		return lotAlias;
	}

	public void setLotAlias(String lotAlias) {
		this.lotAlias = lotAlias;
	}

	/**
	 * 描述： 刻印号1，映射为：SUBSTRATEID1；
	 * @return substrateId1
	 */
	public String getSubstrateId1() {
		return substrateId1;
	}

	public void setSubstrateId1(String substrateId1) {
		this.substrateId1 = substrateId1;
	}

	/**
	 * 描述： 刻印号2，映射为：SUBSTRATEID2；
	 * @return substrateId2
	 */
	public String getSubstrateId2() {
		return substrateId2;
	}

	public void setSubstrateId2(String substrateId2) {
		this.substrateId2 = substrateId2;
	}

	/**
	 * 描述： 批次类型，映射为：LOTTYPE；
	 * @return lotType
	 */
	public String getLotType() {
		return lotType;
	}

	public void setLotType(String lotType) {
		this.lotType = lotType;
	}

	/**
	 * 描述： 产品名称，映射为：PARTNAME；
	 * @return partName
	 */
	public String getPartName() {
		return partName;
	}

	public void setPartName(String partName) {
		this.partName = partName;
	}

	/**
	 * 描述： 产品版本，映射为：PARTVERSION；
	 * @return partVersion
	 */
	public Long getPartVersion() {
		return partVersion;
	}

	public void setPartVersion(Long partVersion) {
		this.partVersion = partVersion;
	}

	/**
	 * 描述： 产品类型，映射为：PARTTYPE；
	 * @return partType
	 */
	public String getPartType() {
		return partType;
	}

	public void setPartType(String partType) {
		this.partType = partType;
	}

	/**
	 * 描述： 产品描述，映射为：PARTDESC；
	 * @return partDesc
	 */
	public String getPartDesc() {
		return partDesc;
	}

	public void setPartDesc(String partDesc) {
		this.partDesc = partDesc;
	}

	/**
	 * 描述： 客户代码，映射为：CUSTOMERCODE；
	 * @return customerCode
	 */
	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	/**
	 * 描述： 主数量，映射为：MAINQTY；
	 * @return mainQty
	 */
	public BigDecimal getMainQty() {
		return mainQty;
	}

	public void setMainQty(BigDecimal mainQty) {
		this.mainQty = mainQty;
	}

	/**
	 * 描述： 子数量，映射为：SUBQTY；
	 * @return subQty
	 */
	public BigDecimal getSubQty() {
		return subQty;
	}

	public void setSubQty(BigDecimal subQty) {
		this.subQty = subQty;
	}

	/**
	 * 描述： 工艺名称，映射为：PROCESSNAME；
	 * @return processName
	 */
	public String getProcessName() {
		return processName;
	}

	public void setProcessName(String processName) {
		this.processName = processName;
	}

	/**
	 * 描述： 工艺版本，映射为：PROCESSVERSION；
	 * @return processVersion
	 */
	public Long getProcessVersion() {
		return processVersion;
	}

	public void setProcessVersion(Long processVersion) {
		this.processVersion = processVersion;
	}

	/**
	 * 描述： 流程名，映射为：PROCEDURENAME；
	 * @return procedureName
	 */
	public String getProcedureName() {
		return procedureName;
	}

	public void setProcedureName(String procedureName) {
		this.procedureName = procedureName;
	}

	/**
	 * 描述： 流程版本，映射为：PROCEDUREVERSION；
	 * @return procedureVersion
	 */
	public Long getProcedureVersion() {
		return procedureVersion;
	}

	public void setProcedureVersion(Long procedureVersion) {
		this.procedureVersion = procedureVersion;
	}
	
	public Date getQueueTime() {
		return queueTime;
	}

	public void setQueueTime(Date queueTime) {
		this.queueTime = queueTime;
	}

	/**
	 * 描述： 工步名称，映射为：STEPNAME；
	 * @return stepName
	 */
	public String getStepName() {
		return stepName;
	}

	public void setStepName(String stepName) {
		this.stepName = stepName;
	}

	/**
	 * 描述： 工步版本，映射为：STEPVERSION；
	 * @return stepVersion
	 */
	public Long getStepVersion() {
		return stepVersion;
	}

	public void setStepVersion(Long stepVersion) {
		this.stepVersion = stepVersion;
	}

	/**
	 * 描述： 工步描述，映射为：STEPDESC；
	 * @return stepDesc
	 */
	public String getStepDesc() {
		return stepDesc;
	}

	public void setStepDesc(String stepDesc) {
		this.stepDesc = stepDesc;
	}

	public String getStageId() {
		return stageId;
	}

	public void setStageId(String stageId) {
		this.stageId = stageId;
	}
	
	/**
	 * 描述： 状态大类，映射为：COMCLASS；
	 * @return comClass
	 */
	public String getComClass() {
		return comClass;
	}

	public void setComClass(String comClass) {
		this.comClass = comClass;
	}

	/**
	 * 描述： 状态，映射为：STATE；
	 * @return state
	 */
	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	/**
	 * 描述： 更新时间，映射为：UPDATED；
	 * @return updated
	 */
	public Date getUpdated() {
		return updated;
	}

	public void setUpdated(Date updated) {
		this.updated = updated;
	}

	/**
	 * 描述： 更新人，映射为：UPDATEDBY；
	 * @return updatedBy
	 */
	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	/**
	 * 描述： 批次包装类型，映射为：MAINMATTYPE；
	 * @return mainMatType
	 */
	public String getMainMatType() {
		return mainMatType;
	}

	public void setMainMatType(String mainMatType) {
		this.mainMatType = mainMatType;
	}

	/**
	 * 描述： 设备Id，映射为：EQUIPMENTID；
	 * @return equipmentId
	 */
	public String getEquipmentId() {
		return equipmentId;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	/**
	 * 描述： 规格1，映射为：GRADE1；
	 * @return grade1
	 */
	public String getGrade1() {
		return grade1;
	}

	public void setGrade1(String grade1) {
		this.grade1 = grade1;
	}

	
	/**
	 * 描述： 规格2，映射为：GRADE2；
	 * @return grade2
	 */
	public String getGrade2() {
		return grade2;
	}

	public void setGrade2(String grade2) {
		this.grade2 = grade2;
	}

	/**
	 * 描述： 综合判断结果1，映射为：JUDGE1；
	 * @return judge1
	 */
	public String getJudge1() {
		return judge1;
	}

	public void setJudge1(String judge1) {
		this.judge1 = judge1;
	}

	
	/**
	 * 描述： 综合判断结果2，映射为：JUDGE2；
	 * @return judge2
	 */
	public String getJudge2() {
		return judge2;
	}

	public void setJudge2(String judge2) {
		this.judge2 = judge2;
	}

	/**
	 * 描述： 返工码，映射为：REWORKCODE；
	 * @return reworkCode
	 */
	public String getReworkCode() {
		return reworkCode;
	}

	public void setReworkCode(String reworkCode) {
		this.reworkCode = reworkCode;
	}

	/**
	 * 描述： 暂停状态，映射为：HOLDSTATE；
	 * @return holdState
	 */
	public String getHoldState() {
		return holdState;
	}

	public void setHoldState(String holdState) {
		this.holdState = holdState;
	}

	/**
	 * 描述：Port号，映射为：PORTID；
	 * @return portId
	 */
	public String getPortId() {
		return portId;
	}

	public void setPortId(String portId) {
		this.portId = portId;
	}

	/**
	 * 描述：recipe名称，映射为：RECIPENAME；
	 * @return recipeName
	 */
	public String getRecipeName() {
		return recipeName;
	}

	public void setRecipeName(String recipeName) {
		this.recipeName = recipeName;
	}

	/**
	 * 描述：recipe版本，映射为：RECIPEVERSION；
	 * @return recipeVersion
	 */
	public Long getRecipeVersion() {
		return recipeVersion;
	}

	public void setRecipeVersion(Long recipeVersion) {
		this.recipeVersion = recipeVersion;
	}
	
	public String getMask() {
		return mask;
	}

	public void setMask(String mask) {
		this.mask = mask;
	}
	
    public String getEquipmentRecipe() {
		return equipmentRecipe;
	}

	public void setEquipmentRecipe(String equipmentRecipe) {
		this.equipmentRecipe = equipmentRecipe;
	}

	public String getEquipmentMask() {
		return equipmentMask;
	}

	public void setEquipmentMask(String equipmentMask) {
		this.equipmentMask = equipmentMask;
	}
	
	/**
	 * 描述：载具号，映射为：DURABLE；
	 * @return durable
	 */
	public String getDurable() {
		return durable;
	}

	public void setDurable(String durable) {
		this.durable = durable;
	}

	/**
	 * 描述：载具位置 ，映射为：POSITION；
	 * @return position
	 */
	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	/**
	 * 描述：优先级 ，映射为：PRIORITY；
	 * @return priority
	 */
	public Long getPriority() {
		return priority;
	}

	public void setPriority(Long priority) {
		this.priority = priority;
	}

	/**
	 * 描述：工单 ，映射为：WOID；
	 * @return woId
	 */
	public String getWoId() {
		return woId;
	}

	public void setWoId(String woId) {
		this.woId = woId;
	}

	/**
	 * 描述： 返工次数，映射为：REWORKCOUNT；
	 * @return reworkCount
	 */
	public Long getReworkCount() {
		return reworkCount;
	}

	public void setReworkCount(Long reworkCount) {
		this.reworkCount = reworkCount;
	}

	/**
	 * 描述： 使用次数，映射为：USECOUNT；
	 * @return useCount
	 */
    public Long getUseCount() {
		return useCount;
	}

	public void setUseCount(Long useCount) {
		this.useCount = useCount;
	}
	
	/**
	 * 描述： batch号，映射为：BATCHID；
	 * @return batchId
	 */
	public String getBatchId() {
		return batchId;
	}

	public void setBatchId(String batchId) {
		this.batchId = batchId;
	}

	/**
	 * 描述： 操作人1，映射为：OPERATOR1；
	 * @return operator1
	 */
	public String getOperator1() {
		return operator1;
	}

	public void setOperator1(String operator1) {
		this.operator1 = operator1;
	}

	/**
	 * 描述： 操作人2，映射为：OPERATOR2；
	 * @return operator2
	 */
	public String getOperator2() {
		return operator2;
	}

	public void setOperator2(String operator2) {
		this.operator2 = operator2;
	}

	/**
	 * 描述： 创建时间，映射为：CREATED；
	 * @return created
	 */
	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	/**
	 * 描述： 批次key，映射为：OBJECTRRN；
	 * @return objectRrn
	 */
	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	/**
	 * 描述： 区域key，映射为：ORGRRN；
	 * @return orgRrn
	 */
	public Long getOrgRrn() {
		return orgRrn;
	}

	public void setOrgRrn(Long orgRrn) {
		this.orgRrn = orgRrn;
	}

	/**
	 * 描述： 区域名称，映射为：ORGNAME；
	 * @return orgName
	 */
	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	/**
	 * 描述： 仓库Id，映射为：WAREHOUSEID；
	 * @return warehouseId
	 */
	public String getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(String warehouseId) {
		this.warehouseId = warehouseId;
	}

	/**
	 * 描述： 转移状态，映射为：TRANSFERSTATE；
	 * @return transferState
	 */
	public String getTransferState() {
		return transferState;
	}

	public void setTransferState(String transferState) {
		this.transferState = transferState;
	}
	
	/**
	 * 描述：，映射为：ISSUBLOT；
	 * @return isSubLot
	 */
	public Boolean getIsSubLot(){
		return "Y".equalsIgnoreCase(this.isSubLot) ? true : false; 
	}

	public void setIsSubLot(Boolean isSubLot) {
		this.isSubLot = isSubLot ? "Y" : "N";
	}
	
	/**
	 * 描述：当前库位，映射为：LOCATORID；
	 * @return locatorId
	 */
	public String getLocatorId() {
		return locatorId;
	}

	public void setLocatorId(String locatorId) {
		this.locatorId = locatorId;
	}

	/**
	 * 描述：当前位置，映射为：LOCATION；
	 * @return location
	 */
	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	/**
	 * 描述：进站时间，映射为：TRACKINTIME；
	 * @return trackInTime
	 */
	public Date getTrackInTime() {
		return trackInTime;
	}

	public void setTrackInTime(Date trackInTime) {
		this.trackInTime = trackInTime;
	}

	/**
	 * 描述：出站时间，映射为：TRACKOUTTIME；
	 * @return trackOutTime
	 */
	public Date getTrackOutTime() {
		return trackOutTime;
	}

	public void setTrackOutTime(Date trackOutTime) {
		this.trackOutTime = trackOutTime;
	}

	/**
	 * 描述：layoutRecipe，映射为：LAYOUTRECIPE；
	 * @return layoutRecipe
	 */
	public String getLayoutRecipe() {
		return layoutRecipe;
	}

	public void setLayoutRecipe(String layoutRecipe) {
		this.layoutRecipe = layoutRecipe;
	}

	/**
	 * 描述：，映射为：LASTPARTNAME；
	 * @return lastPartName
	 */
	public String getLastPartName() {
		return lastPartName;
	}

	public void setLastPartName(String lastPartName) {
		this.lastPartName = lastPartName;
	}

	/**
	 * 描述：，映射为：LASTEQUIPMENTID；
	 * @return lastEquipmentId
	 */
	public String getLastEquipmentId() {
		return lastEquipmentId;
	}

	public String getLotComment() {
		return lotComment;
	}

	/**
	 * 描述：备注，映射为：LOTCOMMENT；
	 * @return lotComment
	 */
	public void setLotComment(String lotComment) {
		this.lotComment = lotComment;
	}

	public void setLastEquipmentId(String lastEquipmentId) {
		this.lastEquipmentId = lastEquipmentId;
	}

	/**
	 * 描述：结束时间，映射为：ENDTIME；
	 * @return endTime
	 */
	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	/**
	 * 描述：包装时间，映射为：PACKINGTIME；
	 * @return packingTime
	 */
	public Date getPackingTime() {
		return packingTime;
	}

	public void setPackingTime(Date packingTime) {
		this.packingTime = packingTime;
	}

	/**
	 * 描述：托盘时间，映射为：PALLETTIME；
	 * @return palletTime
	 */
	public Date getPalletTime() {
		return palletTime;
	}

	public void setPalletTime(Date palletTime) {
		this.palletTime = palletTime;
	}

	/**
	 * 描述：，映射为：SHIPTIME；
	 * @return shipTime
	 */
	public Date getShipTime() {
		return shipTime;
	}

	public void setShipTime(Date shipTime) {
		this.shipTime = shipTime;
	}

	/**
	 * 描述：备用字段1，映射为：ATTRIBUTE1；
	 * @return attribute1
	 */
	public Object getAttribute1() {
		return attribute1;
	}

	public void setAttribute1(Object attribute1) {
		this.attribute1 = attribute1;
	}

	/**
	 * 描述：备用字段2，映射为：ATTRIBUTE2；
	 * @return attribute2
	 */
	public Object getAttribute2() {
		return attribute2;
	}

	public void setAttribute2(Object attribute2) {
		this.attribute2 = attribute2;
	}

	/**
	 * 描述：备用字段3，映射为：ATTRIBUTE3；
	 * @return attribute3
	 */
	public Object getAttribute3() {
		return attribute3;
	}

	public void setAttribute3(Object attribute3) {
		this.attribute3 = attribute3;
	}

	/**
	 * 描述：备用字段4，映射为：ATTRIBUTE4；
	 * @return attribute4
	 */
	public Object getAttribute4() {
		return attribute4;
	}

	public void setAttribute4(Object attribute4) {
		this.attribute4 = attribute4;
	}

	/**
	 * 描述：备用字段5，映射为：ATTRIBUTE5；
	 * @return attribute5
	 */
	public Object getAttribute5() {
		return attribute5;
	}

	public void setAttribute5(Object attribute5) {
		this.attribute5 = attribute5;
	}

	/**
	 * 描述：批次类型，映射为：SUBUNITTYPE；
	 * @return attribute5
	 */
	public String getSubUnitType() {
		return subUnitType;
	}

	public void setSubUnitType(String subUnitType) {
		this.subUnitType = subUnitType;
	}

	public Date getPlanStartDate() {
		return planStartDate;
	}

	public void setPlanStartDate(Date planStartDate) {
		this.planStartDate = planStartDate;
	}

	public Date getPlanEndDate() {
		return planEndDate;
	}

	public void setPlanEndDate(Date planEndDate) {
		this.planEndDate = planEndDate;
	}

	public Date getRequireDate() {
		return requireDate;
	}

	public void setRequireDate(Date requireDate) {
		this.requireDate = requireDate;
	}

	public String getCustomerOrder() {
		return customerOrder;
	}

	public void setCustomerOrder(String customerOrder) {
		this.customerOrder = customerOrder;
	}

	public String getCustomerPartId() {
		return customerPartId;
	}

	public void setCustomerPartId(String customerPartId) {
		this.customerPartId = customerPartId;
	}

	public String getCustomerLotId() {
		return customerLotId;
	}

	public void setCustomerLotId(String customerLotId) {
		this.customerLotId = customerLotId;
	}

	public Long getPartRrn() {
		return partRrn;
	}

	public void setPartRrn(Long partRrn) {
		this.partRrn = partRrn;
	}

	public void setIsSubLot(String isSubLot) {
		this.isSubLot = isSubLot;
	}

	public Long getStepRrn() {
		return stepRrn;
	}

	public void setStepRrn(Long stepRrn) {
		this.stepRrn = stepRrn;
	}

	public Long getRootLotRrn() {
		return rootLotRrn;
	}

	public void setRootLotRrn(Long rootLotRrn) {
		this.rootLotRrn = rootLotRrn;
	}

	public Long getParentLotRrn() {
		return parentLotRrn;
	}

	public void setParentLotRrn(Long parentLotRrn) {
		this.parentLotRrn = parentLotRrn;
	}
	
	public String getContaminationLevel() {
		return contaminationLevel;
	}

	public void setContaminationLevel(String contaminationLevel) {
		this.contaminationLevel = contaminationLevel;
	}
	
	public String getComponentIds() {
		return componentIds;
	}

	public void setComponentIds(String componentIds) {
		this.componentIds = componentIds;
	}

	/**
	 * 描述：Wafer，映射为：COMPONENTLIST；
	 * @return componentList
	 */
	public List<XComponentUnit> getComponentList() {
		return componentList;
	}

	public void setComponentList(List<XComponentUnit> componentList) {
		this.componentList = componentList;
	}
	
    public List<XComponentUnit> getSamplingComponentList() {
		return samplingComponentList;
	}

	public void setSamplingComponentList(List<XComponentUnit> samplingComponentList) {
		this.samplingComponentList = samplingComponentList;
	}

	public String getLastStep() {
		return lastStep;
	}

	public void setLastStep(String lastStep) {
		this.lastStep = lastStep;
	}
	
	public String getpEqpPpid() {
		return pEqpPpid;
	}

	public void setpEqpPpid(String pEqpPpid) {
		this.pEqpPpid = pEqpPpid;
	}

	public String getpEqpReticle() {
		return pEqpReticle;
	}

	public void setpEqpReticle(String pEqpReticle) {
		this.pEqpReticle = pEqpReticle;
	}

	public String getVirtualLotType() {
		return virtualLotType;
	}

	public void setVirtualLotType(String virtualLotType) {
		this.virtualLotType = virtualLotType;
	}

	public String getJobGroup() {
		return jobGroup;
	}

	public void setJobGroup(String jobGroup) {
		this.jobGroup = jobGroup;
	}

	public String getControlId() {
		return controlId;
	}

	public void setControlId(String controlId) {
		this.controlId = controlId;
	}

	public String getIsSample() {
		return isSample;
	}

	public void setIsSample(String isSample) {
		this.isSample = isSample;
	}

	public String getParentLotId() {
		return parentLotId;
	}

	public void setParentLotId(String parentLotId) {
		this.parentLotId = parentLotId;
	}
	
	public String getPartId() {
		return partName == null ? "" : partName + "." + partVersion;
	}
	
	public String getProcessId() {
		return processName == null ? "" : processName + "." + processVersion;
	}
	
	public String getProcedureId() {
		return procedureName == null ? "" : procedureName + "." + procedureVersion;
	}

	public String getStepId() {
		return stepName == null ? "" : stepName + "." + stepVersion;
	}
	
	public String getCstate() {
		if ("RUN".equals(state) && "On".equals(holdState)) {
			return "RUNNINGHOLD";
		} 
		if ("TRACKOUT".equals(state) && "On".equals(holdState)) {
			return "TRACKOUTHOLD";
		}
		if ("DISP".equals(state) && "On".equals(holdState)) {
			return "DISPATCHHOLD";
		}
		if ("BANK".equals(state) && "On".equals(holdState)) {
			return "BANKHOLD";
		}

		if ("SCRAP".equals(state) && "On".equals(holdState)) {
			return "SCRAP";
		}

		if ("On".equals(holdState)) {
			return "HOLD";
		}
		return state;
	}
	
}
