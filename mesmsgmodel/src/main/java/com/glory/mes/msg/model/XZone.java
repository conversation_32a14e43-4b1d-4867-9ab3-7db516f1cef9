package com.glory.mes.msg.model;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "ZONE")
@XmlAccessorType(XmlAccessType.NONE)
public class XZone extends XBase{
	
	private static final long serialVersionUID = 1L;
	
	@XmlElement(name="ZONEID")
	private String zoneId;
	
	@XmlElement(name="BUFSTATUS")
	private String bufferStatus;
	
	/**
	 * casstte数量
	 */
	@XmlElement(name="CASSETTECOUNT")
	private Long cassetteCount;
	
	/**
	 * 可用货架数
	 */
	@XmlElement(name="AVAILRACKCOUNT")
	private Long availRackCount;
	
	/**
	 * 不可用货架数
	 */
	@XmlElement(name="UNAVAILRACKCOUNT")
	private Long unavailRackCount;
	
	/**
	 * 预留的货架数
	 */
	@XmlElement(name="RESERVERACKCOUNT")
	private Long reserveRackCount;
	
	@XmlElementWrapper(name="RACKLIST")
	@XmlElementRef
	private List<XRack> rackList;

	/**
	 * 描述：casstte数量，映射为：CASSETTECOUNT；
	 * @return cassetteCount
	 */
	public Long getCassetteCount() {
		return cassetteCount;
	}

	public void setCassetteCount(Long cassetteCount) {
		this.cassetteCount = cassetteCount;
	}

	/**
	 * 描述：可用货架数，映射为：AVAILRACKCOUNT；
	 * @return availRackCount
	 */
	public Long getAvailRackCount() {
		return availRackCount;
	}

	public void setAvailRackCount(Long availRackCount) {
		this.availRackCount = availRackCount;
	}

	/**
	 * 描述：不可用货架数，映射为：AVAILRACKCOUNT；
	 * @return availRackCount
	 */
	public Long getUnavailRackCount() {
		return unavailRackCount;
	}

	public void setUnavailRackCount(Long unavailRackCount) {
		this.unavailRackCount = unavailRackCount;
	}

	/**
	 * 描述：预留的货架数，映射为：RESERVERACKCOUNT；
	 * @return reserveRackCount
	 */
	public Long getReserveRackCount() {
		return reserveRackCount;
	}

	public void setReserveRackCount(Long reserveRackCount) {
		this.reserveRackCount = reserveRackCount;
	}

	/**
	 * 描述：，映射为：RACKLIST；
	 * @return rackList
	 */
	public List<XRack> getRackList() {
		return rackList;
	}

	public void setRackList(List<XRack> rackList) {
		this.rackList = rackList;
	}

	/**
	 * 描述：货架ID，映射为：ZONEID；
	 * @return zoneId
	 */
	public String getZoneId() {
		return zoneId;
	}

	public void setZoneId(String zoneId) {
		this.zoneId = zoneId;
	}

	/**
	 * 描述：buffer状态，映射为：BUFFERSTATUS；
	 * @return bufferStatus
	 */
	public String getBufferStatus() {
		return bufferStatus;
	}

	public void setBufferStatus(String bufferStatus) {
		this.bufferStatus = bufferStatus;
	}
	
}
