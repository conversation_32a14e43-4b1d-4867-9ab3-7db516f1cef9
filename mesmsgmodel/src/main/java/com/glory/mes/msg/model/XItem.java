package com.glory.mes.msg.model;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name="PROCESSINFO")
@XmlAccessorType(XmlAccessType.FIELD)
public class XItem extends XBase {

	@XmlElement(name="ITEMNAME")
	private String itemName;//Item名称
	
	@XmlElementWrapper(name="SITELIST")
	@XmlElementRef
    private List<XSite> site;

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public List<XSite> getSite() {
		return site;
	}

	public void setSite(List<XSite> site) {
		this.site = site;
	}

}
