package com.glory.mes.msg.model;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "JUDGE")
@XmlAccessorType(XmlAccessType.NONE)
public class XWipJudge extends XBase {

	private static final long serialVersionUID = 1L;

	@XmlElement(name="LOTID")
	private String lotId;
	
	@XmlElement(name="COMPONENTID")
	private String componentId;
	
	@XmlElement(name="SUBCOMPONENTID")
	private String subComponentId;
	
	@XmlElement(name="STEPNAME")
	private String stepName;
	
	@XmlElement(name="EQUIPMENTID")
	private String equipmentId;
	
	@XmlElement(name="EQUIPMENTUNITID")
	private String equipmentUnitId;
	
	@XmlElement(name="GRADE1")
	private String grade1; 
	
	@XmlElement(name="GRADE2")
	private String grade2;

	@XmlElement(name="JUDGE1")
	private String judge1;

	@XmlElement(name="JUDGE2")
	private String judge2;
	
	@XmlElement(name="JUDGE3")
	private String judge3;

	@XmlElement(name="REWORKCODE")
	private String reworkCode;
	
	@XmlElement(name="JUDGERESULT")
	private String judgeResult;
	
	@XmlElement(name="COMMENT")
	private String comment;
	
	@XmlElementWrapper(name="DEFECTLIST")
	@XmlElementRef
	private List<XWipDefect> defectList;

	/**
	 * 描述：批次号，映射为：LOTID；
	 * @return lotId
	 */
	public String getLotId() {
		return lotId;
	}

	public void setLotId(String lotId) {
		this.lotId = lotId;
	}

	/**
	 * 描述：组件ID，映射为：COMPONENTID；
	 * @return componentId
	 */
	public String getComponentId() {
		return componentId;
	}

	public void setComponentId(String componentId) {
		this.componentId = componentId;
	}

	/**
	 * 描述：子组件ID，映射为：SUBCOMPONENTID；
	 * @return subComponentId
	 */
	public String getSubComponentId() {
		return subComponentId;
	}

	public void setSubComponentId(String subComponentId) {
		this.subComponentId = subComponentId;
	}

	/**
	 * 描述：工步名称，映射为：STEPNAME；
	 * @return stepName
	 */
	public String getStepName() {
		return stepName;
	}

	public void setStepName(String stepName) {
		this.stepName = stepName;
	}

	/**
	 * 描述：设备ID，映射为：EQUIPMENTID；
	 * @return equipmentId
	 */
	public String getEquipmentId() {
		return equipmentId;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	/**
	 * 描述：设备单位ID，映射为：EQUIPMENTUNITID；
	 * @return equipmentUnitId
	 */
	public String getEquipmentUnitId() {
		return equipmentUnitId;
	}

	public void setEquipmentUnitId(String equipmentUnitId) {
		this.equipmentUnitId = equipmentUnitId;
	}

	/**
	 * 描述：规格1，映射为：GRADE1；
	 * @return grade1
	 */
	public String getGrade1() {
		return grade1;
	}

	public void setGrade1(String grade1) {
		this.grade1 = grade1;
	}

	/**
	 * 描述：规格2，映射为：GRADE2；
	 * @return grade2
	 */
	public String getGrade2() {
		return grade2;
	}

	public void setGrade2(String grade2) {
		this.grade2 = grade2;
	}

	/**
	 * 描述：判定1，映射为：JUDGE1；
	 * @return judge1
	 */
	public String getJudge1() {
		return judge1;
	}

	public void setJudge1(String judge1) {
		this.judge1 = judge1;
	}

	/**
	 * 描述：判定2，映射为：JUDGE2；
	 * @return judge2
	 */
	public String getJudge2() {
		return judge2;
	}

	public void setJudge2(String judge2) {
		this.judge2 = judge2;
	}

	/**
	 * 描述：判定3，映射为：JUDGE3；
	 * @return judge3
	 */
	public String getJudge3() {
		return judge3;
	}

	public void setJudge3(String judge3) {
		this.judge3 = judge3;
	}

	/**
	 * 描述：返工码，映射为：REWORKCODE；
	 * @return reworkCode
	 */
	public String getReworkCode() {
		return reworkCode;
	}

	public void setReworkCode(String reworkCode) {
		this.reworkCode = reworkCode;
	}

	/**
	 * 描述：判定结果，映射为：JUDGERESULT；
	 * @return judgeResult
	 */
	public String getJudgeResult() {
		return judgeResult;
	}

	public void setJudgeResult(String judgeResult) {
		this.judgeResult = judgeResult;
	}

	/**
	 * 描述：备注，映射为：COMMENT；
	 * @return comment
	 */
	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	/**
	 * 描述：，映射为：DEFECTLIST；
	 * @return defectList
	 */
	public List<XWipDefect> getDefectList() {
		return defectList;
	}

	public void setDefectList(List<XWipDefect> defectList) {
		this.defectList = defectList;
	}
	

}
