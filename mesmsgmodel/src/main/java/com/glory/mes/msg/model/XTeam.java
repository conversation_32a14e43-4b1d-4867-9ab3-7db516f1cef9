package com.glory.mes.msg.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.glory.framework.core.xml.XBase;
import com.glory.msg.model.XObject;


@XmlRootElement(name = "BASTEAM")
@XmlAccessorType(XmlAccessType.NONE)
public class XTeam extends XObject{

	@XmlElement(name = "NAME")
	private String name;
	
	@XmlElement(name = "DESCRIPTION")
	private String description;
	
	@XmlElement(name = "RESERVED1")
	private String reserved1;
	
	@XmlElement(name = "RESERVED2")
	private String reserved2;
	
	@XmlElement(name = "RESERVED3")
	private String reserved3;
	
	@XmlElement(name = "RESERVED4")
	private String reserved4;
	
	@XmlElement(name = "RESERVED5")
	private String reserved5;

	public void setName(String name) {
		this.name = name;
	}

	public String getName() {
		return this.name;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getDescription() {
		return this.description;
	}

	public String getReserved1() {
		return this.reserved1;
	}

	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}

	public String getReserved2() {
		return this.reserved2;
	}

	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}

	public String getReserved3() {
		return this.reserved3;
	}

	public void setReserved3(String reserved3) {
		this.reserved3 = reserved3;
	}

	public String getReserved4() {
		return this.reserved4;
	}

	public void setReserved4(String reserved4) {
		this.reserved4 = reserved4;
	}

	public String getReserved5() {
		return this.reserved5;
	}

	public void setReserved5(String reserved5) {
		this.reserved5 = reserved5;
	}
	
}
