package com.glory.mes.msg.model;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import com.glory.framework.core.xml.XBase;

@XmlRootElement(name="MLOTHLD")
@XmlAccessorType(XmlAccessType.NONE)
public class XMLotHold extends XBase {
	
	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name="HISTORYSEQ")
	private String hisSeq;
	
	@XmlElement(name="HISTORYSEQNO")
	private Long hisSeqNo;

	@XmlElement(name="TRANSWAREHOUSEID")
	private String transWarehouseId;
	
	@XmlElement(name="TRANSSTORAGETYPE")
	private String transStorageType;

	@XmlElement(name="TRANSSTORAGEID")
	private String transStorageId;
	
	@XmlElement(name="MLOTRRN")
	private Long lotRrn;
	
	@XmlElement(name="MLOTID")
	private String lotId;
	
	@XmlElement(name="TRANSMAINQTY")
	private BigDecimal transMainQty;
	
	@XmlElement(name="TRANSSUBQTY")
	private BigDecimal transSubQty;

	@XmlElement(name="ACTIONCODE")
	private String actionCode;
	
	@XmlElement(name="ACTIONREASON")
	private String actionReason;
	
	@XmlElement(name="ACTIONCOMMENT")
	private String actionComment;
	
	@XmlElement(name="OPERATOR")
	private String operator;
	
	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	public String getHisSeq() {
		return hisSeq;
	}

	public void setHisSeq(String hisSeq) {
		this.hisSeq = hisSeq;
	}

	public Long getHisSeqNo() {
		return hisSeqNo;
	}

	public void setHisSeqNo(Long hisSeqNo) {
		this.hisSeqNo = hisSeqNo;
	}

	public String getTransWarehouseId() {
		return transWarehouseId;
	}

	public void setTransWarehouseId(String transWarehouseId) {
		this.transWarehouseId = transWarehouseId;
	}

	public String getTransStorageType() {
		return transStorageType;
	}

	public void setTransStorageType(String transStorageType) {
		this.transStorageType = transStorageType;
	}

	public String getTransStorageId() {
		return transStorageId;
	}

	public void setTransStorageId(String transStorageId) {
		this.transStorageId = transStorageId;
	}

	public Long getLotRrn() {
		return lotRrn;
	}

	public void setLotRrn(Long lotRrn) {
		this.lotRrn = lotRrn;
	}

	public String getLotId() {
		return lotId;
	}

	public void setLotId(String lotId) {
		this.lotId = lotId;
	}

	public BigDecimal getTransMainQty() {
		return transMainQty;
	}

	public void setTransMainQty(BigDecimal transMainQty) {
		this.transMainQty = transMainQty;
	}

	public BigDecimal getTransSubQty() {
		return transSubQty;
	}

	public void setTransSubQty(BigDecimal transSubQty) {
		this.transSubQty = transSubQty;
	}

	public String getActionCode() {
		return actionCode;
	}

	public void setActionCode(String actionCode) {
		this.actionCode = actionCode;
	}

	public String getActionReason() {
		return actionReason;
	}

	public void setActionReason(String actionReason) {
		this.actionReason = actionReason;
	}

	public String getActionComment() {
		return actionComment;
	}

	public void setActionComment(String actionComment) {
		this.actionComment = actionComment;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

}
