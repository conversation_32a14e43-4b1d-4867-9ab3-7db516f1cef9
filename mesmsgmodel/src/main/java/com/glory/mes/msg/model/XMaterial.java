package com.glory.mes.msg.model;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XDynamicComponent;

@XmlRootElement(name = "MATERIAL")
@XmlAccessorType(XmlAccessType.NONE)
public class XMaterial extends XDynamicComponent {

	private static final long serialVersionUID = 1L;
	
	public static final String MATERIAL_TYPE_MATERIAL = "Material";
	public static final String MATERIAL_TYPE_PART = "Part";

	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name = "NAME")
	private String name;

	@XmlElement(name = "DESCRIPTION")
	private String description;
	
	@XmlElement(name = "CLAZZ")
	private String clazz;
	
	@XmlElement(name = "DISPLAYVERISON")
	private String displayVerison;
	
	@XmlElement(name = "ISGLOBAL")
	private String isGlobal;

	@XmlElement(name = "VERSION")
	private Long version;

	@XmlElement(name = "STATUS")
	private String status;

	@XmlElement(name = "CUSTOMERCODE")
	private String customerCode;
	
	@XmlElement(name = "PARTNERCODE")
	private String partnerCode;
	
	@XmlElement(name = "EAN")
	private String ean;

	@XmlElement(name = "SKU")
	private String sku;
	
	@XmlElement(name = "MATERIALTYPE")
	private String materialType;
	
	@XmlElement(name = "MAINMATTYPE")
	private String mainMatType;

	@XmlElement(name = "UOMID")
	private String uomId;

	@XmlElement(name = "CLASSIFICATION")
	private String classification;

	@XmlElement(name = "WAREHOUSEID")
	private String warehouseId;

	@XmlElement(name = "LOCATORID")
	private String locatorId;

	@XmlElement(name = "MLOTID")
	private String mLotId;

	@XmlElement(name = "MATERIALPOSITION")
	private String materialPosition;

	@XmlElement(name = "USEQTY")
	private Long useQty;

	@XmlElement(name = "MATERIALSTATE")
	private String materialState;

	@XmlElement(name = "MLOTQTY")
	private Long mLotQty;

	@XmlElement(name = "RETURNCODE")
	private String returnCode;

	@XmlElement(name = "RETURNCOMMENT")
	private String returnComment;

	@XmlElement(name="SPEC1")
	private String spec1;
	
	@XmlElement(name="SPEC2")
	private String spec2;
	
	@XmlElement(name="SPEC3")
	private String spec3;
	
	@XmlElement(name="SPEC4")
	private String spec4;
	
	@XmlElement(name="SHELFLIFE")
	private Long shelfLife;
	
	@XmlElement(name="OWNER1")
	private String owner1;
	
	@XmlElement(name="COMMENTS")
	private String comments;
	
	@XmlElement(name="CATEGORY")
	private String category;
	
	@XmlElement(name="STATUSMODELRRN")
	private Long statusModelRrn;
	
	@XmlElement(name="GROUP1")
	private String group1;
	
	@XmlElement(name="GROUP3")
	private String group3;
	
	@XmlElement(name="GROUP4")
	private String group4;
	
	@XmlElement(name="MAXSTOCKQTY")
	private BigDecimal maxStockQty;
	
	@XmlElement(name="SAFETYSTOCKQTY")
	private BigDecimal safetyStockQty;
	
	@XmlElement(name="SUBMATTYPE")
	private String subMatType;
	
	@XmlElement(name="BATCHTYPE")
	private String batchType;
	
	@XmlElement(name="ISPRODUCTION")
	private String isProduction;
	
	@XmlElement(name="ISPHANTOM")
	private String isPhantom;
	
	@XmlElement(name="BOMVERIFIED")
	private String bomVerified;
	
	@XmlElement(name="NUMBEROFPACK")
	private BigDecimal numberOfPack;
	
	@XmlElement(name="PACKAGEHIERARCHYNAME")
	private String packageHierarchyName;
	
	@XmlElement(name="NUMBEROFPALLET")
	private BigDecimal numberOfPallet;
	
	@XmlElement(name="LOTSIZE")
	private BigDecimal lotSize;
	
	@XmlElement(name="SUBLOTSIZE")
	private BigDecimal subLotSize;
	
	@XmlElement(name="IDGENERATOR")
	private String idGenerator;
	
	@XmlElement(name="ISTIMESENSITIVE")
	private String isTimeSensitive = "N";
	
	@XmlElement(name="FLOORLIFEACTIVATOR")
	private String floorLifeActivator;
	
	@XmlElement(name="VOLUME")
	private BigDecimal volume;
	
	@XmlElement(name="WEIGHT")
	private BigDecimal weight;
	
	@XmlElement(name="SHELFWIDTH")
	private BigDecimal shelfWidth;
	
	@XmlElement(name="SHELFHEIGHT")
	private BigDecimal shelfHeight;
	
	@XmlElement(name="SHELFDEPTH")
	private BigDecimal shelfDepth;
	
	@XmlElement(name="OWNER2")
	private String owner2;
	
	@XmlElement(name="RESERVED1")
	private String reserved1;
	
	@XmlElement(name="RESERVED2")
	private String reserved2;
	
	@XmlElement(name="RESERVED3")
	private String reserved3;
	
	@XmlElement(name="RESERVED4")
	private String reserved4;
	
	@XmlElement(name="RESERVED5")
	private String reserved5;
	
	@XmlElement(name="RESERVED6")
	private String reserved6;
	
	@XmlElement(name="RESERVED7")
	private String reserved7;
	
	@XmlElement(name="RESERVED8")
	private String reserved8;

	@XmlElement(name="LIMITLIFE")
	private Long limitLife;
	
	@XmlElement(name="LIMITWARNING")
	private Long limitWarning;
	
	@XmlElement(name="SHELFWARNING")
	private Long shelfWarning;
	
	@XmlElement(name="SHELFLIFEUNIT")
	private String shelfLifeUnit;
	
	@XmlElement(name="FLOORLIFE")
	private Long floorLife;
	
	@XmlElement(name="FLOORLIFEUNIT")
	private String floorLifeUnit;
	
	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	/**
	 * 物料组别
	 */
	@XmlElement(name="GROUP2")
	private String group2;
	
	/**
	 * 描述：物料名称，映射为：NAME；
	 * @return name
	 */
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	/**
	 * 描述：物料描述，映射为：DESCRIPTION；
	 * @return description
	 */
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	/**
	 * 描述：物料版本，映射为：VERSION；
	 * @return version
	 */
	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	/**
	 * 描述：物料状态，映射为：STATUS；
	 * @return status
	 */
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	/**
	 * 描述：客户代码，映射为：CUSTOMERCODE；
	 * @return customerCode
	 */
	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	/**
	 * 描述：物料类型，映射为：materialType；
	 * @return materialType
	 */
	public String getMaterialType() {
		return materialType;
	}

	public void setMaterialType(String materialType) {
		this.materialType = materialType;
	}

	/**
	 * 描述：物料单位，映射为：UOMID；
	 * @return uomId
	 */
	public String getUomId() {
		return uomId;
	}

	public void setUomId(String uomId) {
		this.uomId = uomId;
	}

	/**
	 * 描述：物料分类，映射为：CLASSIFICATION；
	 * @return classification
	 */
	public String getClassification() {
		return classification;
	}

	public void setClassification(String classification) {
		this.classification = classification;
	}

	/**
	 * 描述：物料仓库，映射为：WAREHOUSEID；
	 * @return warehouseId
	 */
	public String getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(String warehouseId) {
		this.warehouseId = warehouseId;
	}

	/**
	 * 描述：物料区域，映射为：LOCATORID；
	 * @return locatorId
	 */
	public String getLocatorId() {
		return locatorId;
	}

	public void setLocatorId(String locatorId) {
		this.locatorId = locatorId;
	}

	/**
	 * 描述：物料批号，映射为：MLOTID；
	 * @return mLotId
	 */
	public String getmLotId() {
		return mLotId;
	}

	public void setmLotId(String mLotId) {
		this.mLotId = mLotId;
	}

	/**
	 * 描述：物料位置，映射为：MATERIALPOSITION；
	 * @return materialPosition
	 */
	public String getMaterialPosition() {
		return materialPosition;
	}

	public void setMaterialPosition(String materialPosition) {
		this.materialPosition = materialPosition;
	}

	/**
	 * 描述：使用数量，映射为：USEQTY；
	 * @return useQty
	 */
	public Long getUseQty() {
		return useQty;
	}

	public void setUseQty(Long useQty) {
		this.useQty = useQty;
	}

	/**
	 * 描述：物料状态，映射为：MATERIALSTATE；
	 * @return materialState
	 */
	public String getMaterialState() {
		return materialState;
	}

	public void setMaterialState(String materialState) {
		this.materialState = materialState;
	}

	/**
	 * 描述：物料数量，映射为：MLOTQTY；
	 * @return mLotQty
	 */
	public Long getmLotQty() {
		return mLotQty;
	}

	public void setmLotQty(Long mLotQty) {
		this.mLotQty = mLotQty;
	}

	/**
	 * 描述：返回码，映射为：RETURNCODE；
	 * @return returnCode
	 */
	public String getReturnCode() {
		return returnCode;
	}

	public void setReturnCode(String returnCode) {
		this.returnCode = returnCode;
	}

	/**
	 * 描述：返回备注，映射为：RETURNCOMMENT；
	 * @return returnComment
	 */
	public String getReturnComment() {
		return returnComment;
	}

	public void setReturnComment(String returnComment) {
		this.returnComment = returnComment;
	}

	/**
	 * 描述：规格1，映射为：SPEC1；
	 * @return spec1
	 */
	public String getSpec1() {
		return spec1;
	}

	public void setSpec1(String spec1) {
		this.spec1 = spec1;
	}

	/**
	 * 描述：保质期，映射为：SHELFLIFE；
	 * @return shelfLife
	 */
	public Long getShelfLife() {
		return shelfLife;
	}

	public void setShelfLife(Long shelfLife) {
		this.shelfLife = shelfLife;
	}

	/**
	 * 描述：责任人1，映射为：OWNER1；
	 * @return owner1
	 */
	public String getOwner1() {
		return owner1;
	}

	public void setOwner1(String owner1) {
		this.owner1 = owner1;
	}

	/**
	 * 描述：备注，映射为：COMMENTS；
	 * @return comments
	 */
	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	/**
	 * 描述：组2，映射为：GROUP2；
	 * @return group2
	 */
	public String getGroup2() {
		return group2;
	}

	public void setGroup2(String group2) {
		this.group2 = group2;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public Long getStatusModelRrn() {
		return statusModelRrn;
	}

	public void setStatusModelRrn(Long statusModelRrn) {
		this.statusModelRrn = statusModelRrn;
	}

	public String getGroup1() {
		return group1;
	}

	public void setGroup1(String group1) {
		this.group1 = group1;
	}

	public BigDecimal getMaxStockQty() {
		return maxStockQty;
	}

	public void setMaxStockQty(BigDecimal maxStockQty) {
		this.maxStockQty = maxStockQty;
	}

	public BigDecimal getSafetyStockQty() {
		return safetyStockQty;
	}

	public void setSafetyStockQty(BigDecimal safetyStockQty) {
		this.safetyStockQty = safetyStockQty;
	}

	public String getSpec2() {
		return spec2;
	}

	public void setSpec2(String spec2) {
		this.spec2 = spec2;
	}

	public String getSpec3() {
		return spec3;
	}

	public void setSpec3(String spec3) {
		this.spec3 = spec3;
	}

	public String getSpec4() {
		return spec4;
	}

	public void setSpec4(String spec4) {
		this.spec4 = spec4;
	}

	public String getSubMatType() {
		return subMatType;
	}

	public void setSubMatType(String subMatType) {
		this.subMatType = subMatType;
	}

	public String getBatchType() {
		return batchType;
	}

	public void setBatchType(String batchType) {
		this.batchType = batchType;
	}

	public String getReserved1() {
		return reserved1;
	}

	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}

	public String getReserved2() {
		return reserved2;
	}

	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}

	public String getReserved3() {
		return reserved3;
	}

	public void setReserved3(String reserved3) {
		this.reserved3 = reserved3;
	}

	public String getReserved4() {
		return reserved4;
	}

	public void setReserved4(String reserved4) {
		this.reserved4 = reserved4;
	}

	public String getReserved5() {
		return reserved5;
	}

	public void setReserved5(String reserved5) {
		this.reserved5 = reserved5;
	}

	public String getReserved6() {
		return reserved6;
	}

	public void setReserved6(String reserved6) {
		this.reserved6 = reserved6;
	}

	public String getReserved7() {
		return reserved7;
	}

	public void setReserved7(String reserved7) {
		this.reserved7 = reserved7;
	}

	public String getReserved8() {
		return reserved8;
	}

	public void setReserved8(String reserved8) {
		this.reserved8 = reserved8;
	}

	public Boolean getIsProduction() {
		return "Y".equalsIgnoreCase(this.isProduction) ? true : false;
	}

	public void setIsProduction(Boolean isProduction) {
		this.isProduction = isProduction ? "Y" : "N";
	}
	
	public Long getLimitLife() {
		return limitLife;
	}

	public void setLimitLife(Long limitLife) {
		this.limitLife = limitLife;
	}

	public String getClazz() {
		return clazz;
	}

	public void setClazz(String clazz) {
		this.clazz = clazz;
	}

	public Long getLimitWarning() {
		return limitWarning;
	}

	public void setLimitWarning(Long limitWarning) {
		this.limitWarning = limitWarning;
	}

	public Long getShelfWarning() {
		return shelfWarning;
	}

	public void setShelfWarning(Long shelfWarning) {
		this.shelfWarning = shelfWarning;
	}

	public String getShelfLifeUnit() {
		return shelfLifeUnit;
	}

	public void setShelfLifeUnit(String shelfLifeUnit) {
		this.shelfLifeUnit = shelfLifeUnit;
	}

	public Long getFloorLife() {
		return floorLife;
	}

	public void setFloorLife(Long floorLife) {
		this.floorLife = floorLife;
	}

	public String getFloorLifeUnit() {
		return floorLifeUnit;
	}

	public void setFloorLifeUnit(String floorLifeUnit) {
		this.floorLifeUnit = floorLifeUnit;
	}

	public void setIsProduction(String isProduction) {
		this.isProduction = isProduction;
	}

	public String getDisplayVerison() {
		return displayVerison;
	}

	public void setDisplayVerison(String displayVerison) {
		this.displayVerison = displayVerison;
	}

	public String getPartnerCode() {
		return partnerCode;
	}

	public void setPartnerCode(String partnerCode) {
		this.partnerCode = partnerCode;
	}

	public String getEan() {
		return ean;
	}

	public void setEan(String ean) {
		this.ean = ean;
	}

	public String getSku() {
		return sku;
	}

	public void setSku(String sku) {
		this.sku = sku;
	}

	public String getMainMatType() {
		return mainMatType;
	}

	public void setMainMatType(String mainMatType) {
		this.mainMatType = mainMatType;
	}

	public String getGroup3() {
		return group3;
	}

	public void setGroup3(String group3) {
		this.group3 = group3;
	}

	public String getGroup4() {
		return group4;
	}

	public void setGroup4(String group4) {
		this.group4 = group4;
	}

	public String getBomVerified() {
		return bomVerified;
	}

	public void setBomVerified(String bomVerified) {
		this.bomVerified = bomVerified;
	}

	public BigDecimal getNumberOfPack() {
		return numberOfPack;
	}

	public void setNumberOfPack(BigDecimal numberOfPack) {
		this.numberOfPack = numberOfPack;
	}

	public String getPackageHierarchyName() {
		return packageHierarchyName;
	}

	public void setPackageHierarchyName(String packageHierarchyName) {
		this.packageHierarchyName = packageHierarchyName;
	}

	public BigDecimal getNumberOfPallet() {
		return numberOfPallet;
	}

	public void setNumberOfPallet(BigDecimal numberOfPallet) {
		this.numberOfPallet = numberOfPallet;
	}

	public BigDecimal getLotSize() {
		return lotSize;
	}

	public void setLotSize(BigDecimal lotSize) {
		this.lotSize = lotSize;
	}

	public BigDecimal getSubLotSize() {
		return subLotSize;
	}

	public void setSubLotSize(BigDecimal subLotSize) {
		this.subLotSize = subLotSize;
	}

	public String getIdGenerator() {
		return idGenerator;
	}

	public void setIdGenerator(String idGenerator) {
		this.idGenerator = idGenerator;
	}

	public String getFloorLifeActivator() {
		return floorLifeActivator;
	}

	public void setFloorLifeActivator(String floorLifeActivator) {
		this.floorLifeActivator = floorLifeActivator;
	}

	public BigDecimal getVolume() {
		return volume;
	}

	public void setVolume(BigDecimal volume) {
		this.volume = volume;
	}

	public BigDecimal getWeight() {
		return weight;
	}

	public void setWeight(BigDecimal weight) {
		this.weight = weight;
	}

	public BigDecimal getShelfWidth() {
		return shelfWidth;
	}

	public void setShelfWidth(BigDecimal shelfWidth) {
		this.shelfWidth = shelfWidth;
	}

	public BigDecimal getShelfHeight() {
		return shelfHeight;
	}

	public void setShelfHeight(BigDecimal shelfHeight) {
		this.shelfHeight = shelfHeight;
	}

	public BigDecimal getShelfDepth() {
		return shelfDepth;
	}

	public void setShelfDepth(BigDecimal shelfDepth) {
		this.shelfDepth = shelfDepth;
	}

	public String getOwner2() {
		return owner2;
	}

	public void setOwner2(String owner2) {
		this.owner2 = owner2;
	}
	
	public Boolean getIsGlobal() {
		return "Y".equalsIgnoreCase(this.isGlobal) ? true : false;
	}

	public void setIsGlobal(Boolean isGlobal) {
		if(isGlobal != null) {
			this.isGlobal = isGlobal ? "Y" : "N";
		}else {
			this.isGlobal = "N";
		}
	}
	
	public Boolean getIsPhantom() {
		return "Y".equalsIgnoreCase(this.isPhantom) ? true : false;
	}

	public void setIsPhantom(Boolean isPhantom) {
		if(isPhantom != null) {
			this.isPhantom = isPhantom ? "Y" : "N";
		}else {
			this.isPhantom = "N";
		}
	}
	
	public Boolean getIsTimeSensitive() {
		return "Y".equalsIgnoreCase(this.isTimeSensitive) ? true : false;
	}

	public void setIsTimeSensitive(Boolean isTimeSensitive) {
		if(isTimeSensitive != null) {
			this.isTimeSensitive = isTimeSensitive ? "Y" : "N";
		}else {
			this.isTimeSensitive = "N";
		}
	}
	
}
