package com.glory.mes.msg.model;

import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldDateAdapter;
import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "LOTHOLD")
@XmlAccessorType(XmlAccessType.NONE)
public class XLotHold extends XBase {

	public static final String HOLDCODE_SUBLOTHOLD = "SubLotHold";
	
	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlElement(name = "LOTRRN")
	private Long lotRrn;

	@XmlElement(name = "SEQNO")
	private Long seqNo;
	
	@XmlElement(name = "HOLDUSERNAME")
	private String holdUserName;
	
	@XmlElement(name = "HOLDCODE")
	private String holdCode;

	@XmlElement(name = "HOLDREASON")
	private String holdReason;

	@XmlElement(name = "HOLDPWD")
	private String holdPwd;

	@XmlElement(name = "HOLDOCAPID")
	private String holdOcapId;
	
	@XmlElement(name = "HOLDCOMMENT")
	private String holdComment;
	
	@XmlElement(name = "HOLDLEVEL")
	private String holdLevel;
	
	@XmlElement(name = "HOLDOWNER")
	private String holdOwner;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="HOLDTIME")
	private Date holdTime;
	
	public void setSeqNo(Long seqNo) {
		this.seqNo = seqNo;
	}

	/**
	 * 描述： 序号，映射为：SEQNO；
	 * @return seqNo
	 */
	public Long getSeqNo() {
		return seqNo;
	}

	/**
	 * 描述： 批次key，映射为：LOTRRN；
	 * @return lotRrn
	 */
	public Long getLotRrn() {
		return lotRrn;
	}

	public void setLotRrn(Long lotRrn) {
		this.lotRrn = lotRrn;
	}

	/**
	 * 描述： 暂停码，映射为：HOLDCODE；
	 * @return holdCode
	 */
	public String getHoldCode() {
		return holdCode;
	}

	public void setHoldCode(String holdCode) {
		this.holdCode = holdCode;
	}

	/**
	 * 描述： 暂停原因，映射为：HOLDREASON；
	 * @return holdReason
	 */
	public String getHoldReason() {
		return holdReason;
	}

	public void setHoldReason(String holdReason) {
		this.holdReason = holdReason;
	}

	
	/**
	 * 描述： 暂停密码，映射为：HOLDPWD；
	 * @return holdPwd
	 */
	public String getHoldPwd() {
		return holdPwd;
	}

	public void setHoldPwd(String holdPwd) {
		this.holdPwd = holdPwd;
	}
	
	public void setHoldLevel(String holdLevel) {
		this.holdLevel = holdLevel;
	}

	/**
	 * 描述： 暂停等级，映射为：HOLDLEVEL；
	 * @return holdLevel
	 */
	public String getHoldLevel() {
		return holdLevel;
	}

	public void setHoldOwner(String holdOwner) {
		this.holdOwner = holdOwner;
	}

	/**
	 * 描述： 暂停责任人，映射为：HOLDOWNER；
	 * @return holdOwner
	 */
	public String getHoldOwner() {
		return holdOwner;
	}
	
	public void setHoldTime(Date holdTime) {
		this.holdTime = holdTime;
	}

	/**
	 * 描述： 暂停时间，映射为：HOLDTIME；
	 * @return holdTime
	 */
	public Date getHoldTime() {
		return holdTime;
	}
	
//	public void setPreComClass(String preComClass) {
//		this.preComClass = preComClass;
//	}
//
//	public String getPreComClass() {
//		return preComClass;
//	}
//
//	public void setPreState(String preState) {
//		this.preState = preState;
//	}
//
//	public String getPreState() {
//		return preState;
//	}
//
//	public void setPreSubState(String preSubState) {
//		this.preSubState = preSubState;
//	}
//
//	public String getPreSubState() {
//		return preSubState;
//	}

	public void setHoldUserName(String holdUserName) {
		this.holdUserName = holdUserName;
	}

	/**
	 * 描述： 暂停用户名，映射为：HOLDUSERNAME；
	 * @return holdUserName
	 */
	public String getHoldUserName() {
		return holdUserName;
	}
	
	/**
	 * 描述： ，映射为：HOLDOCAPID；
	 * @return holdOcapId
	 */
	public String getHoldOcapId() {
		return holdOcapId;
	}

	public void setHoldOcapId(String holdOcapId) {
		this.holdOcapId = holdOcapId;
	}

	public void setHoldComment(String holdComment) {
		this.holdComment = holdComment;
	}

	/**
	 * 描述： 暂停备注，映射为：HOLDCOMMENT；
	 * @return holdComment
	 */
	public String getHoldComment() {
		return holdComment;
	}

	/**
	 * 描述： 暂停key，映射为：OBJECTRRN；
	 * @return objectRrn
	 */
	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

}
