package com.glory.mes.msg.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "EDCITEM")
@XmlAccessorType(XmlAccessType.NONE)
public class XEdcItem extends XBase {

	private static final long serialVersionUID = 1L;

	@XmlElement(name="NAME")
	private String name;
	
	@XmlElement(name="DESCRIPTION")
	private String description;
	
	@XmlElement(name="UNIT")
	private String unit;
	
	@XmlElement(name="DIGITS")
	private Long digits;
	
	@XmlElement(name="DATA_TYPE")
	private String dataType = "VARIABLE";
	
	@XmlElement(name="SAMPLE_TYPE")
	private String sampleType;
	
	@XmlElement(name="COMMENTS")
	private String comments;
	
	@XmlElement(name="LOTID")
	private String lotId;
	
	/**
	 * 该数据采集项所需的能力
	 * 如果该项不为空,需要在数据采集时根据设备能力找对应的设备
	 */
	@XmlElement(name="CAPABILITY")
	private Long capability;
	
	@XmlElement(name="EDCSETNAME")
	private String edcSetName;
	
	@XmlElement(name="EDCSETVERSION")
	private Long edcSetVersion;
	
	@XmlElement(name="ITEM")
	private Long item;
	
	@XmlElement(name="COMP")
	private Long comp;
	
	@XmlElement(name="SITE")
	private Long site;

	@XmlElement(name="ISFURMULA")
	private Boolean isFormula;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public Long getDigits() {
		return digits;
	}

	public void setDigits(Long digits) {
		this.digits = digits;
	}

	public String getDataType() {
		return dataType;
	}

	public void setDataType(String dataType) {
		this.dataType = dataType;
	}

	public String getSampleType() {
		return sampleType;
	}

	public void setSampleType(String sampleType) {
		this.sampleType = sampleType;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public Long getCapability() {
		return capability;
	}

	public void setCapability(Long capability) {
		this.capability = capability;
	}
	
	public String getEdcSetName() {
		return edcSetName;
	}

	public void setEdcSetName(String edcSetName) {
		this.edcSetName = edcSetName;
	}

	public Long getEdcSetVersion() {
		return edcSetVersion;
	}

	public void setEdcSetVersion(Long edcSetVersion) {
		this.edcSetVersion = edcSetVersion;
	}

	public Long getComp() {
		return comp;
	}

	public void setComp(Long comp) {
		this.comp = comp;
	}

	public Long getSite() {
		return site;
	}

	public void setSite(Long site) {
		this.site = site;
	}

	public Long getItem() {
		return item;
	}

	public void setItem(Long item) {
		this.item = item;
	}

	public String getLotId() {
		return lotId;
	}

	public void setLotId(String lotId) {
		this.lotId = lotId;
	}

	public Boolean getIsFormula() {
		return isFormula;
	}

	public void setIsFormula(Boolean formula) {
		isFormula = formula;
	}
}
