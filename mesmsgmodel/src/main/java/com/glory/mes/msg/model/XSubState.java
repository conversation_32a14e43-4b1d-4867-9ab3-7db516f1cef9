package com.glory.mes.msg.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "STAGE")
@XmlAccessorType(XmlAccessType.NONE)
public class XSubState extends XBase{
	@XmlElement(name = "OBJECTTYPE")
	private String objectType;
	
	@XmlElement(name = "COMCLASS")
	private String comClass;
	
	@XmlElement(name = "STATE")
	private String state;
	
	@XmlElement(name = "SUBSTATE")
	private String subState;
	
	@XmlElement(name = "DESCRIPTION")
	private String description;
	
	@XmlElement(name = "SEQNO")
	private Integer seqNo;

	@XmlElement(name = "OBJECTRRN")
	protected Long objectRrn;

	public Long getObjectRrn() {
		return this.objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}
	
	public String getObjectType() {
		return this.objectType;
	}

	public void setObjectType(String objectType) {
		this.objectType = objectType;
	}

	public String getDescription() {
		return this.description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getComClass() {
		return this.comClass;
	}

	public void setComClass(String comClass) {
		this.comClass = comClass;
	}

	public String getSubState() {
		return this.subState;
	}

	public void setSubState(String subState) {
		this.subState = subState;
	}

	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public Integer getSeqNo() {
		return this.seqNo;
	}

	public void setSeqNo(Integer seqNo) {
		this.seqNo = seqNo;
	}
}
