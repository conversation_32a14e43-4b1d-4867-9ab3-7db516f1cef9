package com.glory.mes.msg.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.model.XObject;
@XmlRootElement(name="LOCATION")
@XmlAccessorType(XmlAccessType.NONE)
public class XLocation extends XObject {
	
	@XmlElement(name="ORGRRN")
	private Long orgRrn;
	
	@XmlElement(name="NAME")
	private String name;
	
	@XmlElement(name="DESCRIPTION")
	private String description;
	
	@XmlElement(name="LOCATIONTYPE")
	private String locationType;
	
	//表示它不是一个具体的Location
	//而是所有线别相同功能的Location的集合
	//当指定批次Move到此Location时,
	//系统会根据LocationLine找到批次所在的线别所对应的实际Location
//	@Column(name="IS_LINE_GROUP")
//	private String isLineGroup = "N";
	
	//在批次移动此Location时,是否自动接收该批
	//预留栏位
	@XmlElement(name="ISAUTOACCEPT")
	private String isAutoAccept;
	
	@XmlElement(name="PARENTLOCATIONRRN")
	private Long parentLocationRrn;
	
	public Long getOrgRrn() {
		return orgRrn;
	}

	public void setOrgRrn(Long orgRrn) {
		this.orgRrn = orgRrn;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public void setLocationType(String locationType) {
		this.locationType = locationType;
	}

	public String getLocationType() {
		return locationType;
	}
	
//	public void setIsLineGroup(Boolean isLineGroup) {
//		this.isLineGroup = isLineGroup ? "Y" : "N";
//	}
//	
//	public Boolean getIsLineGroup(){
//		return "Y".equalsIgnoreCase(this.isLineGroup) ? true : false; 
//	}
	
	public void setIsAutoAccept(Boolean isAutoAccept) {
		this.isAutoAccept = isAutoAccept ? "Y" : "N";
	}
	
	public Boolean getIsAutoAccept(){
		return "Y".equalsIgnoreCase(this.isAutoAccept) ? true : false; 
	}

	public Long getParentLocationRrn() {
		return parentLocationRrn;
	}

	public void setParentLocationRrn(Long parentLocationRrn) {
		this.parentLocationRrn = parentLocationRrn;
	}
}
