package com.glory.mes.msg.model;

import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldDateAdapter;
import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "PRDSPEC")
@XmlAccessorType(XmlAccessType.NONE)
public class XPrdSpec extends XBase{

	@XmlElement(name = "OBJECTRRN")
	protected Long objectRrn;
	
	@XmlElement(name = "NAME")
	private String name;
	
	@XmlElement(name = "DESCRIPTION")
	private String description;
	
	@XmlElement(name = "VERSION")
	private Long version;
	
	@XmlElement(name = "STATUS")
	private String status;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name = "ACTIVE_TIME")
	private Date activeTime;
	
	@XmlElement(name = "ACTIVE_USER")
	private String activeUser;
	
	@XmlElementWrapper(name = "PRDSPECVALUELIST")
	@XmlElementRef
	private List<XPrdSpecValue> xPrdSpecValues;
	
	public Long getObjectRrn() {
		return this.objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Date getActiveTime() {
		return activeTime;
	}

	public void setActiveTime(Date activeTime) {
		this.activeTime = activeTime;
	}

	public String getActiveUser() {
		return activeUser;
	}

	public void setActiveUser(String activeUser) {
		this.activeUser = activeUser;
	}

	public List<XPrdSpecValue> getxPrdSpecValues() {
		return xPrdSpecValues;
	}

	public void setxPrdSpecValues(List<XPrdSpecValue> xPrdSpecValues) {
		this.xPrdSpecValues = xPrdSpecValues;
	}
	
}
