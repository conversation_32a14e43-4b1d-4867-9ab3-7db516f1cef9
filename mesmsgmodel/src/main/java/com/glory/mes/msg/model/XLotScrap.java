package com.glory.mes.msg.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldDateAdapter;
import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "LOTSCRAP")
@XmlAccessorType(XmlAccessType.NONE)
public class XLotScrap extends XBase {

	public static final String HOLDCODESUBLOTHOLD = "SubLotHold";
	
	@XmlElement(name="OBJECTRRN")
	private Long objectRrn;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="CREATED")
	protected Date created;
	
	@XmlElement(name="CREATEDBY")
	protected String createdBy;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="UPDATED")
	protected Date updated;
	
	@XmlElement(name="UPDATEDBY")
	protected String updatedBy;
	
	@XmlElement(name="LOTRRN")
	private Long lotRrn;
	
	@XmlElement(name="LOTID")
	private String lotId;
	
	@XmlElement(name="STEPNAME")
	private String stepName;
	
	@XmlElement(name="STEPVERSION")
	private Long stepVersion;
	
	@XmlElement(name="STEPRRN")
	private Long stepRrn;
	
	@XmlElement(name="PARTNAME")
	private String partName;
	
	@XmlElement(name="PARTVERSION")
	private Long partVersion;
	
	@XmlElement(name="EQUIPMENTID")
	private String equipmentId;

	@XmlElement(name="COMPONENTRRN")
	private Long componentRrn;
	
	@XmlElement(name="COMPONENTID")
	private String componentId;
	
	@XmlElement(name="MAINQTY")
	private BigDecimal mainQty;
	
	@XmlElement(name="SUBQTY")
	private BigDecimal subQty;
	
	@XmlElement(name="STATE")
	private String state;
	
	@XmlElement(name="SUBSTATE")
	private String subState;
	
	@XmlElement(name="ACTIONCODE")
	private String actionCode;
	
	@XmlElement(name="ACTIONREASON")
	private String actionReason;

	@XmlElement(name="ACTIONCOMMENT")
	private String actionComemnt;

	@XmlElement(name="ATTRIBUTE1")
	private Object attribute1;
	
	@XmlElement(name="ATTRIBUTE2")
	private Object attribute2;

	@XmlElement(name="ATTRIBUTE3")
	private Object attribute3;
	
	@XmlElement(name="ATTRIBUTE4")
	private Object attribute4;
	
	@XmlElement(name="ATTRIBUTE5")
	private Object attribute5;
	
	/**
	 * 描述：主键，映射为：OBJECTRRN；
	 * @return objectRrn
	 */
	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	/**
	 * 描述：工步名称，映射为：STEPNAME；
	 * @return stepName
	 */
	public String getStepName() {
		return stepName;
	}

	public void setStepName(String stepName) {
		this.stepName = stepName;
	}

	/**
	 * 描述：工步版本，映射为：STEPVERSION；
	 * @return stepVersion
	 */
	public Long getStepVersion() {
		return stepVersion;
	}

	public void setStepVersion(Long stepVersion) {
		this.stepVersion = stepVersion;
	}

	/**
	 * 描述：工步主键，映射为：STEPRRN；
	 * @return stepRrn
	 */
	public Long getStepRrn() {
		return stepRrn;
	}

	public void setStepRrn(Long stepRrn) {
		this.stepRrn = stepRrn;
	}

	/**
	 * 描述：设备号，映射为：EQUIPMENTID；
	 * @return equipmentId
	 */
	public String getEquipmentId() {
		return equipmentId;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	/**
	 * 描述：组件主键，映射为：COMPONENTRRN；
	 * @return componentRrn
	 */
	public Long getComponentRrn() {
		return componentRrn;
	}

	public void setComponentRrn(Long componentRrn) {
		this.componentRrn = componentRrn;
	}

	/**
	 * 描述：组件号，映射为：COMPONENTID；
	 * @return componentId
	 */
	public String getComponentId() {
		return componentId;
	}

	public void setComponentId(String componentId) {
		this.componentId = componentId;
	}

	/**
	 * 描述：数量，映射为：MAINQTY；
	 * @return mainQty
	 */
	public BigDecimal getMainQty() {
		return mainQty;
	}

	public void setMainQty(BigDecimal mainQty) {
		this.mainQty = mainQty;
	}

	/**
	 * 描述：子数量，映射为：SUBQTY；
	 * @return subQty
	 */
	public BigDecimal getSubQty() {
		return subQty;
	}

	public void setSubQty(BigDecimal subQty) {
		this.subQty = subQty;
	}

	/**
	 * 描述：状态，映射为：STATE；
	 * @return state
	 */
	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	/**
	 * 描述：子状态，映射为：SUBSTATE；
	 * @return subState
	 */
	public String getSubState() {
		return subState;
	}

	public void setSubState(String subState) {
		this.subState = subState;
	}

	/**
	 * 描述：动作码，映射为：ACTIONCODE；
	 * @return actionCode
	 */
	public String getActionCode() {
		return actionCode;
	}

	public void setActionCode(String actionCode) {
		this.actionCode = actionCode;
	}

	/**
	 * 描述：动作原因，映射为：ACTIONREASON；
	 * @return actionReason
	 */
	public String getActionReason() {
		return actionReason;
	}

	public void setActionReason(String actionReason) {
		this.actionReason = actionReason;
	}

	/**
	 * 描述：动作备注，映射为：ACTIONCOMMENT；
	 * @return actionComemnt
	 */
	public String getActionComemnt() {
		return actionComemnt;
	}

	public void setActionComemnt(String actionComemnt) {
		this.actionComemnt = actionComemnt;
	}

	public String getPartName() {
		return partName;
	}

	public void setPartName(String partName) {
		this.partName = partName;
	}

	public Long getPartVersion() {
		return partVersion;
	}

	public void setPartVersion(Long partVersion) {
		this.partVersion = partVersion;
	}

	public Object getAttribute1() {
		return attribute1;
	}

	public void setAttribute1(Object attribute1) {
		this.attribute1 = attribute1;
	}

	public Object getAttribute2() {
		return attribute2;
	}

	public void setAttribute2(Object attribute2) {
		this.attribute2 = attribute2;
	}

	public Object getAttribute3() {
		return attribute3;
	}

	public void setAttribute3(Object attribute3) {
		this.attribute3 = attribute3;
	}

	public Object getAttribute4() {
		return attribute4;
	}

	public void setAttribute4(Object attribute4) {
		this.attribute4 = attribute4;
	}

	public Object getAttribute5() {
		return attribute5;
	}

	public void setAttribute5(Object attribute5) {
		this.attribute5 = attribute5;
	}

	public Long getLotRrn() {
		return lotRrn;
	}

	public void setLotRrn(Long lotRrn) {
		this.lotRrn = lotRrn;
	}

	public String getLotId() {
		return lotId;
	}

	public void setLotId(String lotId) {
		this.lotId = lotId;
	}

	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public Date getUpdated() {
		return updated;
	}

	public void setUpdated(Date updated) {
		this.updated = updated;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

}
