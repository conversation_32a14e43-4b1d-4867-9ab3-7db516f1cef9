package com.glory.mes.msg.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldDateAdapter;
import com.glory.framework.core.xml.XBase;

@XmlRootElement(name="LOTHIS")
@XmlAccessorType(XmlAccessType.NONE)
public class XLotHis extends XBase {
	
	@XmlElement(name = "OBJECTRRN")
	protected Long objectRrn;
	
	@XmlElement(name="LOTID")
	private String lotId;
	
	@XmlElement(name="LOTTYPE")
	private String lotType;
	
	@XmlElement(name="LOTALIAS")
	private String lotAlias;
	
	@XmlElement(name="PARTNAME")
	private String partName;
	
	@XmlElement(name="PARTVERSION")
	private Long partVersion;

	@XmlElement(name="PARTDESC")
	private String partDesc;
	
	@XmlElement(name="PARTTYPE")
	private String partType;

	@XmlElement(name="MAINMATTYPE")
	private String mainMatType;
	
	@XmlElement(name="SUBMATTYPE")
	private String subMatType;
	
	@XmlElement(name="CUSTOMERCODE")
	private String customerCode;
	
	@XmlElement(name="CUSTOMERORDER")
	private String customerOrder;
	
	@XmlElement(name="CUSTOMERPARTID")
	private String customerPartId;
	
	@XmlElement(name="CUSTOMERLOTID")
	private String customerLotId;
	
	@XmlElement(name="PRIORITY")
	private Long priority;

	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="PLANSTARTDATE")
	private Date planStartDate;

	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="PLANENDDATE")
	private Date planEndDate;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="REQUIREDATE")
	private Date requireDate;

	@XmlElement(name="GRADE1")
	private String grade1;

	@XmlElement(name="GRADE2")
	private String grade2;

	@XmlElement(name="WOID")
	private String woId;

	/**
	 * 当前仓库
	 */
	@XmlElement(name="WAREHOUSEID")
	private String warehouseId;
	
	/**
	 * 当前库位
	 */
	@XmlElement(name="LOCATORID")
	private String locatorId;
	
	@XmlElement(name="LOCATION")
	private String location;

	@XmlElement(name="LINEID")
	private String lineId;
	
	@XmlElement(name="TEAMID")
	private String teamId;
    
	@XmlElement(name="STAGEID")
	private String stageId;
	
	@XmlElement(name="DURABLEID")
	private String durable;
	
	@XmlElement(name="OWNER")
	private String owner;
	
	@XmlElement(name="LOTCOMMENT")
	private String lotComment;
	
	@XmlElement(name="TRANSMAINQTY")
	private BigDecimal transMainQty;
	
	@XmlElement(name="TRANSSUBQTY")
	private BigDecimal transSubQty;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="QUEUETIME")
	private Date queueTime;

	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="TRACKINTIME")
	private Date trackInTime;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="TRACKOUTTIME")
	private Date trackOutTime;
	
	@XmlElement(name="COMCLASS")
	private String comClass;

	@XmlElement(name="STATE")
	private String state;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="STATEENTRYTIME")
	private Date stateEntryTime;
	
	@XmlElement(name="PROCESSNAME")
	private String processName;
	
	@XmlElement(name="PROCESSVERSION")
	private Long processVersion;
	
	@XmlElement(name="PROCEDURENAME")
	private String procedureName;
	
	@XmlElement(name="PROCEDUREVERSION")
	private Long procedureVersion;
	
	@XmlElement(name="STEPNAME")
	private String stepName;
	
	@XmlElement(name="STEPVERSION")
	private Long stepVersion;
	
	@XmlElement(name="STEPDESC")
	private String stepDesc;
	
	@XmlElement(name="TRANSFERSTATE")
	private String transferState;
	
	@XmlElement(name="BATCHID")
	private String batchId;
	
	/**
	 * 批次当前返工层次(进入返工流程时+1,返工流程完成时-1)
	 */
	@XmlElement(name="REWORKSTACKCOUNT")
	private Long reworkStackCount;
	
	@XmlElement(name="REWORKCOUNT")
	private Long reworkCount;

	/**
	 * 批次当前设备菜单
	 */
	@XmlElement(name="RECIPENAME")
	private String recipeName;
	
	@XmlElement(name="RECIPEVERSION")
	private Long recipeVersion;
	
	@XmlElement(name="MASK")
	private String mask;
	
	@XmlElement(name="ITEMSETNAME")
	private String itemSetName;
	
	@XmlElement(name="ITEMSETVERSION")
	private Long itemSetVersion;
	
	@XmlElement(name="ACTIONCODE")
	private String actionCode;
	
	@XmlElement(name="ACTIONREASON")
	private String actionReason;
	
	@XmlElement(name="ACTIONCOMMENT")
	private String actionComment;
	
	@XmlElement(name="OCAPID")
	private String ocapId;
	
	@XmlElement(name="HISCOMMENT")
	private String hisComment;
	
	@XmlElement(name="UPDATEDBY")
	private String updatedBy;

	@XmlElement(name="TRANSTYPE")
	private String transType;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="TRANSTIME")
	private Date transTime;

	@XmlElement(name="HISSEQ")
	private String hisSeq;
	
	@XmlElement(name="HISSEQNO")
	private Long hisSeqNo;
		
	@XmlElement(name="EQUIPMENTID")
	private String equipmentId;
	
	@XmlElement(name="MAINQTY")
	private BigDecimal mainQty;
	
	@XmlElement(name="SUBQTY")
	private BigDecimal subQty;

	@XmlElement(name="OPERATOR1")
	private String operator1;

	@XmlElement(name="OPERATOR2")
	private String operator2;

	@XmlElement(name="PARENTUNITRRN")
	private Long parentUnitRrn;

	@XmlElement(name="SUBUNITTYPE")
	protected String subUnitType;

	public Long getObjectRrn() {
		return objectRrn;
	}

	public void setObjectRrn(Long objectRrn) {
		this.objectRrn = objectRrn;
	}

	public String getLotId() {
		return lotId;
	}

	public void setLotId(String lotId) {
		this.lotId = lotId;
	}

	public String getLotType() {
		return lotType;
	}

	public void setLotType(String lotType) {
		this.lotType = lotType;
	}

	public String getLotAlias() {
		return lotAlias;
	}

	public void setLotAlias(String lotAlias) {
		this.lotAlias = lotAlias;
	}

	public String getPartName() {
		return partName;
	}

	public void setPartName(String partName) {
		this.partName = partName;
	}

	public Long getPartVersion() {
		return partVersion;
	}

	public void setPartVersion(Long partVersion) {
		this.partVersion = partVersion;
	}

	public String getPartDesc() {
		return partDesc;
	}

	public void setPartDesc(String partDesc) {
		this.partDesc = partDesc;
	}

	public String getPartType() {
		return partType;
	}

	public void setPartType(String partType) {
		this.partType = partType;
	}

	public String getMainMatType() {
		return mainMatType;
	}

	public void setMainMatType(String mainMatType) {
		this.mainMatType = mainMatType;
	}

	public String getSubMatType() {
		return subMatType;
	}

	public void setSubMatType(String subMatType) {
		this.subMatType = subMatType;
	}

	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	public String getCustomerOrder() {
		return customerOrder;
	}

	public void setCustomerOrder(String customerOrder) {
		this.customerOrder = customerOrder;
	}

	public String getCustomerPartId() {
		return customerPartId;
	}

	public void setCustomerPartId(String customerPartId) {
		this.customerPartId = customerPartId;
	}

	public String getCustomerLotId() {
		return customerLotId;
	}

	public void setCustomerLotId(String customerLotId) {
		this.customerLotId = customerLotId;
	}

	public Long getPriority() {
		return priority;
	}

	public void setPriority(Long priority) {
		this.priority = priority;
	}

	public Date getPlanStartDate() {
		return planStartDate;
	}

	public void setPlanStartDate(Date planStartDate) {
		this.planStartDate = planStartDate;
	}

	public Date getPlanEndDate() {
		return planEndDate;
	}

	public void setPlanEndDate(Date planEndDate) {
		this.planEndDate = planEndDate;
	}

	public Date getRequireDate() {
		return requireDate;
	}

	public void setRequireDate(Date requireDate) {
		this.requireDate = requireDate;
	}

	public String getGrade1() {
		return grade1;
	}

	public void setGrade1(String grade1) {
		this.grade1 = grade1;
	}

	public String getGrade2() {
		return grade2;
	}

	public void setGrade2(String grade2) {
		this.grade2 = grade2;
	}

	public void setOwner(String owner) {
		this.owner = owner;
	}

	public String getOwner() {
		return owner;
	}
	
	public String getLotComment() {
		return lotComment;
	}

	public void setLotComment(String lotComment) {
		this.lotComment = lotComment;
	}

	public String getWoId() {
		return woId;
	}

	public void setWoId(String woId) {
		this.woId = woId;
	}
	
	public void setLineId(String lineId) {
		this.lineId = lineId;
	}

	public String getLineId() {
		return lineId;
	}
	
	public void setTeamId(String teamId) {
		this.teamId = teamId;
	}

	public String getTeamId() {
		return teamId;
	}
	
	public String getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(String warehouseId) {
		this.warehouseId = warehouseId;
	}

	public String getLocatorId() {
		return locatorId;
	}

	public void setLocatorId(String locatorId) {
		this.locatorId = locatorId;
	}
	
	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public String getStageId() {
		return stageId;
	}

	public void setStageId(String stageId) {
		this.stageId = stageId;
	}

	public String getDurable() {
		return durable;
	}

	public void setDurable(String durable) {
		this.durable = durable;
	}

	public BigDecimal getTransMainQty() {
		return transMainQty;
	}

	public void setTransMainQty(BigDecimal transMainQty) {
		this.transMainQty = transMainQty;
	}

	public BigDecimal getTransSubQty() {
		return transSubQty;
	}

	public void setTransSubQty(BigDecimal transSubQty) {
		this.transSubQty = transSubQty;
	}
	
	public Date getQueueTime() {
		return queueTime;
	}

	public void setQueueTime(Date queueTime) {
		this.queueTime = queueTime;
	}

	public Date getTrackInTime() {
		return trackInTime;
	}

	public void setTrackInTime(Date trackInTime) {
		this.trackInTime = trackInTime;
	}

	public Date getTrackOutTime() {
		return trackOutTime;
	}

	public void setTrackOutTime(Date trackOutTime) {
		this.trackOutTime = trackOutTime;
	}
	
	public String getComClass() {
		return comClass;
	}

	public void setComClass(String comClass) {
		this.comClass = comClass;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public Date getStateEntryTime() {
		return stateEntryTime;
	}

	public void setStateEntryTime(Date stateEntryTime) {
		this.stateEntryTime = stateEntryTime;
	}

	public void setProcessName(String processName) {
		this.processName = processName;
	}

	public String getProcessName() {
		return processName;
	}

	public void setProcessVersion(Long processVersion) {
		this.processVersion = processVersion;
	}

	public Long getProcessVersion() {
		return processVersion;
	}

	public String getProcedureName() {
		return procedureName;
	}

	public void setProcedureName(String procedureName) {
		this.procedureName = procedureName;
	}

	public Long getProcedureVersion() {
		return procedureVersion;
	}

	public void setProcedureVersion(Long procedureVersion) {
		this.procedureVersion = procedureVersion;
	}

	public String getStepName() {
		return stepName;
	}

	public void setStepName(String stepName) {
		this.stepName = stepName;
	}

	public Long getStepVersion() {
		return stepVersion;
	}

	public void setStepVersion(Long stepVersion) {
		this.stepVersion = stepVersion;
	}

	public void setStepDesc(String stepDesc) {
		this.stepDesc = stepDesc;
	}

	public String getStepDesc() {
		return stepDesc;
	}
	
	public String getTransferState() {
		return transferState;
	}

	public void setTransferState(String transferState) {
		this.transferState = transferState;
	}
	
	public String getBatchId() {
		return batchId;
	}

	public void setBatchId(String batchId) {
		this.batchId = batchId;
	}
	
	public Long getReworkStackCount() {
		return reworkStackCount;
	}

	public void setReworkStackCount(Long reworkStackCount) {
		this.reworkStackCount = reworkStackCount;
	}
	
	public Long getReworkCount() {
		return reworkCount;
	}

	public void setReworkCount(Long reworkCount) {
		this.reworkCount = reworkCount;
	}
	
	public String getRecipeName() {
		return recipeName;
	}
	
	public void setRecipeName(String recipeName) {
		this.recipeName = recipeName;
	}

	public Long getRecipeVersion() {
		return recipeVersion;
	}

	public void setRecipeVersion(Long recipeVersion) {
		this.recipeVersion = recipeVersion;
	}

	public void setMask(String mask) {
		this.mask = mask;
	}

	public String getMask() {
		return mask;
	}
	
	public String getItemSetName() {
		return itemSetName;
	}

	public void setItemSetName(String itemSetName) {
		this.itemSetName = itemSetName;
	}

	public Long getItemSetVersion() {
		return itemSetVersion;
	}

	public void setItemSetVersion(Long itemSetVersion) {
		this.itemSetVersion = itemSetVersion;
	}

	public String getActionCode() {
		return actionCode;
	}

	public void setActionCode(String actionCode) {
		this.actionCode = actionCode;
	}

	public String getActionReason() {
		return actionReason;
	}

	public void setActionReason(String actionReason) {
		this.actionReason = actionReason;
	}

	public String getActionComment() {
		return actionComment;
	}

	public void setActionComment(String actionComment) {
		this.actionComment = actionComment;
	}

	public void setOcapId(String ocapId) {
		this.ocapId = ocapId;
	}

	public String getOcapId() {
		return ocapId;
	}
	
	public String getHisComment() {
		return hisComment;
	}

	public void setHisComment(String hisComment) {
		this.hisComment = hisComment;
	}
	
	public String getPartId() {
		return partName == null ? "" : partName + "." + partVersion;
	}
	
	public String getProcessId() {
		return processName == null ? "" : processName + "." + processVersion;
	}
	
	public String getProcedureId() {
		return procedureName == null ? "" : procedureName + "." + procedureVersion;
	}

	public String getStepId() {
		return stepName == null ? "" : stepName + "." + stepVersion;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	
	public String getTransType() {
		return transType;
	}

	public void setTransType(String transType) {
		this.transType = transType;
	}
	
	public Date getTransTime() {
		return transTime;
	}

	public void setTransTime(Date transTime) {
		this.transTime = transTime;
	}
	
	public void setHisSeq(String hisSeq) {
		this.hisSeq = hisSeq;
	}

	public String getHisSeq() {
		return hisSeq;
	}

	public void setHisSeqNo(Long hisSeqNo) {
		this.hisSeqNo = hisSeqNo;
	}

	public Long getHisSeqNo() {
		return hisSeqNo;
	}
	
	public void setSubUnitType(String subUnitType) {
		this.subUnitType = subUnitType;
	}

	public String getSubUnitType() {
		return subUnitType;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	public String getEquipmentId() {
		return equipmentId;
	}

	public void setMainQty(BigDecimal mainQty) {
		this.mainQty = mainQty;
	}

	public BigDecimal getMainQty() {
		return mainQty;
	}

	public void setSubQty(BigDecimal subQty) {
		this.subQty = subQty;
	}

	public BigDecimal getSubQty() {
		return subQty;
	}

	public String getOperator1() {
		return operator1;
	}

	public void setOperator1(String operator1) {
		this.operator1 = operator1;
	}

	public String getOperator2() {
		return operator2;
	}

	public void setOperator2(String operator2) {
		this.operator2 = operator2;
	}
	
	public void setParentUnitRrn(Long parentUnitRrn) {
		this.parentUnitRrn = parentUnitRrn;
	}

	public Long getParentUnitRrn() {
		return parentUnitRrn;
	}
	
}
