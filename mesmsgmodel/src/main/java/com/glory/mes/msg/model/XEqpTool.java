package com.glory.mes.msg.model;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "EQPTOOL")
@XmlAccessorType(XmlAccessType.NONE)
public class XEqpTool extends XBase {

	@XmlElement(name="EQUIPMENTID")
	private String equipmentId;
	
	@XmlElement(name="TOOLID")
	private String toolId;
	
	@XmlElement(name="MATERIALNAME")
    private String materialName;
    
	@XmlElement(name="MATERIALDESC")
    private String materialDesc;
    
	@XmlElement(name="MATERIALTYPE")
    private String materialType;
	
	@XmlElement(name="MAINQTY")
	private BigDecimal mainQty;
	
	@XmlElement(name="SUBQTY")
	private BigDecimal subQty;

	@XmlElement(name="CONSUMABLEID")
	private String consumableId;
	
	@XmlElement(name="POSITIONNAME")
	private String positionName;
	
	public String getEquipmentId() {
		return equipmentId;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	public String getToolId() {
		return toolId;
	}

	public void setToolId(String toolId) {
		this.toolId = toolId;
	}

	public String getMaterialName() {
		return materialName;
	}

	public void setMaterialName(String materialName) {
		this.materialName = materialName;
	}

	public String getMaterialDesc() {
		return materialDesc;
	}

	public void setMaterialDesc(String materialDesc) {
		this.materialDesc = materialDesc;
	}

	public String getMaterialType() {
		return materialType;
	}

	public void setMaterialType(String materialType) {
		this.materialType = materialType;
	}

	public BigDecimal getMainQty() {
		return mainQty;
	}

	public void setMainQty(BigDecimal mainQty) {
		this.mainQty = mainQty;
	}

	public BigDecimal getSubQty() {
		return subQty;
	}

	public void setSubQty(BigDecimal subQty) {
		this.subQty = subQty;
	}

	public String getConsumableId() {
		return consumableId;
	}

	public void setConsumableId(String consumableId) {
		this.consumableId = consumableId;
	}

	public String getPositionName() {
		return positionName;
	}

	public void setPositionName(String positionName) {
		this.positionName = positionName;
	}

}
