package com.glory.mes.msg.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldDateAdapter;
import com.glory.framework.core.xml.XDynamicComponent;

@XmlRootElement(name = "CARRIER")
@XmlAccessorType(XmlAccessType.NONE)
public class XCarrier extends XDynamicComponent {
	
	private static final long serialVersionUID = 1L;

	public static final String ACTION_TYPE_TRANSPORT_CREATE_COMPLETE = "TransportCreateComplete";
	public static final String ACTION_TYPE_TRANSPORT_ALREADY_CREATE = "TransportAlreadyCreate";
	public static final String ACTION_TYPE_TRANSPORT_CHANGE_DESTINATION = "TransportChangeDestination";
	public static final String ACTION_TYPE_TRANSPORT_DELETE_CANCEL = "TransportDeleteCancel";

	@XmlElement(name="DURABLEID")
	private String durableId;
	
	@XmlElement(name="DURABLETYPE")
	private String durableType;
	
	@XmlElement(name="STATE")
	private String state;
	
	@XmlElement(name="DURABLESPECNAME")
	private String durableSpecName;
	
	@XmlElement(name="DURABLESPECVERSION")
	private Long durableSpecVersion;

	@XmlElement(name="HOLDSTATE")
	private String holdState;
	
	@XmlElement(name="CLEANSTATE")
	private String cleanState;
	
	@XmlElement(name="ACTIONCODE")
	private String actionCode;
	
	@XmlElement(name="ACTIONCOMMENT")
	private String actionComment;
	
	@XmlElement(name="ACTIONREASON")
	private String actionReason;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="UPDATED")
	private Date updated;
	
	@XmlElement(name="UPDATEDBY")
	private String updatedBy;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="CREATED")
	private Date created;
	
	@XmlElement(name="CREATEDBY")
	private String createdBy;
	
	@XmlElement(name="TRANSFERSTATE")
	private String transferState;
	
	@XmlElement(name="COMMENTS")
	private String comments;

	@XmlElement(name="ISGLOBAL")
	private String isGlobal;
	
	/*
	 * 新增2014-11-05
	 */
	@XmlElement(name="MAINMATTYPE")
	private String mainMatType;
	
	@XmlElement(name="CAPACITY")
	private BigDecimal capacity;
	
	@XmlElement(name="SUBMATTYPE")
	private String subMatType;
	
	@XmlElement(name="WARNINGCOUNT")
	private Long warningCount;
	
	@XmlElement(name="WARNINGTIME")
	private Long warningTime;
	
	@XmlElement(name="TIMEUNIT")
	private String timeUnit;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="CLEANDATE")
	private Date cleanDate;
	
	@XmlJavaTypeAdapter(FieldDateAdapter.class)
	@XmlElement(name="ARRIVALDATE")
	private Date arrivalDate;
	
	@XmlElement(name="PORTID")
	private String portId;
	
	@XmlElement(name="CATEGORY")
	private String category;
	
	@XmlElement(name="CURRENTCOUNT")
    private Long currentCount;
	
	@XmlElement(name="CURRENTQTY")
	private BigDecimal currentQty;
	/*
	 * 结束
	 */
	
	@XmlElement(name="LOTID")
	private String lotId;
	
	/*
	 * MCS接口使用
	 */
	
	@XmlElement(name="PRODUCTNAME")
	private String productName;
	
	@XmlElement(name="PRODUCTQTY")
	private BigDecimal productQty;

	@XmlElement(name="CURPOSITIONTYPE")
	private String curPositionType;
	
	@XmlElement(name="CURMACHINENAME")
	private String curMachineName;
	
	@XmlElement(name="CURPOSITIONNAME")
	private String curPositionName;
	
	@XmlElement(name="CURZONENAME")
	private String curZoneName;
	
	@XmlElement(name="CARRIERSTATE")
	private String carrierState;
	
	/*
	 * MCS接口使用
	 */
	@XmlElement(name="WAREHOUSEID")
	private String warehouseId;
	
	@XmlElement(name="LOCATORID")
	private String locatorId;
	
	@XmlElement(name="EQUIPMENTID")
	private String equipmentId;
	
	@XmlElement(name="LOTTYPE")
	private String lotType;
	
	@XmlElement(name="PARTNAME")
	private String partName;
	
	@XmlElement(name="MAINQTY")
	private String mainQty;
	
	@XmlElement(name="STEPNAME")
	private String stepName;
	
	@XmlElement(name="IS_CHANGE_ORG")
	private String isChangeOrg;
	
	@XmlElement(name="ORGRRN")
	private Long orgRrn;

	/**
	 * 是否自动派工
	 */
	@XmlElement(name="ISDISPATCH")
	private String isDispatch;
	
	public String getDurableId() {
		return durableId;
	}

	public BigDecimal getCapacity() {
		return capacity;
	}

	public void setCapacity(BigDecimal capacity) {
		this.capacity = capacity;
	}

	public void setDurableId(String durableId) {
		this.durableId = durableId;
	}

	public String getDurableType() {
		return durableType;
	}

	public void setDurableType(String durableType) {
		this.durableType = durableType;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public Long getDurableSpecVersion() {
		return durableSpecVersion;
	}

	public void setDurableSpecVersion(Long durableSpecVersion) {
		this.durableSpecVersion = durableSpecVersion;
	}

	public String getDurableSpecName() {
		return durableSpecName;
	}

	public void setDurableSpecName(String durableSpecName) {
		this.durableSpecName = durableSpecName;
	}

	public String getHoldState() {
		return holdState;
	}

	public void setHoldState(String holdState) {
		this.holdState = holdState;
	}

	public String getCleanState() {
		return cleanState;
	}

	public void setCleanState(String cleanState) {
		this.cleanState = cleanState;
	}

	public String getActionCode() {
		return actionCode;
	}

	public void setActionCode(String actionCode) {
		this.actionCode = actionCode;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getActionComment() {
		return actionComment;
	}

	public void setActionComment(String actionComment) {
		this.actionComment = actionComment;
	}

	public String getActionReason() {
		return actionReason;
	}

	public void setActionReason(String actionReason) {
		this.actionReason = actionReason;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public Date getUpdated() {
		return updated;
	}

	public void setUpdated(Date updated) {
		this.updated = updated;
	}

	public String getTransferState() {
		return transferState;
	}

	public void setTransferState(String transferState) {
		this.transferState = transferState;
	}

	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}
	
	public Boolean getIsGlobal() {
		return "Y".equalsIgnoreCase(this.isGlobal) ? true : false;
	}

	public void setIsGlobal(Boolean isGlobal) {
		this.isGlobal = isGlobal ? "Y" : "N";
	}

	public String getMainMatType() {
		return mainMatType;
	}

	public void setMainMatType(String mainMatType) {
		this.mainMatType = mainMatType;
	}

	public String getSubMatType() {
		return subMatType;
	}

	public void setSubMatType(String subMatType) {
		this.subMatType = subMatType;
	}

	public Long getWarningCount() {
		return warningCount;
	}

	public void setWarningCount(Long warningCount) {
		this.warningCount = warningCount;
	}

	public Long getWarningTime() {
		return warningTime;
	}

	public void setWarningTime(Long warningTime) {
		this.warningTime = warningTime;
	}

	public String getTimeUnit() {
		return timeUnit;
	}

	public void setTimeUnit(String timeUnit) {
		this.timeUnit = timeUnit;
	}

	public Date getCleanDate() {
		return cleanDate;
	}

	public void setCleanDate(Date cleanDate) {
		this.cleanDate = cleanDate;
	}

	public Date getArrivalDate() {
		return arrivalDate;
	}

	public void setArrivalDate(Date arrivalDate) {
		this.arrivalDate = arrivalDate;
	}

	public String getPortId() {
		return portId;
	}

	public void setPortId(String portId) {
		this.portId = portId;
	}

	public void setIsGlobal(String isGlobal) {
		this.isGlobal = isGlobal;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public Long getCurrentCount() {
		return currentCount;
	}

	public void setCurrentCount(Long currentCount) {
		this.currentCount = currentCount;
	}

	public BigDecimal getCurrentQty() {
		return currentQty;
	}

	public void setCurrentQty(BigDecimal currentQty) {
		this.currentQty = currentQty;
	}

	public String getLotId() {
		return lotId;
	}

	public void setLotId(String lotId) {
		this.lotId = lotId;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public BigDecimal getProductQty() {
		return productQty;
	}

	public void setProductQty(BigDecimal productQty) {
		this.productQty = productQty;
	}

	public String getCurPositionType() {
		return curPositionType;
	}

	public void setCurPositionType(String curPositionType) {
		this.curPositionType = curPositionType;
	}

	public String getCurPositionName() {
		return curPositionName;
	}

	public void setCurPositionName(String curPositionName) {
		this.curPositionName = curPositionName;
	}

	public String getCurZoneName() {
		return curZoneName;
	}

	public void setCurZoneName(String curZoneName) {
		this.curZoneName = curZoneName;
	}

	public String getCarrierState() {
		return carrierState;
	}

	public void setCarrierState(String carrierState) {
		this.carrierState = carrierState;
	}

	public String getCurMachineName() {
		return curMachineName;
	}

	public void setCurMachineName(String curMachineName) {
		this.curMachineName = curMachineName;
	}

	public String getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(String warehouseId) {
		this.warehouseId = warehouseId;
	}

	public String getLocatorId() {
		return locatorId;
	}

	public void setLocatorId(String locatorId) {
		this.locatorId = locatorId;
	}

	public String getEquipmentId() {
		return equipmentId;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	public String getLotType() {
		return lotType;
	}

	public void setLotType(String lotType) {
		this.lotType = lotType;
	}

	public String getPartName() {
		return partName;
	}

	public void setPartName(String partName) {
		this.partName = partName;
	}

	public String getMainQty() {
		return mainQty;
	}

	public void setMainQty(String mainQty) {
		this.mainQty = mainQty;
	}

	public String getStepName() {
		return stepName;
	}

	public void setStepName(String stepName) {
		this.stepName = stepName;
	}

	public String getIsChangeOrg() {
		return isChangeOrg;
	}

	public void setIsChangeOrg(String isChangeOrg) {
		this.isChangeOrg = isChangeOrg;
	}

	public Long getOrgRrn() {
		return orgRrn;
	}

	public void setOrgRrn(Long orgRrn) {
		this.orgRrn = orgRrn;
	}

	public Boolean getIsDispatch() {
		return "Y".equalsIgnoreCase(this.isDispatch);
	}

	public void setIsDispatch(Boolean isDispatch) {
		if (isDispatch != null) {
			this.isDispatch = isDispatch ? "Y" : "N";
		} else {
			this.isDispatch = "N";
		}
	}

}
