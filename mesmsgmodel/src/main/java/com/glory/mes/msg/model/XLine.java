package com.glory.mes.msg.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "LINE")
@XmlAccessorType(XmlAccessType.NONE)
public class XLine extends XBase {

	private static final long serialVersionUID = 1L;
	
	@XmlElement(name="NAME")
	private String name;
	
	@XmlElement(name="DESCRIPTION")
	private String description;

	//车间
	@XmlElement(name="SHOPID")
	private String shopId;
	
	@XmlElement(name = "RESERVED1")
	private String reserved1;
	
	@XmlElement(name = "RESERVED2")
	private String reserved2;
	
	@XmlElement(name = "RESERVED3")
	private String reserved3;
	
	@XmlElement(name = "RESERVED4")
	private String reserved4;
	
	@XmlElement(name = "RESERVED5")
	private String reserved5;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getShopId() {
		return shopId;
	}

	public void setShopId(String shopId) {
		this.shopId = shopId;
	}

	public String getReserved1() {
		return reserved1;
	}

	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}

	public String getReserved2() {
		return reserved2;
	}

	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}

	public String getReserved3() {
		return reserved3;
	}

	public void setReserved3(String reserved3) {
		this.reserved3 = reserved3;
	}

	public String getReserved4() {
		return reserved4;
	}

	public void setReserved4(String reserved4) {
		this.reserved4 = reserved4;
	}

	public String getReserved5() {
		return reserved5;
	}

	public void setReserved5(String reserved5) {
		this.reserved5 = reserved5;
	}
	
}
